import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import apiClient, { PaginatedResponse, PaginationParams } from './api';
import { PermissionGroup } from './permissionGroup';

// 节点数据类型
export interface Node {
  id: number;
  name: string;
  protocol: 'anytls' | 'vmess' | 'vless' | 'trojan' | 'shadowsocks';
  host: string;
  port: number;
  password: string;
  config: any; // JSON配置
  server_name: string;
  status: 'online' | 'offline' | 'maintenance';
  sort_order: number;
  traffic_rate: number;
  max_users: number;
  created_at: string;
  updated_at: string;
  online_users?: number;
  group_ids?: number[];
  groups?: PermissionGroup[];
}

// AnyTLS协议配置
export interface AnyTLSConfig {
  padding?: {
    enabled?: boolean;
    min_length?: number;
    max_length?: number;
    strategy?: 'random' | 'fixed' | 'adaptive';
  };
  tls?: {
    sni?: string;
    alpn?: string[];
    skip_verify?: boolean;
  };
  transport?: {
    type?: 'tcp' | 'ws' | 'grpc';
    path?: string;
    headers?: Record<string, string>;
  };
}

// 节点创建表单类型
export interface NodeForm {
  name: string;
  protocol: 'anytls' | 'vmess' | 'vless' | 'trojan' | 'shadowsocks';
  host: string;
  port: number;
  password: string;
  config: any;
  server_name: string;
  status: 'online' | 'offline' | 'maintenance';
  sort_order: number;
  traffic_rate: number;
  max_users: number;
}

// 节点更新表单类型
export interface NodeUpdateForm {
  name?: string;
  protocol?: 'anytls' | 'vmess' | 'vless' | 'trojan' | 'shadowsocks';
  host?: string;
  port?: number;
  password?: string;
  config?: any;
  server_name?: string;
  status?: 'online' | 'offline' | 'maintenance';
  sort_order?: number;
  traffic_rate?: number;
  max_users?: number;
}

// 节点查询参数
export interface NodeQueryParams extends PaginationParams {
  search?: string;
  protocol?: string;
  status?: string;
  sort_by?: string;
  sort_order?: string;
  group_id?: number;
}

// 节点统计信息
export interface NodeStats {
  id: number;
  name: string;
  status: string;
  online_users: number;
  max_users: number;
  usage_rate: number;
  traffic_rate: number;
  last_check: string;
  response_time: number;
  is_healthy: boolean;
}

// 节点健康检查结果
export interface NodeHealthCheckResult {
  node_id: number;
  node_name: string;
  is_healthy: boolean;
  response_time: number;
  error?: string;
  checked_at: string;
}

// 批量操作请求
export interface NodeBatchOperation {
  node_ids: number[];
  operation: 'start' | 'stop' | 'restart' | 'delete' | 'update_status';
  data?: any;
}

// 协议配置模板
export const ProtocolTemplates = {
  anytls: {
    name: 'AnyTLS',
    icon: '🔒',
    description: '基于TLS的安全代理协议',
    defaultConfig: {
      padding: {
        enabled: true,
        min_length: 100,
        max_length: 900,
        strategy: 'random'
      },
      tls: {
        sni: '',
        alpn: ['h2', 'http/1.1'],
        skip_verify: false
      },
      transport: {
        type: 'tcp',
        path: '/',
        headers: {}
      }
    } as AnyTLSConfig,
    configFields: [
      {
        name: 'padding',
        label: '填充配置',
        type: 'object',
        fields: [
          { name: 'enabled', label: '启用填充', type: 'boolean' },
          { name: 'min_length', label: '最小长度', type: 'number', min: 50, max: 1000 },
          { name: 'max_length', label: '最大长度', type: 'number', min: 100, max: 2000 },
          { name: 'strategy', label: '填充策略', type: 'select', options: [
            { label: '随机', value: 'random' },
            { label: '固定', value: 'fixed' },
            { label: '自适应', value: 'adaptive' }
          ]}
        ]
      },
      {
        name: 'tls',
        label: 'TLS配置',
        type: 'object',
        fields: [
          { name: 'sni', label: 'SNI', type: 'string' },
          { name: 'alpn', label: 'ALPN', type: 'tags' },
          { name: 'skip_verify', label: '跳过验证', type: 'boolean' }
        ]
      },
      {
        name: 'transport',
        label: '传输配置',
        type: 'object',
        fields: [
          { name: 'type', label: '传输类型', type: 'select', options: [
            { label: 'TCP', value: 'tcp' },
            { label: 'WebSocket', value: 'ws' },
            { label: 'gRPC', value: 'grpc' }
          ]},
          { name: 'path', label: '路径', type: 'string' },
          { name: 'headers', label: 'HTTP头', type: 'object' }
        ]
      }
    ]
  },
  vmess: {
    name: 'VMess',
    icon: '⚡',
    description: 'V2Ray VMess 协议',
    defaultConfig: {},
    configFields: []
  },
  vless: {
    name: 'VLESS',
    icon: '🚀',
    description: 'V2Ray VLESS 协议',
    defaultConfig: {},
    configFields: []
  },
  trojan: {
    name: 'Trojan',
    icon: '🐎',
    description: 'Trojan 协议',
    defaultConfig: {},
    configFields: []
  },
  shadowsocks: {
    name: 'Shadowsocks',
    icon: '🥷',
    description: 'Shadowsocks 协议',
    defaultConfig: {},
    configFields: []
  }
};

class NodeService {
  // 获取节点列表
  async getNodes(params: NodeQueryParams): Promise<PaginatedResponse<Node>> {
    // 转换分页参数以匹配后端期望的格式
    const backendParams = {
      ...params,
      page: params.current || 1,
      page_size: params.pageSize || 20,
    };
    // 删除前端的分页参数，避免冲突
    delete backendParams.current;
    delete backendParams.pageSize;

    const response = await apiClient.get('/nodes/admin', backendParams);

    // 转换后端响应格式为前端期望的分页格式
    return {
      list: response.nodes || [],  // 后端返回的是 "nodes" 字段
      total: response.total || 0,
      current: params.current || 1,
      pageSize: params.pageSize || 20
    };
  }

  // 获取所有节点（不分页）
  async getAllNodes(): Promise<Node[]> {
    return apiClient.get('/nodes/admin/all');
  }

  // 获取在线节点
  async getOnlineNodes(): Promise<Node[]> {
    return apiClient.get('/nodes/admin/online');
  }

  // 获取单个节点详情
  async getNode(id: number): Promise<Node> {
    return apiClient.get(`/nodes/admin/${id}`);
  }

  // 创建节点
  async createNode(data: NodeForm): Promise<Node> {
    return apiClient.post('/nodes/admin', data);
  }

  // 更新节点
  async updateNode(id: number, data: NodeUpdateForm): Promise<Node> {
    return apiClient.put(`/nodes/admin/${id}`, data);
  }

  // 删除节点
  async deleteNode(id: number): Promise<void> {
    return apiClient.delete(`/nodes/admin/${id}`);
  }

  // 更新节点状态
  async updateNodeStatus(id: number, status: 'online' | 'offline' | 'maintenance'): Promise<void> {
    return apiClient.put(`/nodes/admin/${id}/status`, { status });
  }

  // 获取节点统计信息
  async getNodeStats(id: number): Promise<NodeStats> {
    return apiClient.get(`/nodes/admin/${id}/stats`);
  }

  // 获取所有节点统计信息
  async getAllNodesStats(): Promise<NodeStats[]> {
    return apiClient.get('/nodes/admin/stats');
  }

  // 节点健康检查
  async healthCheck(id: number): Promise<NodeHealthCheckResult> {
    return apiClient.get(`/nodes/admin/${id}/health`);
  }

  // 批量健康检查
  async batchHealthCheck(nodeIds: number[]): Promise<NodeHealthCheckResult[]> {
    return apiClient.post('/nodes/admin/health-check', { node_ids: nodeIds });
  }

  // 获取节点关联的权限组
  async getNodeGroups(id: number): Promise<PermissionGroup[]> {
    return apiClient.get(`/nodes/admin/${id}/groups`);
  }

  // 获取当前用户可用的节点列表（基于权限组）
  async getUserAvailableNodes(): Promise<Node[]> {
    const response = await apiClient.get('/nodes/available');
    return response.nodes || [];
  }

  // 批量操作节点
  async batchUpdateNodes(operation: NodeBatchOperation): Promise<any> {
    return apiClient.post('/nodes/admin/batch', operation);
  }

  // 批量删除节点
  async batchDeleteNodes(ids: number[]): Promise<void> {
    await this.batchUpdateNodes({
      node_ids: ids,
      operation: 'delete'
    });
  }

  // 获取协议模板
  getProtocolTemplate(protocol: string) {
    return ProtocolTemplates[protocol as keyof typeof ProtocolTemplates] || ProtocolTemplates.anytls;
  }

  // 生成默认配置
  generateDefaultConfig(protocol: string): any {
    const template = this.getProtocolTemplate(protocol);
    return template.defaultConfig;
  }

  // 验证节点配置
  validateNodeConfig(protocol: string, config: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (protocol === 'anytls') {
      const anyConfig = config as AnyTLSConfig;
      
      // 验证填充配置
      if (anyConfig.padding?.enabled) {
        if (!anyConfig.padding.min_length || anyConfig.padding.min_length < 50) {
          errors.push('填充最小长度不能小于50');
        }
        if (!anyConfig.padding.max_length || anyConfig.padding.max_length > 2000) {
          errors.push('填充最大长度不能大于2000');
        }
        if (anyConfig.padding.min_length && anyConfig.padding.max_length && 
            anyConfig.padding.min_length >= anyConfig.padding.max_length) {
          errors.push('填充最小长度必须小于最大长度');
        }
      }
      
      // 验证传输配置
      if (anyConfig.transport?.type === 'ws' && !anyConfig.transport.path) {
        errors.push('WebSocket传输必须指定路径');
      }
    }
    
    return { valid: errors.length === 0, errors };
  }

  // 格式化节点状态
  formatStatus(status: string): { text: string; color: string } {
    const statusMap = {
      online: { text: '在线', color: 'success' },
      offline: { text: '离线', color: 'error' },
      maintenance: { text: '维护', color: 'warning' }
    };
    return statusMap[status as keyof typeof statusMap] || { text: status, color: 'default' };
  }

  // 格式化协议显示
  formatProtocol(protocol: string): { text: string; icon: string } {
    const template = this.getProtocolTemplate(protocol);
    return { text: template.name, icon: template.icon };
  }

  // 计算节点使用率
  calculateUsageRate(onlineUsers: number, maxUsers: number): number {
    if (maxUsers === 0) return 0;
    return Math.min((onlineUsers / maxUsers) * 100, 100);
  }

  // 格式化响应时间
  formatResponseTime(responseTime: number): string {
    if (responseTime < 1000) return `${responseTime}ms`;
    return `${(responseTime / 1000).toFixed(1)}s`;
  }

  // 获取状态颜色
  getStatusColor(status: string): string {
    const colorMap = {
      online: 'success',
      offline: 'error',
      maintenance: 'warning'
    };
    return colorMap[status as keyof typeof colorMap] || 'default';
  }

  // 检查节点是否健康
  isNodeHealthy(node: Node, stats?: NodeStats): boolean {
    if (node.status !== 'online') return false;
    if (stats && !stats.is_healthy) return false;
    return true;
  }

  // 格式化流量倍率
  formatTrafficRate(rate: number): string {
    return `${rate}x`;
  }

  // 获取节点配置摘要
  getConfigSummary(protocol: string, config: any): string {
    if (protocol === 'anytls') {
      const anyConfig = config as AnyTLSConfig;
      const parts = [];
      
      if (anyConfig.padding?.enabled) {
        parts.push(`填充(${anyConfig.padding.min_length}-${anyConfig.padding.max_length})`);
      }
      
      if (anyConfig.transport?.type && anyConfig.transport.type !== 'tcp') {
        parts.push(`传输(${anyConfig.transport.type.toUpperCase()})`);
      }
      
      if (anyConfig.tls?.sni) {
        parts.push(`SNI(${anyConfig.tls.sni})`);
      }
      
      return parts.length > 0 ? parts.join(', ') : '默认配置';
    }
    
    return '标准配置';
  }
}

export const nodeService = new NodeService();

// React Query Hooks
export const useNodes = (params: NodeQueryParams) => {
  return useQuery({
    queryKey: ['nodes', params],
    queryFn: () => nodeService.getNodes(params),
    keepPreviousData: true,
  });
};

export const useAllNodes = () => {
  return useQuery({
    queryKey: ['all-nodes'],
    queryFn: () => nodeService.getAllNodes(),
    staleTime: 5 * 60 * 1000, // 5分钟
  });
};

// 获取用户可用节点的Hook
export const useUserAvailableNodes = () => {
  return useQuery({
    queryKey: ['user-available-nodes'],
    queryFn: () => nodeService.getUserAvailableNodes(),
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  });
};

export const useOnlineNodes = () => {
  return useQuery({
    queryKey: ['online-nodes'],
    queryFn: () => nodeService.getOnlineNodes(),
    refetchInterval: 30 * 1000, // 30秒刷新
  });
};

export const useNode = (id: number) => {
  return useQuery({
    queryKey: ['node', id],
    queryFn: () => nodeService.getNode(id),
    enabled: !!id,
  });
};

export const useNodeStats = (id: number) => {
  return useQuery({
    queryKey: ['node-stats', id],
    queryFn: () => nodeService.getNodeStats(id),
    enabled: !!id,
    refetchInterval: 30 * 1000, // 30秒刷新
  });
};

export const useAllNodesStats = () => {
  return useQuery({
    queryKey: ['all-nodes-stats'],
    queryFn: () => nodeService.getAllNodesStats(),
    refetchInterval: 30 * 1000, // 30秒刷新
  });
};

export const useNodeGroups = (id: number) => {
  return useQuery({
    queryKey: ['node-groups', id],
    queryFn: () => nodeService.getNodeGroups(id),
    enabled: !!id,
  });
};

// Mutations
export const useCreateNode = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: NodeForm) => nodeService.createNode(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['nodes'] });
      queryClient.invalidateQueries({ queryKey: ['all-nodes'] });
      queryClient.invalidateQueries({ queryKey: ['all-nodes-stats'] });
      message.success('节点创建成功');
    },
    onError: () => {
      message.error('节点创建失败');
    },
  });
};

export const useUpdateNode = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: NodeUpdateForm }) => 
      nodeService.updateNode(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['nodes'] });
      queryClient.invalidateQueries({ queryKey: ['node'] });
      queryClient.invalidateQueries({ queryKey: ['all-nodes'] });
      queryClient.invalidateQueries({ queryKey: ['all-nodes-stats'] });
      message.success('节点更新成功');
    },
    onError: () => {
      message.error('节点更新失败');
    },
  });
};

export const useDeleteNode = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: number) => nodeService.deleteNode(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['nodes'] });
      queryClient.invalidateQueries({ queryKey: ['all-nodes'] });
      queryClient.invalidateQueries({ queryKey: ['all-nodes-stats'] });
      message.success('节点删除成功');
    },
    onError: () => {
      message.error('节点删除失败');
    },
  });
};

export const useBatchDeleteNodes = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (ids: number[]) => nodeService.batchDeleteNodes(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['nodes'] });
      queryClient.invalidateQueries({ queryKey: ['all-nodes'] });
      queryClient.invalidateQueries({ queryKey: ['all-nodes-stats'] });
      message.success('批量删除成功');
    },
    onError: () => {
      message.error('批量删除失败');
    },
  });
};

export const useUpdateNodeStatus = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, status }: { id: number; status: 'online' | 'offline' | 'maintenance' }) => 
      nodeService.updateNodeStatus(id, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['nodes'] });
      queryClient.invalidateQueries({ queryKey: ['node'] });
      queryClient.invalidateQueries({ queryKey: ['online-nodes'] });
      queryClient.invalidateQueries({ queryKey: ['all-nodes-stats'] });
      message.success('节点状态更新成功');
    },
    onError: () => {
      message.error('节点状态更新失败');
    },
  });
};

export const useHealthCheck = () => {
  return useMutation({
    mutationFn: (id: number) => nodeService.healthCheck(id),
    onSuccess: () => {
      message.success('健康检查完成');
    },
    onError: () => {
      message.error('健康检查失败');
    },
  });
};

export const useBatchHealthCheck = () => {
  return useMutation({
    mutationFn: (nodeIds: number[]) => nodeService.batchHealthCheck(nodeIds),
    onSuccess: () => {
      message.success('批量健康检查完成');
    },
    onError: () => {
      message.error('批量健康检查失败');
    },
  });
};

export const useBatchUpdateNodes = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (operation: NodeBatchOperation) => nodeService.batchUpdateNodes(operation),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['nodes'] });
      queryClient.invalidateQueries({ queryKey: ['all-nodes'] });
      queryClient.invalidateQueries({ queryKey: ['all-nodes-stats'] });
      message.success('批量操作成功');
    },
    onError: () => {
      message.error('批量操作失败');
    },
  });
};

export default nodeService;
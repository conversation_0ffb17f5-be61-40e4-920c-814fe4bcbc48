# AnyPanel V2bX API 使用说明

## 概述

AnyPanel 提供了完全兼容标准 V2bX 规范的 API，支持与现有的 V2bX 节点服务端无缝集成。

## 配置方式

### 1. 全局 Token 验证

AnyPanel 使用全局 token 验证机制，类似于 V2board：

在 `config.yaml` 中配置：

```yaml
v2bx:
  server_token: "your-server-token"
```

### 2. 节点配置

在 AnyPanel 管理面板中创建节点，支持多种协议：
- **shadowsocks** - Shadowsocks 协议
- **vmess** - VMess 协议
- **vless** - VLESS 协议
- **trojan** - Trojan 协议
- **hysteria** - Hysteria 协议
- **tuic** - TUIC 协议
- **anytls** - AnyTLS 协议

确保节点状态设置为 "online"，并配置正确的服务器地址、端口等信息。

## API 接口

### 基础信息

- **Base URL**: `/api/v1/server/UniProxy`
- **认证方式**: 通过 `token` 参数或 `X-Node-Token` header
- **支持格式**: JSON 和 MessagePack
- **标准兼容**: 完全兼容 V2bX UniProxy 规范

### 接口列表

#### 1. 获取节点配置

```
GET /api/v1/server/UniProxy/config?token={server_token}&node_id={node_id}&node_type={node_type}
```

**参数说明**:
- `token`: 全局服务器 token
- `node_id`: 节点 ID
- `node_type`: 节点类型 (shadowsocks, vmess, vless, trojan, hysteria, tuic, anytls)

**Shadowsocks 响应示例**:
```json
{
  "server_port": 8388,
  "cipher": "aes-256-gcm",
  "obfs": "http",
  "obfs_settings": {
    "host": "example.com",
    "path": "/"
  },
  "base_config": {
    "push_interval": 60,
    "pull_interval": 60
  }
}
```

**VMess 响应示例**:
```json
{
  "server_port": 443,
  "network": "ws",
  "networkSettings": {
    "path": "/vmess",
    "host": "example.com"
  },
  "tls": true,
  "base_config": {
    "push_interval": 60,
    "pull_interval": 60
  }
}
```

**AnyTLS 响应示例**:
```json
{
  "server_port": 443,
  "server_name": "example.com",
  "padding_scheme": ["pkcs1", "pkcs8"],
  "base_config": {
    "push_interval": 60,
    "pull_interval": 60
  }
}
```

#### 2. 获取用户列表

```
GET /api/v1/server/UniProxy/user?token={server_token}&node_id={node_id}&node_type={node_type}
```

**响应示例**:
```json
{
  "users": [
    {
      "uuid": "user-uuid-1",
      "password": "",
      "traffic_used": 1048576,
      "traffic_limit": 10737418240,
      "speed_limit": 100,
      "device_limit": 3,
      "expired_at": "2024-12-31T23:59:59Z"
    }
  ]
}
```

#### 3. 上报流量数据

```
POST /api/v1/server/UniProxy/push?token={server_token}&node_id={node_id}&node_type={node_type}
```

**请求体**:
```json
[
  {
    "uuid": "user-uuid-1",
    "upload": 1024,
    "download": 2048
  }
]
```

**响应示例**:
```json
{
  "data": true
}
```

#### 4. 上报在线用户

```
POST /api/v1/server/UniProxy/alive?token={server_token}&node_id={node_id}&node_type={node_type}
```

**请求体**:
```json
[
  {
    "uuid": "user-uuid-1",
    "ip_address": "*************"
  }
]
```

**响应示例**:
```json
{
  "data": true
}
```

#### 5. 获取在线统计

```
GET /api/v1/server/UniProxy/alivelist?token={server_token}&node_id={node_id}&node_type={node_type}
```

**响应示例**:
```json
{
  "users": ["user-uuid-1", "user-uuid-2"],
  "count": 2
}
```

## V2bX 节点配置

### 服务端配置

在 V2bX 服务端配置文件中：

```yaml
# 面板地址
panel_url: "https://your-anypanel-domain.com"

# 全局 token
panel_token: "your-server-token"

# 节点信息
node_id: 33
node_type: "shadowsocks"
```

### 支持的节点类型

AnyPanel 支持以下标准 V2bX 节点类型：

| 类型 | 说明 | 配置示例 |
|------|------|----------|
| shadowsocks | Shadowsocks 协议 | `node_type: "shadowsocks"` |
| vmess | VMess 协议 | `node_type: "vmess"` |
| vless | VLESS 协议 | `node_type: "vless"` |
| trojan | Trojan 协议 | `node_type: "trojan"` |
| hysteria | Hysteria 协议 | `node_type: "hysteria"` |
| tuic | TUIC 协议 | `node_type: "tuic"` |
| anytls | AnyTLS 协议 | `node_type: "anytls"` |

### 类型映射

AnyPanel 会自动处理节点类型映射：

- `v2ray` → `vmess`
- `hysteria2` → `hysteria`
- `shadowsock` → `shadowsocks`

### 客户端连接

用户可以通过 AnyPanel 生成的订阅链接连接到节点，支持多种客户端格式：
- Clash
- Sing-box
- V2RayN
- Shadowrocket
- 等

## 标准兼容性

### ✅ 完全兼容标准 V2bX 规范

- **全局 Token 验证**: 使用单一全局 token 进行身份验证
- **多协议支持**: 支持所有标准 V2bX 协议类型
- **标准 API 接口**: 完全兼容 V2bX UniProxy API 规范
- **节点类型验证**: 严格的节点 ID + 类型验证
- **标准响应格式**: 遵循 V2bX 标准响应格式

### 🔒 安全特性

- Token 认证
- 节点 ID 和类型双重验证
- 权限组隔离
- 设备数量限制
- 流量超限自动禁用
- 订阅过期自动处理

### 📊 监控功能

- 实时在线用户统计
- 流量使用监控
- 节点健康检查
- 用户连接日志
- ETag 缓存支持

## 与 V2board 的差异

1. **简化的验证机制**: 使用单一全局 token，简化管理
2. **完整的协议支持**: 支持所有 V2bX 标准协议
3. **现代化架构**: 使用 Go 语言构建，性能更好
4. **增强的安全性**: 严格的节点类型和 ID 验证
5. **更好的错误处理**: 详细的错误信息和日志

## 故障排除

### 常见问题

1. **Token 验证失败**
   - 检查 `server_token` 配置是否正确
   - 确认节点服务端配置的 token 与面板一致

2. **节点类型不匹配**
   - 确认 `node_type` 参数与面板中配置的协议一致
   - 检查类型映射（如 v2ray → vmess）

3. **节点 ID 不存在**
   - 确认 `node_id` 参数正确
   - 检查节点是否存在于数据库中

4. **节点状态异常**
   - 检查节点状态是否为 "online"
   - 确认权限组配置正确
   - 检查用户订阅是否有效

5. **流量上报失败**
   - 检查网络连接
   - 确认节点 ID 和类型正确
   - 查看面板日志获取详细错误信息

### 调试模式

在配置文件中启用调试模式：

```yaml
debug: true
```

这将输出详细的 API 请求和响应日志。

## API 测试

### 使用 curl 测试

```bash
# 测试获取节点配置
curl "https://your-domain.com/api/v1/server/UniProxy/config?token=your-server-token&node_id=33&node_type=shadowsocks"

# 测试获取用户列表
curl "https://your-domain.com/api/v1/server/UniProxy/user?token=your-server-token&node_id=33&node_type=shadowsocks"

# 测试流量上报
curl -X POST "https://your-domain.com/api/v1/server/UniProxy/push?token=your-server-token&node_id=33&node_type=shadowsocks" \
  -H "Content-Type: application/json" \
  -d '[{"uuid": "test-uuid", "upload": 1024, "download": 2048}]'
```

## 更新日志

### v2.0.0
- 完全兼容标准 V2bX 规范
- 支持所有标准协议类型
- 实现节点 ID + 类型双重验证
- 标准化配置响应格式
- 增强错误处理和日志记录

### v1.0.0
- 实现 V2bX 兼容 API
- 支持 AnyTLS 协议
- 全局 token 验证
- 完整的权限管理
- 流量和在线用户统计
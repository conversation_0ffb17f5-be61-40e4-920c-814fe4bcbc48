import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import apiClient, { PaginatedResponse, PaginationParams } from './api';
import { User } from './user';
import { Product } from './product';

// 订单数据类型
export interface Order {
  id: number;
  order_id: string;
  user_id: number;
  user: User;
  product_id: number;
  product: Product;
  amount: number;
  status: 'pending' | 'paid' | 'cancelled' | 'refunded';
  payment_method: string;
  payment_id: string;
  paid_at: string | null;
  created_at: string;
  updated_at: string;
}

// 订单创建表单类型
export interface OrderForm {
  user_id: number;
  product_id: number;
  amount: number;
}

// 订单更新表单类型
export interface OrderUpdateForm {
  status?: 'pending' | 'paid' | 'cancelled' | 'refunded';
  payment_method?: string;
  payment_id?: string;
}

// 订单查询参数
export interface OrderQueryParams extends PaginationParams {
  search?: string;
  user_id?: number;
  product_id?: number;
  status?: string;
  payment_method?: string;
  sort_by?: string;
  sort_order?: string;
}

// 订单统计数据
export interface OrderStats {
  total_orders: number;
  pending_orders: number;
  paid_orders: number;
  cancelled_orders: number;
  refunded_orders: number;
  total_revenue: number;
  today_orders: number;
  today_revenue: number;
  this_month_orders: number;
  this_month_revenue: number;
  avg_order_amount: number;
}

// 收入分析数据
export interface RevenueAnalysisData {
  date: string;
  revenue: number;
  orders: number;
  avg_amount: number;
}

// 支付方式统计
export interface PaymentMethodStats {
  method: string;
  method_name: string;
  count: number;
  revenue: number;
  percentage: number;
}

// 商品销售统计
export interface ProductSalesStats {
  product_id: number;
  product_name: string;
  sales_count: number;
  revenue: number;
  avg_price: number;
}

// 支付处理请求
export interface PaymentProcessRequest {
  payment_method: string;
  payment_id?: string;
}

// 订单状态选项
export const OrderStatusOptions = [
  { label: '待支付', value: 'pending', color: 'warning' },
  { label: '已支付', value: 'paid', color: 'success' },
  { label: '已取消', value: 'cancelled', color: 'default' },
  { label: '已退款', value: 'refunded', color: 'error' },
];

// 支付方式选项
export const PaymentMethodOptions = [
  { label: '支付宝', value: 'alipay', icon: '💳' },
  { label: '微信支付', value: 'wechat', icon: '💚' },
  { label: '银行卡', value: 'bank', icon: '🏦' },
  { label: '余额支付', value: 'balance', icon: '💰' },
  { label: '手动确认', value: 'manual', icon: '✋' },
];

class OrderService {
  // 获取订单列表
  async getOrders(params: OrderQueryParams): Promise<PaginatedResponse<Order>> {
    // 转换分页参数以匹配后端期望的格式
    const backendParams = {
      ...params,
      page: params.current || 1,
      page_size: params.pageSize || 20,
    };
    // 删除前端的分页参数，避免冲突
    delete backendParams.current;
    delete backendParams.pageSize;

    const response = await apiClient.get('/orders', backendParams);
    // 转换后端响应格式为前端期望的分页格式
    return {
      list: response.orders || [],
      total: response.total || 0,
      current: params.current || 1,
      pageSize: params.pageSize || 20
    };
  }

  // 获取单个订单详情
  async getOrder(id: number): Promise<Order> {
    return apiClient.get(`/orders/${id}`);
  }

  // 创建订单
  async createOrder(data: OrderForm): Promise<Order> {
    return apiClient.post('/orders', data);
  }

  // 更新订单
  async updateOrder(id: number, data: OrderUpdateForm): Promise<Order> {
    return apiClient.put(`/orders/${id}`, data);
  }

  // 删除订单
  async deleteOrder(id: number): Promise<void> {
    return apiClient.delete(`/orders/${id}`);
  }

  // 更新订单状态
  async updateOrderStatus(id: number, status: 'pending' | 'paid' | 'cancelled' | 'refunded'): Promise<Order> {
    return apiClient.put(`/orders/${id}/status`, { status });
  }

  // 处理支付
  async processPayment(id: number, data: PaymentProcessRequest): Promise<Order> {
    return apiClient.post(`/orders/${id}/pay`, data);
  }

  // 获取订单统计
  async getOrderStats(): Promise<OrderStats> {
    return apiClient.get('/orders/stats');
  }

  // 获取收入分析数据
  async getRevenueAnalysis(days: number = 30): Promise<RevenueAnalysisData[]> {
    return apiClient.get('/orders/revenue-analysis', { days });
  }

  // 获取支付方式统计
  async getPaymentMethodStats(): Promise<PaymentMethodStats[]> {
    return apiClient.get('/orders/payment-method-stats');
  }

  // 获取商品销售统计
  async getProductSalesStats(): Promise<ProductSalesStats[]> {
    return apiClient.get('/orders/product-sales-stats');
  }

  // 批量删除订单
  async batchDeleteOrders(ids: number[]): Promise<void> {
    await Promise.all(ids.map(id => this.deleteOrder(id)));
  }

  // 批量更新订单状态
  async batchUpdateOrderStatus(ids: number[], status: 'pending' | 'paid' | 'cancelled' | 'refunded'): Promise<void> {
    await Promise.all(ids.map(id => this.updateOrderStatus(id, status)));
  }

  // 格式化订单状态
  formatStatus(status: string): { text: string; color: string } {
    const statusOption = OrderStatusOptions.find(option => option.value === status);
    return {
      text: statusOption?.label || status,
      color: statusOption?.color || 'default'
    };
  }

  // 格式化支付方式
  formatPaymentMethod(method: string): { text: string; icon: string } {
    const methodOption = PaymentMethodOptions.find(option => option.value === method);
    return {
      text: methodOption?.label || method,
      icon: methodOption?.icon || '💳'
    };
  }

  // 格式化金额
  formatAmount(amount: number): string {
    return `¥${amount.toFixed(2)}`;
  }

  // 计算订单完成率
  calculateCompletionRate(stats: OrderStats): number {
    if (stats.total_orders === 0) return 0;
    return (stats.paid_orders / stats.total_orders) * 100;
  }

  // 计算退款率
  calculateRefundRate(stats: OrderStats): number {
    if (stats.paid_orders === 0) return 0;
    return (stats.refunded_orders / stats.paid_orders) * 100;
  }

  // 获取状态颜色
  getStatusColor(status: string): string {
    const statusMap = {
      pending: 'warning',
      paid: 'success',
      cancelled: 'default',
      refunded: 'error'
    };
    return statusMap[status as keyof typeof statusMap] || 'default';
  }

  // 获取状态文本
  getStatusText(status: string): string {
    const statusMap = {
      pending: '待支付',
      paid: '已支付',
      cancelled: '已取消',
      refunded: '已退款'
    };
    return statusMap[status as keyof typeof statusMap] || status;
  }

  // 检查订单是否可以取消
  canCancel(order: Order): boolean {
    return order.status === 'pending';
  }

  // 检查订单是否可以退款
  canRefund(order: Order): boolean {
    return order.status === 'paid';
  }

  // 检查订单是否可以手动确认
  canManualConfirm(order: Order): boolean {
    return order.status === 'pending';
  }

  // 生成订单摘要
  getOrderSummary(order: Order): string {
    return `${order.user.username} 购买 ${order.product.name} - ${this.formatAmount(order.amount)}`;
  }

  // 获取支付方式图标
  getPaymentMethodIcon(method: string): string {
    const methodOption = PaymentMethodOptions.find(option => option.value === method);
    return methodOption?.icon || '💳';
  }

  // 格式化订单编号
  formatOrderId(orderId: string): string {
    // 如果订单号过长，显示前8位和后4位
    if (orderId.length > 12) {
      return `${orderId.substring(0, 8)}...${orderId.slice(-4)}`;
    }
    return orderId;
  }

  // 计算订单时长（从创建到支付）
  calculateOrderDuration(order: Order): string {
    if (!order.paid_at || order.status !== 'paid') return '-';
    
    const createdAt = new Date(order.created_at);
    const paidAt = new Date(order.paid_at);
    const duration = paidAt.getTime() - createdAt.getTime();
    
    const minutes = Math.floor(duration / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days}天`;
    if (hours > 0) return `${hours}小时`;
    return `${minutes}分钟`;
  }

  // 获取今日收入增长率
  getTodayGrowthRate(stats: OrderStats): number {
    // 这里简化处理，实际项目中应该从API获取昨日数据进行比较
    // 返回模拟的增长率
    return Math.floor(Math.random() * 20 - 10); // -10% 到 +10%
  }

  // 获取本月收入增长率
  getMonthGrowthRate(stats: OrderStats): number {
    // 这里简化处理，实际项目中应该从API获取上月数据进行比较
    // 返回模拟的增长率
    return Math.floor(Math.random() * 50 - 25); // -25% 到 +25%
  }
}

export const orderService = new OrderService();

// React Query Hooks
export const useOrders = (params: OrderQueryParams) => {
  return useQuery({
    queryKey: ['orders', params],
    queryFn: () => orderService.getOrders(params),
    keepPreviousData: true,
  });
};

export const useOrder = (id: number) => {
  return useQuery({
    queryKey: ['order', id],
    queryFn: () => orderService.getOrder(id),
    enabled: !!id,
  });
};

export const useOrderStats = () => {
  return useQuery({
    queryKey: ['order-stats'],
    queryFn: () => orderService.getOrderStats(),
    refetchInterval: 5 * 60 * 1000, // 5分钟刷新
  });
};

export const useRevenueAnalysis = (days: number = 30) => {
  return useQuery({
    queryKey: ['revenue-analysis', days],
    queryFn: () => orderService.getRevenueAnalysis(days),
    staleTime: 5 * 60 * 1000, // 5分钟
  });
};

export const usePaymentMethodStats = () => {
  return useQuery({
    queryKey: ['payment-method-stats'],
    queryFn: () => orderService.getPaymentMethodStats(),
    staleTime: 10 * 60 * 1000, // 10分钟
  });
};

export const useProductSalesStats = () => {
  return useQuery({
    queryKey: ['product-sales-stats'],
    queryFn: () => orderService.getProductSalesStats(),
    staleTime: 10 * 60 * 1000, // 10分钟
  });
};

// Mutations
export const useCreateOrder = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: OrderForm) => orderService.createOrder(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      queryClient.invalidateQueries({ queryKey: ['order-stats'] });
      message.success('订单创建成功');
    },
    onError: () => {
      message.error('订单创建失败');
    },
  });
};

export const useUpdateOrder = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: OrderUpdateForm }) => 
      orderService.updateOrder(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      queryClient.invalidateQueries({ queryKey: ['order'] });
      queryClient.invalidateQueries({ queryKey: ['order-stats'] });
      message.success('订单更新成功');
    },
    onError: () => {
      message.error('订单更新失败');
    },
  });
};

export const useDeleteOrder = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: number) => orderService.deleteOrder(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      queryClient.invalidateQueries({ queryKey: ['order-stats'] });
      message.success('订单删除成功');
    },
    onError: () => {
      message.error('订单删除失败');
    },
  });
};

export const useBatchDeleteOrders = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (ids: number[]) => orderService.batchDeleteOrders(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      queryClient.invalidateQueries({ queryKey: ['order-stats'] });
      message.success('批量删除成功');
    },
    onError: () => {
      message.error('批量删除失败');
    },
  });
};

export const useUpdateOrderStatus = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, status }: { id: number; status: 'pending' | 'paid' | 'cancelled' | 'refunded' }) => 
      orderService.updateOrderStatus(id, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      queryClient.invalidateQueries({ queryKey: ['order'] });
      queryClient.invalidateQueries({ queryKey: ['order-stats'] });
      message.success('订单状态更新成功');
    },
    onError: () => {
      message.error('订单状态更新失败');
    },
  });
};

export const useProcessPayment = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: PaymentProcessRequest }) => 
      orderService.processPayment(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      queryClient.invalidateQueries({ queryKey: ['order'] });
      queryClient.invalidateQueries({ queryKey: ['order-stats'] });
      message.success('支付处理成功');
    },
    onError: () => {
      message.error('支付处理失败');
    },
  });
};

export const useBatchUpdateOrderStatus = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ ids, status }: { ids: number[]; status: 'pending' | 'paid' | 'cancelled' | 'refunded' }) => 
      orderService.batchUpdateOrderStatus(ids, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      queryClient.invalidateQueries({ queryKey: ['order-stats'] });
      message.success('批量状态更新成功');
    },
    onError: () => {
      message.error('批量状态更新失败');
    },
  });
};

export default orderService;
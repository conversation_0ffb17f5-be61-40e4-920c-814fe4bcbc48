package api

import (
	"anypanel/internal/middleware"
	"anypanel/internal/service"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SecurityHandler 安全处理器
type SecurityHandler struct {
	securityEventService *service.SecurityEventService
	ipBanService         *service.IPBanService
	auditLogService      *service.AuditLogService
	rateLimitService     *service.RateLimitService
}

// NewSecurityHandler 创建安全处理器
func NewSecurityHandler(db *gorm.DB) *SecurityHandler {
	return &SecurityHandler{
		securityEventService: service.NewSecurityEventService(db),
		ipBanService:         service.NewIPBanService(db),
		auditLogService:      service.NewAuditLogService(db),
		rateLimitService:     service.NewRateLimitService(db),
	}
}

// GetSecurityEvents 获取安全事件列表
func (h *SecurityHandler) GetSecurityEvents(c *gin.Context) {
	// 解析查询参数
	limitStr := c.Default<PERSON>y("limit", "20")
	offsetStr := c.<PERSON>ult<PERSON>("offset", "0")
	eventType := c.Query("event_type")
	severity := c.Query("severity")
	ipAddress := c.Query("ip_address")

	limit, _ := strconv.Atoi(limitStr)
	offset, _ := strconv.Atoi(offsetStr)

	// 获取安全事件
	events, total, err := h.securityEventService.GetSecurityEvents(limit, offset, eventType, severity, ipAddress)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取安全事件失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"events": events,
		"total":  total,
		"limit":  limit,
		"offset": offset,
	})
}

// GetBannedIPs 获取封禁IP列表
func (h *SecurityHandler) GetBannedIPs(c *gin.Context) {
	// 解析查询参数
	limitStr := c.DefaultQuery("limit", "20")
	offsetStr := c.DefaultQuery("offset", "0")
	activeOnlyStr := c.DefaultQuery("active_only", "true")

	limit, _ := strconv.Atoi(limitStr)
	offset, _ := strconv.Atoi(offsetStr)
	activeOnly, _ := strconv.ParseBool(activeOnlyStr)

	// 获取封禁IP列表
	records, total, err := h.ipBanService.GetBannedIPs(limit, offset, activeOnly)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取封禁IP列表失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"records": records,
		"total":   total,
		"limit":   limit,
		"offset":  offset,
	})
}

// BanIPRequest 封禁IP请求结构
type BanIPRequest struct {
	IPAddress   string `json:"ip_address" binding:"required"`
	Reason      string `json:"reason" binding:"required"`
	BanType     string `json:"ban_type" binding:"required,oneof=temporary permanent"`
	DurationDays int   `json:"duration_days"` // 临时封禁的天数
}

// BanIP 封禁IP
func (h *SecurityHandler) BanIP(c *gin.Context) {
	var req BanIPRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "参数错误",
			"details": err.Error(),
		})
		return
	}

	// 获取当前用户ID
	currentUser, exists := middleware.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "未找到当前用户信息",
		})
		return
	}

	// 计算过期时间
	var expiresAt *time.Time
	if req.BanType == "temporary" && req.DurationDays > 0 {
		expiry := time.Now().AddDate(0, 0, req.DurationDays)
		expiresAt = &expiry
	}

	// 执行封禁
	err := h.ipBanService.BanIP(req.IPAddress, req.Reason, req.BanType, expiresAt, currentUser.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "封禁IP失败",
			"details": err.Error(),
		})
		return
	}

	// 记录审计日志
	clientIP := c.ClientIP()
	h.auditLogService.LogOperation(
		currentUser.ID,
		"ban_ip",
		"ip_ban_record",
		req.IPAddress,
		clientIP,
		c.GetHeader("User-Agent"),
		nil,
		req,
	)

	// 记录安全事件
	h.securityEventService.LogSecurityEvent(
		currentUser.ID,
		"ip_banned",
		clientIP,
		c.GetHeader("User-Agent"),
		"管理员封禁IP: "+req.IPAddress+" 原因: "+req.Reason,
		"medium",
		"admin",
		req,
	)

	c.JSON(http.StatusOK, gin.H{
		"message": "IP封禁成功",
	})
}

// UnbanIP 解封IP
func (h *SecurityHandler) UnbanIP(c *gin.Context) {
	ipAddress := c.Param("ip")
	if ipAddress == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "IP地址不能为空",
		})
		return
	}

	// 获取当前用户ID
	currentUser, exists := middleware.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "未找到当前用户信息",
		})
		return
	}

	// 执行解封
	err := h.ipBanService.UnbanIP(ipAddress)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "解封IP失败",
			"details": err.Error(),
		})
		return
	}

	// 记录审计日志
	clientIP := c.ClientIP()
	h.auditLogService.LogOperation(
		currentUser.ID,
		"unban_ip",
		"ip_ban_record",
		ipAddress,
		clientIP,
		c.GetHeader("User-Agent"),
		nil,
		gin.H{"ip_address": ipAddress},
	)

	// 记录安全事件
	h.securityEventService.LogSecurityEvent(
		currentUser.ID,
		"ip_unbanned",
		clientIP,
		c.GetHeader("User-Agent"),
		"管理员解封IP: "+ipAddress,
		"low",
		"admin",
		gin.H{"ip_address": ipAddress},
	)

	c.JSON(http.StatusOK, gin.H{
		"message": "IP解封成功",
	})
}

// GetAuditLogs 获取审计日志
func (h *SecurityHandler) GetAuditLogs(c *gin.Context) {
	// 解析查询参数
	limitStr := c.DefaultQuery("limit", "20")
	offsetStr := c.DefaultQuery("offset", "0")
	userIDStr := c.Query("user_id")
	action := c.Query("action")
	resource := c.Query("resource")

	limit, _ := strconv.Atoi(limitStr)
	offset, _ := strconv.Atoi(offsetStr)
	
	var userID uint
	if userIDStr != "" {
		if id, err := strconv.ParseUint(userIDStr, 10, 32); err == nil {
			userID = uint(id)
		}
	}

	// 获取审计日志
	logs, total, err := h.auditLogService.GetAuditLogs(limit, offset, userID, action, resource)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取审计日志失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"logs":   logs,
		"total":  total,
		"limit":  limit,
		"offset": offset,
	})
}

// GetRateLimitRecords 获取速率限制记录
func (h *SecurityHandler) GetRateLimitRecords(c *gin.Context) {
	// 解析查询参数
	limitStr := c.DefaultQuery("limit", "20")
	offsetStr := c.DefaultQuery("offset", "0")
	identifier := c.Query("identifier")
	limitType := c.Query("limit_type")
	blockedOnlyStr := c.DefaultQuery("blocked_only", "false")

	limit, _ := strconv.Atoi(limitStr)
	offset, _ := strconv.Atoi(offsetStr)
	blockedOnly, _ := strconv.ParseBool(blockedOnlyStr)

	// 获取速率限制记录
	records, total, err := h.rateLimitService.GetRateLimitRecords(limit, offset, identifier, limitType, blockedOnly)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取速率限制记录失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"records": records,
		"total":   total,
		"limit":   limit,
		"offset":  offset,
	})
}

// CleanupRateLimitRecords 清理旧的速率限制记录
func (h *SecurityHandler) CleanupRateLimitRecords(c *gin.Context) {
	hoursStr := c.DefaultQuery("hours", "24")
	hours, _ := strconv.Atoi(hoursStr)

	err := h.rateLimitService.CleanupOldRecords(hours)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "清理速率限制记录失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "清理完成",
	})
}

// SetupSecurityRoutes 设置安全相关路由
func SetupSecurityRoutes(v1 *gin.RouterGroup, db *gorm.DB) {
	securityHandler := NewSecurityHandler(db)

	// 安全路由组 - 需要管理员权限
	security := v1.Group("/security")
	security.Use(middleware.AuthMiddlewareCompat(db), middleware.AdminMiddleware())
	{
		// 安全事件
		security.GET("/events", securityHandler.GetSecurityEvents)

		// IP封禁管理
		security.GET("/banned-ips", securityHandler.GetBannedIPs)
		security.POST("/ban-ip", securityHandler.BanIP)
		security.DELETE("/unban-ip/:ip", securityHandler.UnbanIP)

		// 审计日志
		security.GET("/audit-logs", securityHandler.GetAuditLogs)

		// 速率限制记录
		security.GET("/rate-limit-records", securityHandler.GetRateLimitRecords)
		security.POST("/cleanup-rate-limit-records", securityHandler.CleanupRateLimitRecords)
	}
}
{"version": "0.2.0", "configurations": [{"name": "Launch Backend", "type": "go", "request": "launch", "mode": "auto", "program": "${workspaceFolder}/backend/cmd/main.go", "env": {"GIN_MODE": "debug"}, "args": [], "cwd": "${workspaceFolder}/backend", "console": "integratedTerminal", "internalConsoleOptions": "openOnSessionStart"}, {"name": "Launch Frontend", "type": "node", "request": "launch", "program": "${workspaceFolder}/frontend/node_modules/vite/bin/vite.js", "args": [], "cwd": "${workspaceFolder}/frontend", "console": "integratedTerminal", "internalConsoleOptions": "openOnSessionStart"}]}
package config

import (
	"fmt"
	"log"
	"strings"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"
	"github.com/go-playground/validator/v10"
	"github.com/spf13/viper"
)

// ConfigManager 全局配置管理器
type ConfigManager struct {
	config   *Config
	viper    *viper.Viper
	mu       sync.RWMutex
	watchers []ConfigWatcher
	stopCh   chan struct{}
}

// ConfigWatcher 配置变更监听器
type ConfigWatcher func(config *Config)

var (
	configManager *ConfigManager
	configOnce    sync.Once
)

// GetConfigManager 获取配置管理器单例
func GetConfigManager() *ConfigManager {
	configOnce.Do(func() {
		configManager = &ConfigManager{
			viper:    viper.New(),
			watchers: make([]ConfigWatcher, 0),
		}
	})
	return configManager
}

// Config 主配置结构
type Config struct {
	Debug bool `mapstructure:"debug"`

	// 静态配置（从配置文件加载）
	Server Server   `mapstructure:"server" validate:"required"`
	DB     Database `mapstructure:"database" validate:"required"`
	Redis  Redis    `mapstructure:"redis" validate:"required"`
	JWT    JWT      `mapstructure:"jwt" validate:"required"`

	// 动态配置（从数据库加载）
	V2bX       V2bX       `json:"v2bx"`
	Log        Log        `json:"log"`
	CORS       CORS       `json:"cors"`
	RateLimit  RateLimit  `json:"rate_limit"`
	Upload     Upload     `json:"upload"`
	Payment    Payment    `json:"payment"`
	Security   Security   `json:"security"`
	Monitoring Monitoring `json:"monitoring"`
	Email      Email      `json:"email"`
	SMS        SMS        `json:"sms"`
}

// Server 服务器配置
type Server struct {
	Address      string        `mapstructure:"address" validate:"required"`
	ReadTimeout  time.Duration `mapstructure:"read_timeout"`
	WriteTimeout time.Duration `mapstructure:"write_timeout"`
}

// Database 数据库配置
type Database struct {
	Type            string        `mapstructure:"type" validate:"required,oneof=mysql postgres sqlite"`
	Host            string        `mapstructure:"host" validate:"required"`
	Port            int           `mapstructure:"port" validate:"required,min=1,max=65535"`
	Username        string        `mapstructure:"username" validate:"required"`
	Password        string        `mapstructure:"password" validate:"required"`
	DBName          string        `mapstructure:"dbname" validate:"required"`
	Charset         string        `mapstructure:"charset"`
	SSLMode         string        `mapstructure:"ssl_mode"`
	MaxIdleConns    int           `mapstructure:"max_idle_conns"`
	MaxOpenConns    int           `mapstructure:"max_open_conns"`
	ConnMaxLifetime time.Duration `mapstructure:"conn_max_lifetime"`
}

// Redis Redis配置
type Redis struct {
	Host     string `mapstructure:"host" validate:"required"`
	Port     int    `mapstructure:"port" validate:"required,min=1,max=65535"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db" validate:"min=0"`
	PoolSize int    `mapstructure:"pool_size" validate:"min=1"`
}

// JWT JWT配置
type JWT struct {
	Secret               string        `mapstructure:"secret" validate:"required,min=32"`
	ExpireTime           int           `mapstructure:"expire_time" validate:"required"`
	RefreshExpireTime    int           `mapstructure:"refresh_expire_time" validate:"required"`
	AccessTokenExpire    time.Duration `mapstructure:"access_token_expire" validate:"required"`
	RefreshTokenExpire   time.Duration `mapstructure:"refresh_token_expire" validate:"required"`
	RefreshTokenLifetime time.Duration `mapstructure:"refresh_token_lifetime" validate:"required"`
}

// V2bX V2bX配置
type V2bX struct {
	ServerToken string `json:"server_token"`
}

// Log 日志配置
type Log struct {
	Level      string `json:"level"`
	File       string `json:"file"`
	MaxSize    int    `json:"max_size"`
	MaxBackups int    `json:"max_backups"`
	MaxAge     int    `json:"max_age"`
}

// CORS CORS配置
type CORS struct {
	AllowedOrigins []string `json:"allowed_origins"`
	AllowedMethods []string `json:"allowed_methods"`
	AllowedHeaders []string `json:"allowed_headers"`
}

// RateLimit 限流配置
type RateLimit struct {
	Enabled           bool `json:"enabled"`
	RequestsPerMinute int  `json:"requests_per_minute"`
	Burst             int  `json:"burst"`
}

type Upload struct {
	MaxSize      int      `json:"max_size"`
	AllowedTypes []string `json:"allowed_types"`
}

// Payment 支付配置
type Payment struct {
	Timeout int          `json:"timeout"`
	Alipay  AlipayConfig `json:"alipay"`
	WeChat  WeChatConfig `json:"wechat"`
}

// AlipayConfig 支付宝配置
type AlipayConfig struct {
	AppID      string `json:"app_id"`
	PrivateKey string `json:"private_key"`
	PublicKey  string `json:"public_key"`
	NotifyURL  string `json:"notify_url"`
	IsSandbox  bool   `json:"is_sandbox"`
}

// WeChatConfig 微信支付配置
type WeChatConfig struct {
	AppID     string `json:"app_id"`
	MchID     string `json:"mch_id"`
	APIKey    string `json:"api_key"`
	NotifyURL string `json:"notify_url"`
	IsSandbox bool   `json:"is_sandbox"`
}

// Security 安全配置
type Security struct {
	EncryptionKey    string            `json:"encryption_key"`
	RateLimit        SecurityRateLimit `json:"rate_limit"`
	IPWhitelist      []string          `json:"ip_whitelist"`
	IPBlacklist      []string          `json:"ip_blacklist"`
	MaxLoginAttempts int               `json:"max_login_attempts"`
	LockoutDuration  int               `json:"lockout_duration"`
}

type SecurityRateLimit struct {
	Enabled           bool `json:"enabled"`
	RequestsPerMinute int  `json:"requests_per_minute"`
	Burst             int  `json:"burst"`
}

// Monitoring 监控配置
type Monitoring struct {
	Enabled              bool `json:"enabled"`
	MetricsRetentionDays int  `json:"metrics_retention_days"`
	LogsRetentionDays    int  `json:"logs_retention_days"`
	HealthCheckInterval  int  `json:"health_check_interval"`
}

// Email 邮件配置
type Email struct {
	Enabled  bool   `json:"enabled"`
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
	From     string `json:"from"`
	TLS      bool   `json:"tls"`
}

// SMS 短信配置
type SMS struct {
	Enabled   bool   `json:"enabled"`
	Provider  string `json:"provider"`
	AppID     string `json:"app_id"`
	AppSecret string `json:"app_secret"`
	SignName  string `json:"sign_name"`
}

// Load 加载配置
func Load() (*Config, error) {
	configManager := GetConfigManager()

	// 设置配置文件路径
	configManager.viper.SetConfigName("config")
	configManager.viper.SetConfigType("yaml")
	configManager.viper.AddConfigPath(".")

	// 设置环境变量
	configManager.viper.AutomaticEnv()
	configManager.viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// 读取配置文件
	if err := configManager.viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析配置
	var config Config
	if err := configManager.viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("解析配置失败: %w", err)
	}

	// 验证配置
	validate := validator.New()
	if err := validate.Struct(&config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	// 设置管理器配置
	configManager.mu.Lock()
	configManager.config = &config
	configManager.mu.Unlock()

	// 启动配置文件监听
	configManager.viper.OnConfigChange(func(e fsnotify.Event) {
		log.Println("配置文件发生变更，重新加载配置")
		newConfig, err := configManager.reload()
		if err != nil {
			log.Printf("重新加载配置失败: %v", err)
			return
		}

		// 通知监听器
		configManager.mu.RLock()
		watchers := configManager.watchers
		configManager.mu.RUnlock()

		for _, watcher := range watchers {
			watcher(newConfig)
		}
	})
	configManager.viper.WatchConfig()

	return &config, nil
}

// reload 重新加载配置
func (cm *ConfigManager) reload() (*Config, error) {
	var config Config
	if err := cm.viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("重新解析配置失败: %w", err)
	}

	validate := validator.New()
	if err := validate.Struct(&config); err != nil {
		return nil, fmt.Errorf("重新验证配置失败: %w", err)
	}

	cm.mu.Lock()
	cm.config = &config
	cm.mu.Unlock()

	return &config, nil
}

// GetConfig 获取当前配置
func GetConfig() *Config {
	manager := GetConfigManager()
	manager.mu.RLock()
	defer manager.mu.RUnlock()
	return manager.config
}

// AddWatcher 添加配置变更监听器
func AddWatcher(watcher ConfigWatcher) {
	manager := GetConfigManager()
	manager.mu.Lock()
	defer manager.mu.Unlock()
	manager.watchers = append(manager.watchers, watcher)
}

// SetDynamicConfig 设置动态配置（外部调用）
func (cm *ConfigManager) SetDynamicConfig(dynamicConfig *Config) {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	// 将动态配置合并到当前配置中
	if dynamicConfig.V2bX.ServerToken != "" {
		cm.config.V2bX.ServerToken = dynamicConfig.V2bX.ServerToken
	}
	if dynamicConfig.Log.Level != "" {
		cm.config.Log.Level = dynamicConfig.Log.Level
	}
	if dynamicConfig.Log.File != "" {
		cm.config.Log.File = dynamicConfig.Log.File
	}
	// ... 其他字段的合并

	// 通知所有监听器
	for _, watcher := range cm.watchers {
		watcher(cm.config)
	}
}

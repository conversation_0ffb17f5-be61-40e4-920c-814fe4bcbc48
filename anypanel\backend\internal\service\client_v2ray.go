package service

import (
	"anypanel/internal/model"
	"encoding/base64"
	"fmt"
	"strings"
)

// V2RayConfigGenerator V2Ray配置生成器（生成URI列表）
type V2RayConfigGenerator struct{}

// GenerateSubscription 生成V2Ray订阅配置
func (g *V2RayConfigGenerator) GenerateSubscription(nodes []*model.Node, userUUID string) (*SubscriptionResult, error) {
	var uris []string

	// 为每个节点生成URI
	for _, node := range nodes {
		uri, err := g.generateNodeURI(node, userUUID)
		if err != nil {
			continue // 跳过URI生成失败的节点
		}
		uris = append(uris, uri)
	}

	// 如果没有可用的URI，返回空配置
	if len(uris) == 0 {
		return &SubscriptionResult{
			Content:     "",
			ContentType: "text/plain",
			Filename:    "empty.txt",
		}, nil
	}

	// 将URI列表用换行符连接并进行Base64编码
	content := strings.Join(uris, "\n")
	encodedContent := base64.StdEncoding.EncodeToString([]byte(content))

	return &SubscriptionResult{
		Content:     encodedContent,
		ContentType: "text/plain",
		Filename:    "subscription.txt",
	}, nil
}

// generateNodeURI 生成节点的URI
func (g *V2RayConfigGenerator) generateNodeURI(node *model.Node, userUUID string) (string, error) {
	handler, err := GetProtocolHandler(node.Protocol)
	if err != nil {
		return "", fmt.Errorf("不支持的协议: %s", node.Protocol)
	}

	return handler.GenerateURI(node, userUUID)
}

// GetSupportedProtocols 获取支持的协议
func (g *V2RayConfigGenerator) GetSupportedProtocols() []string {
	return []string{"anytls"}
}

// generateNodeName 生成节点名称（支持emoji和地区标识）
func (g *V2RayConfigGenerator) generateNodeName(node *model.Node) string {
	// 这里可以根据节点信息添加地区emoji
	// 目前简单返回节点名称
	return node.Name
}

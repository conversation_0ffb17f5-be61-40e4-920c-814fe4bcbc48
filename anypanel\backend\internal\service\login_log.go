package service

import (
	"anypanel/internal/model"
	"fmt"
	"net"
	"strings"
	"time"

	"gorm.io/gorm"
)

type LoginLogService struct {
	db *gorm.DB
}

func NewLoginLogService(db *gorm.DB) *LoginLogService {
	return &LoginLogService{
		db: db,
	}
}

// LoginLogQueryRequest 登录日志查询请求
type LoginLogQueryRequest struct {
	Page      int    `form:"page" binding:"min=1"`
	PageSize  int    `form:"page_size" binding:"min=1,max=100"`
	UserID    *uint  `form:"user_id"`
	Status    string `form:"status"`
	StartTime string `form:"start_time"`
	EndTime   string `form:"end_time"`
	IPAddress string `form:"ip_address"`
	SortBy    string `form:"sort_by"`
	SortOrder string `form:"sort_order"`
}

// LoginLogQueryResponse 登录日志查询响应
type LoginLogQueryResponse struct {
	Total int64          `json:"total"`
	Logs  []*LoginLogDTO `json:"logs"`
}

// LoginLogDTO 登录日志数据传输对象
type LoginLogDTO struct {
	ID         uint      `json:"id"`
	UserID     uint      `json:"user_id"`
	Username   string    `json:"username"`
	Email      string    `json:"email"`
	IPAddress  string    `json:"ip_address"`
	UserAgent  string    `json:"user_agent"`
	Location   string    `json:"location"`
	Status     string    `json:"status"`
	Method     string    `json:"method"`
	FailReason string    `json:"fail_reason"`
	LoginAt    time.Time `json:"login_at"`
}

// CreateLoginLogRequest 创建登录日志请求
type CreateLoginLogRequest struct {
	UserID     uint   `json:"user_id" binding:"required"`
	IPAddress  string `json:"ip_address" binding:"required"`
	UserAgent  string `json:"user_agent"`
	Status     string `json:"status" binding:"oneof=success failed"`
	Method     string `json:"method"`
	FailReason string `json:"fail_reason"`
}

// RecordLoginLog 记录登录日志
func (s *LoginLogService) RecordLoginLog(req *CreateLoginLogRequest) error {
	// 生成地理位置信息（简化实现）
	location := s.getLocationFromIP(req.IPAddress)

	loginLog := &model.LoginLog{
		UserID:     req.UserID,
		IPAddress:  req.IPAddress,
		UserAgent:  req.UserAgent,
		Location:   location,
		Status:     req.Status,
		Method:     req.Method,
		FailReason: req.FailReason,
		LoginAt:    time.Now(),
	}

	if err := loginLog.Validate(); err != nil {
		return err
	}

	return s.db.Create(loginLog).Error
}

// GetUserLoginLogs 获取用户登录日志
func (s *LoginLogService) GetUserLoginLogs(userID uint, req *LoginLogQueryRequest) (*LoginLogQueryResponse, error) {
	query := s.db.Model(&model.LoginLog{}).Where("user_id = ?", userID)

	// 应用筛选条件
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	if req.StartTime != "" {
		if startTime, err := time.Parse("2006-01-02", req.StartTime); err == nil {
			query = query.Where("login_at >= ?", startTime)
		}
	}

	if req.EndTime != "" {
		if endTime, err := time.Parse("2006-01-02", req.EndTime); err == nil {
			// 包含整天
			endTime = endTime.Add(24*time.Hour - time.Second)
			query = query.Where("login_at <= ?", endTime)
		}
	}

	if req.IPAddress != "" {
		query = query.Where("ip_address LIKE ?", "%"+req.IPAddress+"%")
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 排序
	sortBy := req.SortBy
	if sortBy == "" {
		sortBy = "login_at"
	}
	sortOrder := req.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// 分页
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// 预加载用户信息
	var logs []model.LoginLog
	if err := query.Preload("User").Find(&logs).Error; err != nil {
		return nil, err
	}

	// 转换为DTO
	logDTOs := make([]*LoginLogDTO, len(logs))
	for i, log := range logs {
		logDTOs[i] = s.toLoginLogDTO(&log)
	}

	return &LoginLogQueryResponse{
		Total: total,
		Logs:  logDTOs,
	}, nil
}

// QueryLoginLogs 查询登录日志（管理员使用）
func (s *LoginLogService) QueryLoginLogs(req *LoginLogQueryRequest) (*LoginLogQueryResponse, error) {
	query := s.db.Model(&model.LoginLog{})

	// 应用筛选条件
	if req.UserID != nil {
		query = query.Where("user_id = ?", *req.UserID)
	}

	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	if req.StartTime != "" {
		if startTime, err := time.Parse("2006-01-02", req.StartTime); err == nil {
			query = query.Where("login_at >= ?", startTime)
		}
	}

	if req.EndTime != "" {
		if endTime, err := time.Parse("2006-01-02", req.EndTime); err == nil {
			// 包含整天
			endTime = endTime.Add(24*time.Hour - time.Second)
			query = query.Where("login_at <= ?", endTime)
		}
	}

	if req.IPAddress != "" {
		query = query.Where("ip_address LIKE ?", "%"+req.IPAddress+"%")
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 排序
	sortBy := req.SortBy
	if sortBy == "" {
		sortBy = "login_at"
	}
	sortOrder := req.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// 分页
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// 预加载用户信息
	var logs []model.LoginLog
	if err := query.Preload("User").Find(&logs).Error; err != nil {
		return nil, err
	}

	// 转换为DTO
	logDTOs := make([]*LoginLogDTO, len(logs))
	for i, log := range logs {
		logDTOs[i] = s.toLoginLogDTO(&log)
	}

	return &LoginLogQueryResponse{
		Total: total,
		Logs:  logDTOs,
	}, nil
}

// GetRecentLoginLogs 获取最近的登录日志
func (s *LoginLogService) GetRecentLoginLogs(userID uint, limit int) ([]*LoginLogDTO, error) {
	if limit <= 0 {
		limit = 10
	}

	var logs []model.LoginLog
	if err := s.db.Where("user_id = ?", userID).
		Order("login_at DESC").
		Limit(limit).
		Preload("User").
		Find(&logs).Error; err != nil {
		return nil, err
	}

	// 转换为DTO
	logDTOs := make([]*LoginLogDTO, len(logs))
	for i, log := range logs {
		logDTOs[i] = s.toLoginLogDTO(&log)
	}

	return logDTOs, nil
}

// GetLoginStats 获取登录统计信息
func (s *LoginLogService) GetLoginStats(userID *uint, days int) (map[string]interface{}, error) {
	if days <= 0 {
		days = 30 // 默认30天
	}

	startTime := time.Now().AddDate(0, 0, -days)
	query := s.db.Model(&model.LoginLog{}).Where("login_at >= ?", startTime)

	// 如果指定用户ID，只查询该用户的统计
	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}

	// 总登录次数
	var totalLogins int64
	if err := query.Count(&totalLogins).Error; err != nil {
		return nil, err
	}

	// 成功登录次数
	var successLogins int64
	if err := query.Where("status = ?", "success").Count(&successLogins).Error; err != nil {
		return nil, err
	}

	// 失败登录次数
	failedLogins := totalLogins - successLogins

	// 不同IP数量
	var uniqueIPs int64
	if err := query.Distinct("ip_address").Count(&uniqueIPs).Error; err != nil {
		return nil, err
	}

	// 按天统计登录次数
	var dailyStats []struct {
		Date          string `json:"date"`
		TotalLogins   int64  `json:"total_logins"`
		SuccessLogins int64  `json:"success_logins"`
		FailedLogins  int64  `json:"failed_logins"`
	}

	if err := s.db.Model(&model.LoginLog{}).
		Select("DATE(login_at) as date, COUNT(*) as total_logins, SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as success_logins, SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_logins").
		Where("login_at >= ?", startTime).
		Group("DATE(login_at)").
		Order("date ASC").
		Scan(&dailyStats).Error; err != nil {
		return nil, err
	}

	stats := map[string]interface{}{
		"period":         fmt.Sprintf("%d天", days),
		"total_logins":   totalLogins,
		"success_logins": successLogins,
		"failed_logins":  failedLogins,
		"success_rate":   float64(successLogins) / float64(totalLogins) * 100,
		"unique_ips":     uniqueIPs,
		"daily_stats":    dailyStats,
	}

	// 避免除零错误
	if totalLogins == 0 {
		stats["success_rate"] = 0.0
	}

	return stats, nil
}

// CleanupOldLoginLogs 清理旧的登录日志
func (s *LoginLogService) CleanupOldLoginLogs(days int) error {
	if days <= 0 {
		days = 180 // 默认保留180天
	}

	cutoffTime := time.Now().AddDate(0, 0, -days)

	// 删除旧日志
	if err := s.db.Where("login_at < ?", cutoffTime).
		Delete(&model.LoginLog{}).Error; err != nil {
		return err
	}

	return nil
}

// getLocationFromIP 从IP地址获取地理位置（简化实现）
func (s *LoginLogService) getLocationFromIP(ipAddress string) string {
	// 检查是否为本地IP
	ip := net.ParseIP(ipAddress)
	if ip == nil {
		return "未知"
	}

	if ip.IsLoopback() {
		return "本地"
	}

	if ip.IsPrivate() {
		return "内网"
	}

	// 这里可以集成第三方IP地理位置查询服务
	// 例如：MaxMind GeoIP、IPinfo、IP2Location等
	// 目前返回简化结果
	if strings.HasPrefix(ipAddress, "192.168.") ||
		strings.HasPrefix(ipAddress, "10.") ||
		strings.HasPrefix(ipAddress, "172.") {
		return "内网"
	}

	return "未知位置"
}

// 辅助函数
func (s *LoginLogService) toLoginLogDTO(log *model.LoginLog) *LoginLogDTO {
	dto := &LoginLogDTO{
		ID:         log.ID,
		UserID:     log.UserID,
		IPAddress:  log.IPAddress,
		UserAgent:  log.UserAgent,
		Location:   log.Location,
		Status:     log.Status,
		Method:     log.Method,
		FailReason: log.FailReason,
		LoginAt:    log.LoginAt,
	}

	// 添加用户信息
	if log.User.ID != 0 {
		dto.Username = log.User.Username
		dto.Email = log.User.Email
	}

	return dto
}

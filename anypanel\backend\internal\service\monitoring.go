package service

import (
	"anypanel/internal/model"
	"encoding/json"
	"fmt"
	"runtime"
	"time"

	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/host"
	"github.com/shirou/gopsutil/v3/load"
	"github.com/shirou/gopsutil/v3/mem"
	"gorm.io/gorm"
)

// MonitoringService 监控服务
type MonitoringService struct {
	db *gorm.DB
}

// NewMonitoringService 创建监控服务
func NewMonitoringService(db *gorm.DB) *MonitoringService {
	return &MonitoringService{
		db: db,
	}
}

// SystemHealth 系统健康状态
type SystemHealth struct {
	Status       string                       `json:"status"` // healthy, degraded, unhealthy
	Timestamp    time.Time                    `json:"timestamp"`
	Version      string                       `json:"version"`
	Uptime       string                       `json:"uptime"`
	Database     HealthCheckResult            `json:"database"`
	Memory       MemoryStats                  `json:"memory"`
	Goroutines   int                          `json:"goroutines"`
	Dependencies map[string]HealthCheckResult `json:"dependencies"`
}

// HealthCheckResult 健康检查结果
type HealthCheckResult struct {
	Status       string        `json:"status"`
	ResponseTime time.Duration `json:"response_time"`
	Message      string        `json:"message,omitempty"`
	Details      interface{}   `json:"details,omitempty"`
}

// MemoryStats 内存统计
type MemoryStats struct {
	Alloc      uint64 `json:"alloc"`       // 已分配内存 (bytes)
	TotalAlloc uint64 `json:"total_alloc"` // 总分配内存 (bytes)
	Sys        uint64 `json:"sys"`         // 系统内存 (bytes)
	NumGC      uint32 `json:"num_gc"`      // GC次数
	GCPauseNs  uint64 `json:"gc_pause_ns"` // GC暂停时间 (ns)
	HeapAlloc  uint64 `json:"heap_alloc"`  // 堆内存 (bytes)
	HeapSys    uint64 `json:"heap_sys"`    // 堆系统内存 (bytes)
	HeapIdle   uint64 `json:"heap_idle"`   // 堆空闲内存 (bytes)
	HeapInuse  uint64 `json:"heap_inuse"`  // 堆使用内存 (bytes)
}

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	Timestamp       time.Time              `json:"timestamp"`
	RequestsTotal   int64                  `json:"requests_total"`
	RequestsPerSec  float64                `json:"requests_per_sec"`
	AvgResponseTime time.Duration          `json:"avg_response_time"`
	ErrorRate       float64                `json:"error_rate"`
	DatabaseMetrics DatabaseMetrics        `json:"database_metrics"`
	BusinessMetrics map[string]interface{} `json:"business_metrics"`
}

// DatabaseMetrics 数据库指标
type DatabaseMetrics struct {
	ConnectionsActive int           `json:"connections_active"`
	ConnectionsIdle   int           `json:"connections_idle"`
	QueryTime         time.Duration `json:"avg_query_time"`
	SlowQueries       int64         `json:"slow_queries"`
}

// MetricRecord 指标记录
type MetricRecord struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	Name      string    `json:"name" gorm:"size:100"`
	Value     float64   `json:"value"`
	Labels    string    `json:"labels" gorm:"type:text"` // JSON格式的标签
	Timestamp time.Time `json:"timestamp"`
	CreatedAt time.Time `json:"created_at"`
}

// AlertRule 告警规则
type AlertRule struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"size:100"`
	MetricName  string    `json:"metric_name" gorm:"size:100"`
	Operator    string    `json:"operator" gorm:"size:10"` // >, <, >=, <=, ==, !=
	Threshold   float64   `json:"threshold"`
	Duration    int       `json:"duration"`                // 持续时间（秒）
	Severity    string    `json:"severity" gorm:"size:20"` // critical, warning, info
	Description string    `json:"description" gorm:"type:text"`
	IsActive    bool      `json:"is_active" gorm:"default:true"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// GetSystemHealth 获取系统健康状态
func (s *MonitoringService) GetSystemHealth() (*SystemHealth, error) {
	startTime := time.Now()

	health := &SystemHealth{
		Status:       "healthy",
		Timestamp:    startTime,
		Version:      "1.0.0", // 从配置或环境变量获取
		Uptime:       time.Since(startTime).String(),
		Goroutines:   runtime.NumGoroutine(),
		Dependencies: make(map[string]HealthCheckResult),
	}

	// 检查数据库健康状态
	health.Database = s.checkDatabaseHealth()
	if health.Database.Status != "healthy" {
		health.Status = "degraded"
	}

	// 获取内存统计
	health.Memory = s.getMemoryStats()

	// 检查其他依赖服务
	// health.Dependencies["redis"] = s.checkRedisHealth()
	// health.Dependencies["external_api"] = s.checkExternalAPIHealth()

	return health, nil
}

// checkDatabaseHealth 检查数据库健康状态
func (s *MonitoringService) checkDatabaseHealth() HealthCheckResult {
	start := time.Now()

	// 执行一个简单的查询
	var count int64
	err := s.db.Model(&model.User{}).Count(&count).Error

	responseTime := time.Since(start)

	if err != nil {
		return HealthCheckResult{
			Status:       "unhealthy",
			ResponseTime: responseTime,
			Message:      fmt.Sprintf("数据库连接失败: %v", err),
		}
	}

	status := "healthy"
	if responseTime > 100*time.Millisecond {
		status = "degraded"
	}

	return HealthCheckResult{
		Status:       status,
		ResponseTime: responseTime,
		Details: map[string]interface{}{
			"user_count": count,
		},
	}
}

// getMemoryStats 获取内存统计
func (s *MonitoringService) getMemoryStats() MemoryStats {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	return MemoryStats{
		Alloc:      m.Alloc,
		TotalAlloc: m.TotalAlloc,
		Sys:        m.Sys,
		NumGC:      m.NumGC,
		GCPauseNs:  m.PauseNs[(m.NumGC+255)%256],
		HeapAlloc:  m.HeapAlloc,
		HeapSys:    m.HeapSys,
		HeapIdle:   m.HeapIdle,
		HeapInuse:  m.HeapInuse,
	}
}

// GetRealSystemUptime 获取真实的系统运行时间
func (s *MonitoringService) GetRealSystemUptime() (string, error) {
	bootTime, err := host.BootTime()
	if err != nil {
		return "未知", err
	}

	uptime := time.Since(time.Unix(int64(bootTime), 0))

	// 格式化为友好的显示格式
	days := int(uptime.Hours()) / 24
	hours := int(uptime.Hours()) % 24
	minutes := int(uptime.Minutes()) % 60

	if days > 0 {
		return fmt.Sprintf("%dd %dh %dm", days, hours, minutes), nil
	} else if hours > 0 {
		return fmt.Sprintf("%dh %dm", hours, minutes), nil
	} else {
		return fmt.Sprintf("%dm", minutes), nil
	}
}

// GetRealSystemLoad 获取真实的系统负载
func (s *MonitoringService) GetRealSystemLoad() (string, error) {
	// 获取CPU使用率
	cpuPercent, err := cpu.Percent(time.Second, false)
	if err != nil {
		return "未知", err
	}

	// 获取内存使用情况
	memInfo, err := mem.VirtualMemory()
	if err != nil {
		return "未知", err
	}

	// 获取系统负载平均值（仅在支持的系统上）
	loadAvg, err := load.Avg()
	var loadStatus string
	if err != nil {
		// 如果无法获取负载平均值，使用CPU和内存使用率来判断
		avgLoad := (cpuPercent[0] + memInfo.UsedPercent) / 2
		if avgLoad > 80 {
			loadStatus = "高负载"
		} else if avgLoad > 60 {
			loadStatus = "中等负载"
		} else {
			loadStatus = "低负载"
		}
	} else {
		// 使用系统负载平均值
		if loadAvg.Load1 > 2.0 {
			loadStatus = "高负载"
		} else if loadAvg.Load1 > 1.0 {
			loadStatus = "中等负载"
		} else {
			loadStatus = "低负载"
		}
	}

	// 返回详细的负载信息
	return fmt.Sprintf("%s (CPU: %.1f%%, 内存: %.1f%%)",
		loadStatus, cpuPercent[0], memInfo.UsedPercent), nil
}

// GetPerformanceMetrics 获取性能指标
func (s *MonitoringService) GetPerformanceMetrics(timeRange time.Duration) (*PerformanceMetrics, error) {
	since := time.Now().Add(-timeRange)

	metrics := &PerformanceMetrics{
		Timestamp:       time.Now(),
		BusinessMetrics: make(map[string]interface{}),
	}

	// 计算请求相关指标
	if err := s.calculateRequestMetrics(metrics, since); err != nil {
		return nil, err
	}

	// 计算数据库指标
	if err := s.calculateDatabaseMetrics(metrics); err != nil {
		return nil, err
	}

	// 计算业务指标
	if err := s.calculateBusinessMetrics(metrics, since); err != nil {
		return nil, err
	}

	return metrics, nil
}

// calculateRequestMetrics 计算请求指标
func (s *MonitoringService) calculateRequestMetrics(metrics *PerformanceMetrics, since time.Time) error {
	// 从安全事件中统计请求
	var totalRequests int64
	var failedRequests int64

	// 计算总请求数（从速率限制记录）
	err := s.db.Model(&model.RateLimitRecord{}).
		Where("window_start > ?", since).
		Select("COALESCE(SUM(request_count), 0)").
		Scan(&totalRequests).Error
	if err != nil {
		return err
	}

	// 计算失败请求数（从安全事件）
	err = s.db.Model(&model.SecurityEvent{}).
		Where("event_type = 'failed_request' AND created_at > ?", since).
		Count(&failedRequests).Error
	if err != nil {
		return err
	}

	duration := time.Since(since).Seconds()
	metrics.RequestsTotal = totalRequests
	metrics.RequestsPerSec = float64(totalRequests) / duration
	if totalRequests > 0 {
		metrics.ErrorRate = float64(failedRequests) / float64(totalRequests) * 100
	}

	return nil
}

// calculateDatabaseMetrics 计算数据库指标
func (s *MonitoringService) calculateDatabaseMetrics(metrics *PerformanceMetrics) error {
	// 获取数据库连接池信息
	sqlDB, err := s.db.DB()
	if err != nil {
		return err
	}

	stats := sqlDB.Stats()
	metrics.DatabaseMetrics = DatabaseMetrics{
		ConnectionsActive: stats.InUse,
		ConnectionsIdle:   stats.Idle,
		// QueryTime 需要通过其他方式计算
		SlowQueries: 0, // 需要从日志或监控中获取
	}

	return nil
}

// calculateBusinessMetrics 计算业务指标
func (s *MonitoringService) calculateBusinessMetrics(metrics *PerformanceMetrics, since time.Time) error {
	// 统计用户相关指标
	var activeUsers int64
	err := s.db.Model(&model.User{}).
		Where("status = 'active'").
		Count(&activeUsers).Error
	if err != nil {
		return err
	}

	// 统计订单相关指标
	var newOrders int64
	var paidOrders int64
	var totalRevenue float64

	err = s.db.Model(&model.Order{}).
		Where("created_at > ?", since).
		Count(&newOrders).Error
	if err != nil {
		return err
	}

	err = s.db.Model(&model.Order{}).
		Where("status = 'paid' AND paid_at > ?", since).
		Count(&paidOrders).Error
	if err != nil {
		return err
	}

	err = s.db.Model(&model.Order{}).
		Where("status = 'paid' AND paid_at > ?", since).
		Select("COALESCE(SUM(amount), 0)").
		Scan(&totalRevenue).Error
	if err != nil {
		return err
	}

	// 统计流量相关指标
	var totalTraffic int64
	err = s.db.Model(&model.TrafficLog{}).
		Where("recorded_at > ?", since).
		Select("COALESCE(SUM(upload + download), 0)").
		Scan(&totalTraffic).Error
	if err != nil {
		return err
	}

	// 统计在线用户
	var onlineUsers int64
	err = s.db.Model(&model.OnlineUser{}).
		Where("last_seen > ?", time.Now().Add(-5*time.Minute)).
		Count(&onlineUsers).Error
	if err != nil {
		return err
	}

	metrics.BusinessMetrics = map[string]interface{}{
		"active_users":  activeUsers,
		"online_users":  onlineUsers,
		"new_orders":    newOrders,
		"paid_orders":   paidOrders,
		"total_revenue": totalRevenue,
		"total_traffic": totalTraffic,
	}

	return nil
}

// RecordMetric 记录指标
func (s *MonitoringService) RecordMetric(name string, value float64, labels map[string]string) error {
	labelsJSON, _ := json.Marshal(labels)

	metric := &MetricRecord{
		Name:      name,
		Value:     value,
		Labels:    string(labelsJSON),
		Timestamp: time.Now(),
		CreatedAt: time.Now(),
	}

	return s.db.Create(metric).Error
}

// GetMetrics 获取指标数据
func (s *MonitoringService) GetMetrics(name string, timeRange time.Duration, labels map[string]string) ([]*MetricRecord, error) {
	var metrics []*MetricRecord

	query := s.db.Where("name = ? AND timestamp > ?", name, time.Now().Add(-timeRange))

	if len(labels) > 0 {
		labelsJSON, _ := json.Marshal(labels)
		query = query.Where("labels = ?", string(labelsJSON))
	}

	err := query.Order("timestamp ASC").Find(&metrics).Error
	return metrics, err
}

// CheckAlerts 检查告警
func (s *MonitoringService) CheckAlerts() ([]*AlertRule, error) {
	var rules []*AlertRule
	err := s.db.Where("is_active = ?", true).Find(&rules).Error
	if err != nil {
		return nil, err
	}

	var triggeredRules []*AlertRule

	for _, rule := range rules {
		triggered, err := s.evaluateAlertRule(rule)
		if err != nil {
			continue
		}

		if triggered {
			triggeredRules = append(triggeredRules, rule)
			// 这里可以发送告警通知
			s.sendAlert(rule)
		}
	}

	return triggeredRules, nil
}

// evaluateAlertRule 评估告警规则
func (s *MonitoringService) evaluateAlertRule(rule *AlertRule) (bool, error) {
	// 获取最近的指标值
	since := time.Now().Add(-time.Duration(rule.Duration) * time.Second)

	var avgValue float64
	err := s.db.Model(&MetricRecord{}).
		Where("name = ? AND timestamp > ?", rule.MetricName, since).
		Select("AVG(value)").
		Scan(&avgValue).Error

	if err != nil {
		return false, err
	}

	// 评估条件
	switch rule.Operator {
	case ">":
		return avgValue > rule.Threshold, nil
	case "<":
		return avgValue < rule.Threshold, nil
	case ">=":
		return avgValue >= rule.Threshold, nil
	case "<=":
		return avgValue <= rule.Threshold, nil
	case "==":
		return avgValue == rule.Threshold, nil
	case "!=":
		return avgValue != rule.Threshold, nil
	default:
		return false, fmt.Errorf("未知的操作符: %s", rule.Operator)
	}
}

// sendAlert 发送告警
func (s *MonitoringService) sendAlert(rule *AlertRule) {
	// 记录告警事件
	event := &model.SecurityEvent{
		EventType:   "alert_triggered",
		Description: fmt.Sprintf("告警规则 '%s' 被触发: %s", rule.Name, rule.Description),
		Severity:    rule.Severity,
		Source:      "monitoring",
		CreatedAt:   time.Now(),
	}

	s.db.Create(event)

	// TODO: 发送邮件、短信、webhook等告警通知
	fmt.Printf("ALERT: %s - %s\n", rule.Name, rule.Description)
}

// CleanupOldMetrics 清理旧的指标数据
func (s *MonitoringService) CleanupOldMetrics(retentionDays int) error {
	cutoff := time.Now().AddDate(0, 0, -retentionDays)
	return s.db.Where("timestamp < ?", cutoff).Delete(&MetricRecord{}).Error
}

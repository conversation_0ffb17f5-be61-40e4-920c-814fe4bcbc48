package database

import (
	"anypanel/internal/model"
	"fmt"
	"log"

	"gorm.io/gorm"
)

// SeedData 初始化种子数据
func SeedData(db *gorm.DB) error {
	log.Println("🌱 开始初始化种子数据...")

	// 创建默认管理员用户
	if err := createDefaultAdmin(db); err != nil {
		return err
	}

	// 创建默认权限组
	if err := createDefaultPermissionGroups(db); err != nil {
		return err
	}

	// 创建示例商品
	if err := createDefaultProducts(db); err != nil {
		return err
	}

	// 创建示例节点
	if err := createDefaultNodes(db); err != nil {
		return err
	}

	// 关联权限组和节点
	if err := associatePermissionGroupNodes(db); err != nil {
		return err
	}

	log.Println("✅ 种子数据初始化完成")
	return nil
}

// createDefaultAdmin 创建默认管理员用户
func createDefaultAdmin(db *gorm.DB) error {
	var count int64
	if err := db.Model(&model.User{}).Where("role = ?", "admin").Count(&count).Error; err != nil {
		return err
	}

	if count > 0 {
		log.Println("管理员用户已存在，跳过创建")
		return nil
	}

	admin := &model.User{
		UUID:         "admin-uuid-12345",
		Username:     "admin",
		Email:        "<EMAIL>",
		PasswordHash: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password
		Role:         "admin",
		Status:       "active",
		TrafficLimit: 0, // 无限流量
		DeviceLimit:  10,
		SpeedLimit:   0, // 无限速度
	}

	if err := db.Create(admin).Error; err != nil {
		return fmt.Errorf("创建管理员用户失败: %w", err)
	}

	log.Println("✅ 创建默认管理员用户")
	return nil
}

// createDefaultPermissionGroups 创建默认权限组
func createDefaultPermissionGroups(db *gorm.DB) error {
	groups := []model.PermissionGroup{
		{
			Name:        "默认组",
			Description: "默认权限组",
			SortOrder:   1,
		},
		{
			Name:        "高级组",
			Description: "高级用户权限组",
			SortOrder:   2,
		},
		{
			Name:        "VIP组",
			Description: "VIP用户权限组",
			SortOrder:   3,
		},
	}

	for _, group := range groups {
		var existing model.PermissionGroup
		if err := db.Where("name = ?", group.Name).First(&existing).Error; err == nil {
			log.Printf("权限组 '%s' 已存在，跳过创建", group.Name)
			continue
		}

		if err := db.Create(&group).Error; err != nil {
			return fmt.Errorf("创建权限组 '%s' 失败: %w", group.Name, err)
		}
		log.Printf("✅ 创建权限组: %s", group.Name)
	}

	return nil
}

// createDefaultProducts 创建默认商品
func createDefaultProducts(db *gorm.DB) error {
	// 获取权限组ID
	var defaultGroup, premiumGroup, vipGroup model.PermissionGroup
	if err := db.Where("name = ?", "默认组").First(&defaultGroup).Error; err != nil {
		return fmt.Errorf("未找到默认权限组: %w", err)
	}
	if err := db.Where("name = ?", "高级组").First(&premiumGroup).Error; err != nil {
		return fmt.Errorf("未找到高级权限组: %w", err)
	}
	if err := db.Where("name = ?", "VIP组").First(&vipGroup).Error; err != nil {
		return fmt.Errorf("未找到VIP权限组: %w", err)
	}

	products := []model.Product{
		{
			Name:              "基础套餐",
			Description:       "100GB流量，30天有效期",
			Price:             9.99,
			TrafficLimit:      107374182400, // 100GB
			DurationDays:      30,
			DeviceLimit:       1,
			SpeedLimit:        0,
			PermissionGroupID: defaultGroup.ID,
			Status:            "active",
			SortOrder:         1,
		},
		{
			Name:              "高级套餐",
			Description:       "500GB流量，90天有效期",
			Price:             24.99,
			TrafficLimit:      536870912000, // 500GB
			DurationDays:      90,
			DeviceLimit:       3,
			SpeedLimit:        0,
			PermissionGroupID: premiumGroup.ID,
			Status:            "active",
			SortOrder:         2,
		},
		{
			Name:              "VIP套餐",
			Description:       "无限流量，365天有效期",
			Price:             99.99,
			TrafficLimit:      0, // 无限流量
			DurationDays:      365,
			DeviceLimit:       5,
			SpeedLimit:        0,
			PermissionGroupID: vipGroup.ID,
			Status:            "active",
			SortOrder:         3,
		},
	}

	for _, product := range products {
		var existing model.Product
		if err := db.Where("name = ?", product.Name).First(&existing).Error; err == nil {
			log.Printf("商品 '%s' 已存在，跳过创建", product.Name)
			continue
		}

		if err := db.Create(&product).Error; err != nil {
			return fmt.Errorf("创建商品 '%s' 失败: %w", product.Name, err)
		}
		log.Printf("✅ 创建商品: %s", product.Name)
	}

	return nil
}

// createDefaultNodes 创建默认节点
func createDefaultNodes(db *gorm.DB) error {
	nodes := []model.Node{
		{
			Name:       "香港节点01",
			Protocol:   "anytls",
			Host:       "hk.example.com",
			Port:       8443,
			Password:   "test-password",
			ServerName: "hk.example.com",
			Status:     "online",
			SortOrder:  1,
		},
		{
			Name:       "日本节点01",
			Protocol:   "anytls",
			Host:       "jp.example.com",
			Port:       8443,
			Password:   "test-password",
			ServerName: "jp.example.com",
			Status:     "online",
			SortOrder:  2,
		},
		{
			Name:       "美国节点01",
			Protocol:   "anytls",
			Host:       "us.example.com",
			Port:       8443,
			Password:   "test-password",
			ServerName: "us.example.com",
			Status:     "online",
			SortOrder:  3,
		},
	}

	for _, node := range nodes {
		var existing model.Node
		if err := db.Where("name = ?", node.Name).First(&existing).Error; err == nil {
			log.Printf("节点 '%s' 已存在，跳过创建", node.Name)
			continue
		}

		if err := db.Create(&node).Error; err != nil {
			return fmt.Errorf("创建节点 '%s' 失败: %w", node.Name, err)
		}
		log.Printf("✅ 创建节点: %s", node.Name)
	}

	return nil
}

// associatePermissionGroupNodes 关联权限组和节点
func associatePermissionGroupNodes(db *gorm.DB) error {
	// 获取所有权限组和节点
	var groups []model.PermissionGroup
	if err := db.Find(&groups).Error; err != nil {
		return err
	}

	var nodes []model.Node
	if err := db.Find(&nodes).Error; err != nil {
		return err
	}

	// 根据组名关联节点
	for _, group := range groups {
		var groupNodes []model.Node
		
		switch group.Name {
		case "默认组":
			// 默认组只能访问前2个节点
			if len(nodes) >= 2 {
				groupNodes = nodes[:2]
			} else {
				groupNodes = nodes
			}
		case "高级组":
			// 高级组可以访问所有节点
			groupNodes = nodes
		case "VIP组":
			// VIP组可以访问所有节点
			groupNodes = nodes
		default:
			continue
		}

		// 清除现有关联
		if err := db.Model(&group).Association("Nodes").Clear(); err != nil {
			return err
		}

		// 建立新关联
		if err := db.Model(&group).Association("Nodes").Append(&groupNodes); err != nil {
			return fmt.Errorf("关联权限组 '%s' 和节点失败: %w", group.Name, err)
		}

		log.Printf("✅ 权限组 '%s' 关联了 %d 个节点", group.Name, len(groupNodes))
	}

	return nil
}

// IsDataSeeded 检查是否已经初始化过数据
func IsDataSeeded(db *gorm.DB) (bool, error) {
	var userCount int64
	if err := db.Model(&model.User{}).Count(&userCount).Error; err != nil {
		return false, err
	}
	
	var groupCount int64
	if err := db.Model(&model.PermissionGroup{}).Count(&groupCount).Error; err != nil {
		return false, err
	}
	
	return userCount > 0 && groupCount > 0, nil
}
package api

import (
	"anypanel/internal/config"
	"anypanel/internal/middleware"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

// NewRouter 创建新的路由器
func NewRouter(cfg *config.Config, db *gorm.DB, rdb *redis.Client) *gin.Engine {
	router := gin.New()

	// 中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// CORS中间件
	router.Use(func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")
		for _, allowedOrigin := range cfg.CORS.AllowedOrigins {
			if origin == allowedOrigin {
				c.<PERSON><PERSON>("Access-Control-Allow-Origin", origin)
				break
			}
		}

		c.<PERSON>er("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.<PERSON><PERSON>("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	})

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "AnyPanel is running",
		})
	})

	// API路由组
	v1 := router.Group("/api/v1")
	{
		// 设置认证路由
		SetupAuthRoutes(v1, db, cfg)

		// 管理员路由
		admin := v1.Group("/admin")
		admin.Use(middleware.AuthMiddleware(db, cfg), middleware.AdminMiddleware())
		{
			SetupAdminRoutes(admin, db, cfg)
		}

		// 用户路由
		user := v1.Group("/user")
		user.Use(middleware.AuthMiddleware(db, cfg))
		{
			SetupUserRoutes(user, db, cfg)
		}

		// V2bX兼容API路由
		SetupV2bXRoutes(v1, db, cfg)

		// 权限组路由
		SetupPermissionGroupRoutes(v1, db)

		// 节点路由
		SetupNodeRoutes(v1, db)

		// 商品系统路由
		SetupProductRoutes(v1, db)

		// 流量统计和监控路由
		SetupTrafficRoutes(v1, db)
		SetupOnlineUserRoutes(v1, db)

		// 支付路由
		SetupPaymentRoutes(v1, db)

		// 订阅路由
		SetupSubscriptionRoutes(v1, db)

		// 支持系统路由
		SetupSupportRoutes(v1, db)

		// 安全系统路由
		SetupSecurityRoutes(v1, db)

		// 监控系统路由
		SetupMonitoringRoutes(v1, db)

		// 系统配置路由
		SetupSystemConfigRoutes(v1, db)
	}

	return router
}

// SetupAdminRoutes 设置管理员路由
func SetupAdminRoutes(router *gin.RouterGroup, db *gorm.DB, cfg *config.Config) {
	// 创建AdminAPI实例
	adminAPI := NewAdminAPI(db, cfg)

	// 系统管理路由（移除硬编码，使用真实数据）
	system := router.Group("/system")
	{
		system.GET("/stats", adminAPI.GetSystemStats)
		system.GET("/user-stats", adminAPI.GetSystemStats) // 添加用户统计路由
		system.GET("/traffic-stats", adminAPI.GetTrafficStats)
		system.GET("/online-user-stats", adminAPI.GetOnlineUserStats)
		system.GET("/user-growth", adminAPI.GetUserGrowthStats)
		system.GET("/traffic-detailed-stats", adminAPI.GetDetailedTrafficStats)
		system.GET("/traffic-ranking", adminAPI.GetTrafficRanking)
	}

	// 订单统计路由（新增）
	orders := router.Group("/orders")
	{
		orders.GET("/stats", adminAPI.GetOrderRevenueStats)
	}

	// 产品统计路由（新增）
	products := router.Group("/products")
	{
		products.GET("/stats", adminAPI.GetProductStats)
	}

	// 管理员仪表板
	router.GET("/dashboard", func(c *gin.Context) {
		c.JSON(200, gin.H{"message": "Admin dashboard"})
	})
}

// SetupTrafficRoutes 设置流量统计路由
func SetupTrafficRoutes(router *gin.RouterGroup, db *gorm.DB) {
	traffic := router.Group("/traffic")
	traffic.Use(middleware.AuthMiddlewareCompat(db))
	{
		traffic.GET("/stats", func(c *gin.Context) {
			c.JSON(200, gin.H{"message": "Traffic stats"})
		})
	}
}

// SetupOnlineUserRoutes 设置在线用户路由
func SetupOnlineUserRoutes(router *gin.RouterGroup, db *gorm.DB) {
	online := router.Group("/online")
	online.Use(middleware.AuthMiddlewareCompat(db))
	{
		online.GET("/stats", func(c *gin.Context) {
			c.JSON(200, gin.H{"message": "Online user stats"})
		})
	}
}

// SetupPaymentRoutes 设置支付路由
func SetupPaymentRoutes(router *gin.RouterGroup, db *gorm.DB) {
	payment := router.Group("/payment")
	payment.Use(middleware.AuthMiddlewareCompat(db))
	{
		payment.POST("/create", func(c *gin.Context) {
			c.JSON(200, gin.H{"message": "Create payment"})
		})
	}
}

package api

import (
	"anypanel/internal/service"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

type TrafficAPI struct {
	trafficService *service.TrafficService
}

func NewTrafficAPI(trafficService *service.TrafficService) *TrafficAPI {
	return &TrafficAPI{
		trafficService: trafficService,
	}
}

// GetTrafficStats 获取流量统计
func (api *TrafficAPI) GetTrafficStats(c *gin.Context) {
	var req service.TrafficReportRequest
	
	// 解析查询参数
	if startTime := c.Query("start_time"); startTime != "" {
		if t, err := time.Parse("2006-01-02", startTime); err == nil {
			req.StartTime = t
		}
	}
	
	if endTime := c.Query("end_time"); endTime != "" {
		if t, err := time.Parse("2006-01-02", endTime); err == nil {
			req.EndTime = t
		}
	}
	
	if userID := c.Query("user_id"); userID != "" {
		if id, err := strconv.ParseUint(userID, 10, 32); err == nil {
			id32 := uint(id)
			req.UserID = &id32
		}
	}
	
	if nodeID := c.Query("node_id"); nodeID != "" {
		if id, err := strconv.ParseUint(nodeID, 10, 32); err == nil {
			id32 := uint(id)
			req.NodeID = &id32
		}
	}
	
	req.GroupBy = c.Query("group_by")

	// 获取流量统计
	report, err := api.trafficService.GetTrafficStats(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取流量统计失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取流量统计成功",
		"data":    report,
	})
}

// GetTrafficTrends 获取流量趋势
func (api *TrafficAPI) GetTrafficTrends(c *gin.Context) {
	daysStr := c.DefaultQuery("days", "7")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days <= 0 {
		days = 7
	}

	trends, err := api.trafficService.GetTrafficTrends(days)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取流量趋势失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取流量趋势成功",
		"data":    trends,
	})
}

// GetUserTrafficSummary 获取用户流量汇总
func (api *TrafficAPI) GetUserTrafficSummary(c *gin.Context) {
	summary, err := api.trafficService.GetUserTrafficSummary()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取用户流量汇总失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取用户流量汇总成功",
		"data":    summary,
	})
}

// GetNodeTrafficSummary 获取节点流量汇总
func (api *TrafficAPI) GetNodeTrafficSummary(c *gin.Context) {
	summary, err := api.trafficService.GetNodeTrafficSummary()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取节点流量汇总失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取节点流量汇总成功",
		"data":    summary,
	})
}

// GetRealtimeTrafficStats 获取实时流量统计
func (api *TrafficAPI) GetRealtimeTrafficStats(c *gin.Context) {
	stats, err := api.trafficService.GetRealtimeTrafficStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取实时流量统计失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取实时流量统计成功",
		"data":    stats,
	})
}

// CheckTrafficLimits 检查流量限制
func (api *TrafficAPI) CheckTrafficLimits(c *gin.Context) {
	err := api.trafficService.CheckTrafficLimits()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "检查流量限制失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "流量限制检查完成",
	})
}

// AutoHandleTrafficLimitExceeded 自动处理流量超限
func (api *TrafficAPI) AutoHandleTrafficLimitExceeded(c *gin.Context) {
	err := api.trafficService.AutoHandleTrafficLimitExceeded()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "自动处理流量超限失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "自动处理流量超限完成",
	})
}

// CleanupOldTrafficLogs 清理旧的流量日志
func (api *TrafficAPI) CleanupOldTrafficLogs(c *gin.Context) {
	daysStr := c.DefaultQuery("days", "90")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days <= 0 {
		days = 90
	}

	err = api.trafficService.CleanupOldTrafficLogs(days)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "清理流量日志失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "清理流量日志完成",
	})
}

// ExportTrafficReport 导出流量报表
func (api *TrafficAPI) ExportTrafficReport(c *gin.Context) {
	var req service.TrafficReportRequest
	
	// 解析查询参数
	if startTime := c.Query("start_time"); startTime != "" {
		if t, err := time.Parse("2006-01-02", startTime); err == nil {
			req.StartTime = t
		}
	}
	
	if endTime := c.Query("end_time"); endTime != "" {
		if t, err := time.Parse("2006-01-02", endTime); err == nil {
			req.EndTime = t
		}
	}
	
	if userID := c.Query("user_id"); userID != "" {
		if id, err := strconv.ParseUint(userID, 10, 32); err == nil {
			id32 := uint(id)
			req.UserID = &id32
		}
	}
	
	if nodeID := c.Query("node_id"); nodeID != "" {
		if id, err := strconv.ParseUint(nodeID, 10, 32); err == nil {
			id32 := uint(id)
			req.NodeID = &id32
		}
	}

	// 获取CSV数据
	csvData, err := api.trafficService.ExportTrafficReport(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "导出流量报表失败",
			"error":   err.Error(),
		})
		return
	}

	// 设置CSV响应头
	c.Header("Content-Type", "text/csv")
	c.Header("Content-Disposition", "attachment; filename=traffic_report.csv")
	
	// 返回CSV数据
	for _, row := range csvData {
		c.Writer.WriteString("\"" + row[0] + "\"")
		for i := 1; i < len(row); i++ {
			c.Writer.WriteString(",\"" + row[i] + "\"")
		}
		c.Writer.WriteString("\n")
	}
}
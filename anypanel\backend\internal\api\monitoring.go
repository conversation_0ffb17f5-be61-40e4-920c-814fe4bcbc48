package api

import (
	"anypanel/internal/middleware"
	"anypanel/internal/service"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// MonitoringHandler 监控处理器
type MonitoringHandler struct {
	monitoringService *service.MonitoringService
}

// NewMonitoringHandler 创建监控处理器
func NewMonitoringHandler(db *gorm.DB) *MonitoringHandler {
	return &MonitoringHandler{
		monitoringService: service.NewMonitoringService(db),
	}
}

// GetSystemHealth 获取系统健康状态
func (h *MonitoringHandler) GetSystemHealth(c *gin.Context) {
	health, err := h.monitoringService.GetSystemHealth()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取系统健康状态失败",
			"details": err.Error(),
		})
		return
	}

	c.<PERSON>(http.StatusOK, health)
}

// GetPerformanceMetrics 获取性能指标
func (h *MonitoringHandler) GetPerformanceMetrics(c *gin.Context) {
	// 解析时间范围参数
	timeRangeStr := c.DefaultQuery("time_range", "1h")
	timeRange, err := time.ParseDuration(timeRangeStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的时间范围",
			"details": err.Error(),
		})
		return
	}

	metrics, err := h.monitoringService.GetPerformanceMetrics(timeRange)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取性能指标失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, metrics)
}

// GetMetrics 获取指标数据
func (h *MonitoringHandler) GetMetrics(c *gin.Context) {
	name := c.Query("name")
	if name == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "指标名称不能为空",
		})
		return
	}

	// 解析时间范围
	timeRangeStr := c.DefaultQuery("time_range", "1h")
	timeRange, err := time.ParseDuration(timeRangeStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的时间范围",
			"details": err.Error(),
		})
		return
	}

	// 解析标签
	labels := make(map[string]string)
	if labelStr := c.Query("labels"); labelStr != "" {
		// TODO: 解析标签字符串
	}

	metrics, err := h.monitoringService.GetMetrics(name, timeRange, labels)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取指标数据失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"metrics": metrics,
		"total": len(metrics),
	})
}

// RecordMetricRequest 记录指标请求
type RecordMetricRequest struct {
	Name   string            `json:"name" binding:"required"`
	Value  float64           `json:"value" binding:"required"`
	Labels map[string]string `json:"labels"`
}

// RecordMetric 记录指标
func (h *MonitoringHandler) RecordMetric(c *gin.Context) {
	var req RecordMetricRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "参数错误",
			"details": err.Error(),
		})
		return
	}

	err := h.monitoringService.RecordMetric(req.Name, req.Value, req.Labels)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "记录指标失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "指标记录成功",
	})
}

// CheckAlerts 检查告警
func (h *MonitoringHandler) CheckAlerts(c *gin.Context) {
	alerts, err := h.monitoringService.CheckAlerts()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "检查告警失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"alerts": alerts,
		"total": len(alerts),
	})
}

// GetDashboardData 获取仪表板数据
func (h *MonitoringHandler) GetDashboardData(c *gin.Context) {
	// 获取基础健康状态
	health, err := h.monitoringService.GetSystemHealth()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取系统健康状态失败",
			"details": err.Error(),
		})
		return
	}

	// 获取最近1小时的性能指标
	metrics, err := h.monitoringService.GetPerformanceMetrics(time.Hour)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取性能指标失败",
			"details": err.Error(),
		})
		return
	}

	// 检查告警
	alerts, err := h.monitoringService.CheckAlerts()
	if err != nil {
		// 告警检查失败不应该影响整个接口
		alerts = []*service.AlertRule{}
	}

	c.JSON(http.StatusOK, gin.H{
		"health":  health,
		"metrics": metrics,
		"alerts":  alerts,
		"timestamp": time.Now(),
	})
}

// CleanupMetrics 清理旧指标
func (h *MonitoringHandler) CleanupMetrics(c *gin.Context) {
	retentionDaysStr := c.DefaultQuery("retention_days", "30")
	retentionDays, err := strconv.Atoi(retentionDaysStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "无效的保留天数",
			"details": err.Error(),
		})
		return
	}

	err = h.monitoringService.CleanupOldMetrics(retentionDays)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "清理指标失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "指标清理完成",
	})
}

// GetSystemStatus 获取系统状态页面数据
func (h *MonitoringHandler) GetSystemStatus(c *gin.Context) {
	// 这是一个公开接口，用于状态页面
	health, err := h.monitoringService.GetSystemHealth()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"status": "unknown",
			"message": "无法获取系统状态",
		})
		return
	}

	// 只返回必要的状态信息，不暴露敏感数据
	status := gin.H{
		"status": health.Status,
		"timestamp": health.Timestamp,
		"uptime": health.Uptime,
		"services": gin.H{
			"database": health.Database.Status,
		},
	}

	// 根据状态设置HTTP状态码
	httpStatus := http.StatusOK
	if health.Status == "degraded" {
		httpStatus = http.StatusPartialContent
	} else if health.Status == "unhealthy" {
		httpStatus = http.StatusServiceUnavailable
	}

	c.JSON(httpStatus, status)
}

// SetupMonitoringRoutes 设置监控路由
func SetupMonitoringRoutes(v1 *gin.RouterGroup, db *gorm.DB) {
	monitoringHandler := NewMonitoringHandler(db)

	// 公开的系统状态端点
	v1.GET("/status", monitoringHandler.GetSystemStatus)

	// 管理员监控路由
	monitoring := v1.Group("/monitoring")
	monitoring.Use(middleware.AuthMiddlewareCompat(db), middleware.AdminMiddleware())
	{
		// 系统健康状态
		monitoring.GET("/health", monitoringHandler.GetSystemHealth)

		// 性能指标
		monitoring.GET("/metrics", monitoringHandler.GetMetrics)
		monitoring.GET("/performance", monitoringHandler.GetPerformanceMetrics)
		monitoring.POST("/metrics", monitoringHandler.RecordMetric)

		// 告警管理
		monitoring.GET("/alerts", monitoringHandler.CheckAlerts)

		// 仪表板数据
		monitoring.GET("/dashboard", monitoringHandler.GetDashboardData)

		// 运维操作
		monitoring.POST("/cleanup", monitoringHandler.CleanupMetrics)
	}
}
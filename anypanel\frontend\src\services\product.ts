import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import apiClient, { PaginatedResponse, PaginationParams } from './api';

// 权限组基础信息
export interface PermissionGroupInfo {
  id: number;
  name: string;
  description: string;
  sort_order: number;
}

// 商品数据类型
export interface Product {
  id: number;
  name: string;
  description: string;
  price: number;
  traffic_limit: number; // 字节
  duration_days: number;
  device_limit: number;
  speed_limit: number; // Mbps，0表示不限速
  permission_group_id: number;
  permission_group: PermissionGroupInfo;
  status: 'active' | 'inactive';
  sort_order: number;
  created_at: string;
  updated_at: string;
}

// 商品创建表单类型
export interface ProductForm {
  name: string;
  description?: string;
  price: number;
  traffic_limit: number;
  duration_days: number;
  device_limit: number;
  speed_limit: number;
  permission_group_id: number;
  status: 'active' | 'inactive';
  sort_order: number;
}

// 商品查询参数
export interface ProductQueryParams extends PaginationParams {
  search?: string;
  status?: string;
  sort_by?: string;
  sort_order?: string;
}

// 商品统计数据
export interface ProductStats {
  total_products: number;
  active_products: number;
  total_sales: number;
  total_revenue: number;
  avg_price: number;
  popular_products: Array<{
    id: number;
    name: string;
    sales_count: number;
    revenue: number;
  }>;
}

class ProductService {
  // 获取商品列表
  async getProducts(params: ProductQueryParams): Promise<PaginatedResponse<Product>> {
    // 转换分页参数以匹配后端期望的格式
    const backendParams = {
      ...params,
      page: params.current || 1,
      page_size: params.pageSize || 20,
    };
    // 删除前端的分页参数，避免冲突
    delete backendParams.current;
    delete backendParams.pageSize;

    const response = await apiClient.get('/products', backendParams);
    // 转换后端响应格式为前端期望的分页格式
    return {
      list: response.products || [],
      total: response.total || 0,
      current: params.current || 1,
      pageSize: params.pageSize || 20
    };
  }

  // 获取所有商品（不分页）
  async getAllProducts(): Promise<Product[]> {
    return apiClient.get('/products/all');
  }

  // 获取单个商品详情
  async getProduct(id: number): Promise<Product> {
    return apiClient.get(`/products/${id}`);
  }

  // 创建商品
  async createProduct(data: ProductForm): Promise<Product> {
    return apiClient.post('/products', data);
  }

  // 更新商品
  async updateProduct(id: number, data: Partial<ProductForm>): Promise<Product> {
    return apiClient.put(`/products/${id}`, data);
  }

  // 删除商品
  async deleteProduct(id: number): Promise<void> {
    return apiClient.delete(`/products/${id}`);
  }

  // 更新商品状态
  async updateProductStatus(id: number, status: 'active' | 'inactive'): Promise<Product> {
    return apiClient.put(`/products/${id}/status`, { status });
  }

  // 批量删除商品
  async batchDeleteProducts(ids: number[]): Promise<void> {
    // 批量删除需要循环调用单个删除API
    await Promise.all(ids.map(id => this.deleteProduct(id)));
  }

  // 获取商品统计
  async getProductStats(): Promise<ProductStats> {
    return apiClient.get('/products/stats');
  }

  // 格式化流量显示
  formatTraffic(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(unitIndex > 0 ? 2 : 0)} ${units[unitIndex]}`;
  }

  // 格式化价格显示
  formatPrice(price: number): string {
    return `¥${price.toFixed(2)}`;
  }

  // 格式化时长显示
  formatDuration(days: number): string {
    if (days < 30) {
      return `${days}天`;
    } else if (days < 365) {
      const months = Math.floor(days / 30);
      const remainingDays = days % 30;
      return remainingDays > 0 ? `${months}个月${remainingDays}天` : `${months}个月`;
    } else {
      const years = Math.floor(days / 365);
      const remainingDays = days % 365;
      return remainingDays > 0 ? `${years}年${remainingDays}天` : `${years}年`;
    }
  }

  // 格式化速度显示
  formatSpeed(speedMbps: number): string {
    if (speedMbps === 0) {
      return '不限速';
    }
    return `${speedMbps} Mbps`;
  }

  // 获取状态显示文本
  getStatusText(status: string): string {
    const statusMap: Record<string, string> = {
      active: '启用',
      inactive: '禁用',
    };
    return statusMap[status] || status;
  }

  // 获取状态颜色
  getStatusColor(status: string): string {
    const colorMap: Record<string, string> = {
      active: 'success',
      inactive: 'default',
    };
    return colorMap[status] || 'default';
  }
}

export const productService = new ProductService();

// React Query Hooks
export const useProducts = (params: ProductQueryParams) => {
  return useQuery({
    queryKey: ['products', params],
    queryFn: () => productService.getProducts(params),
    keepPreviousData: true,
  });
};

export const useProduct = (id: number) => {
  return useQuery({
    queryKey: ['product', id],
    queryFn: () => productService.getProduct(id),
    enabled: !!id,
  });
};

export const useProductStats = () => {
  return useQuery({
    queryKey: ['product-stats'],
    queryFn: () => productService.getProductStats(),
    refetchInterval: 5 * 60 * 1000, // 5分钟刷新
  });
};

// Mutations
export const useCreateProduct = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: ProductForm) => productService.createProduct(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      queryClient.invalidateQueries({ queryKey: ['product-stats'] });
      message.success('商品创建成功');
    },
    onError: () => {
      message.error('商品创建失败');
    },
  });
};

export const useUpdateProduct = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: Partial<ProductForm> }) => 
      productService.updateProduct(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      queryClient.invalidateQueries({ queryKey: ['product'] });
      queryClient.invalidateQueries({ queryKey: ['product-stats'] });
      message.success('商品更新成功');
    },
    onError: () => {
      message.error('商品更新失败');
    },
  });
};

export const useDeleteProduct = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: number) => productService.deleteProduct(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      queryClient.invalidateQueries({ queryKey: ['product-stats'] });
      message.success('商品删除成功');
    },
    onError: () => {
      message.error('商品删除失败');
    },
  });
};

export const useBatchDeleteProducts = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (ids: number[]) => productService.batchDeleteProducts(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      queryClient.invalidateQueries({ queryKey: ['product-stats'] });
      message.success('批量删除成功');
    },
    onError: () => {
      message.error('批量删除失败');
    },
  });
};

export const useUpdateProductStatus = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, status }: { id: number; status: 'active' | 'inactive' }) => 
      productService.updateProductStatus(id, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['products'] });
      queryClient.invalidateQueries({ queryKey: ['product'] });
      message.success('商品状态更新成功');
    },
    onError: () => {
      message.error('商品状态更新失败');
    },
  });
};

// 获取所有商品（不分页，用于下拉选择）
export const useAllProducts = () => {
  return useQuery({
    queryKey: ['all-products'],
    queryFn: () => productService.getAllProducts(),
    staleTime: 5 * 60 * 1000, // 5分钟
  });
};

export default productService;
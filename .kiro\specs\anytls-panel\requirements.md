# Requirements Document

## Introduction

本项目旨在开发一个基于AnyTLS协议的多用户管理面板，类似于V2board项目，但专门针对AnyTLS协议进行优化。该面板需要与V2bX后端完全兼容，提供用户管理、节点管理、流量统计、订阅生成等核心功能。项目将采用现代化的Web技术栈，提供直观易用的管理界面。

## Requirements

### Requirement 1

**User Story:** 作为系统管理员，我希望能够管理用户账户，以便控制谁可以使用代理服务

#### Acceptance Criteria

1. WHEN 管理员访问用户管理页面 THEN 系统 SHALL 显示所有用户的列表，包括用户ID、用户名、邮箱、注册时间、到期时间、流量使用情况
2. WHEN 管理员点击添加用户按钮 THEN 系统 SHALL 显示用户创建表单，包含用户名、邮箱、密码、到期时间、流量限制等字段
3. WHEN 管理员提交用户创建表单 THEN 系统 SHALL 验证输入数据并创建新用户账户，生成唯一的UUID
4. WHEN 管理员选择用户并点击编辑 THEN 系统 SHALL 允许修改用户信息，包括到期时间、流量限制、状态等
5. WHEN 管理员选择用户并点击删除 THEN 系统 SHALL 显示确认对话框，确认后删除用户及其相关数据
6. WHEN 管理员搜索用户 THEN 系统 SHALL 支持按用户名、邮箱进行模糊搜索

### Requirement 2

**User Story:** 作为系统管理员，我希望能够管理代理节点，当前专注于AnyTLS协议，以便提供稳定的代理服务

#### Acceptance Criteria

1. WHEN 管理员访问节点管理页面 THEN 系统 SHALL 显示所有节点的列表，包括协议类型、节点名称、地址、端口、状态、在线用户数
2. WHEN 管理员点击添加节点按钮 THEN 系统 SHALL 显示节点创建表单，当前支持AnyTLS协议配置，包含填充策略等特定参数
3. WHEN 管理员提交节点创建表单 THEN 系统 SHALL 验证协议配置并创建新节点，生成节点ID和认证令牌
4. WHEN 管理员编辑节点配置 THEN 系统 SHALL 允许修改节点参数，包括服务器信息、协议特定配置、限速设置等
5. WHEN 管理员删除节点 THEN 系统 SHALL 确认操作并删除节点配置，同时通知相关用户
6. WHEN 节点状态发生变化 THEN 系统 SHALL 实时更新节点状态显示，包括在线/离线状态
7. IF 未来需要支持其他协议 THEN 系统架构 SHALL 支持通过扩展模块添加新的协议类型

### Requirement 3

**User Story:** 作为普通用户，我希望能够获取我的订阅链接和配置信息，以便在客户端中使用代理服务

#### Acceptance Criteria

1. WHEN 用户登录到用户面板 THEN 系统 SHALL 显示用户的基本信息，包括到期时间、剩余流量、订阅链接
2. WHEN 用户点击订阅链接 THEN 系统 SHALL 生成包含用户可用节点的配置文件，当前支持AnyTLS格式
3. WHEN 用户访问订阅URL THEN 系统 SHALL 根据客户端类型返回相应格式的配置信息，支持Clash、Sing-box、V2Ray等主流客户端
4. WHEN 用户请求Clash订阅 THEN 系统 SHALL 返回YAML格式的Clash配置文件，包含代理组和规则
5. WHEN 用户请求Sing-box订阅 THEN 系统 SHALL 返回JSON格式的Sing-box配置文件
6. WHEN 用户请求V2Ray订阅 THEN 系统 SHALL 返回Base64编码的URI列表
7. WHEN 用户请求单个节点配置 THEN 系统 SHALL 根据节点协议类型提供相应的URI格式配置链接
8. WHEN 用户的订阅信息发生变化 THEN 系统 SHALL 自动更新订阅内容，无需手动刷新
9. IF 用户账户已过期或流量耗尽 THEN 系统 SHALL 在订阅中排除该用户，返回空配置
10. IF 系统未来支持多种协议 THEN 订阅生成 SHALL 能够处理不同协议的配置格式
11. WHEN 用户在面板中查看订阅信息 THEN 系统 SHALL 显示不同客户端的订阅链接和二维码
12. WHEN 用户选择特定客户端类型 THEN 系统 SHALL 提供该客户端的配置指南和下载链接

### Requirement 4

**User Story:** 作为系统管理员，我希望能够监控用户流量使用情况，以便进行资源管理和计费

#### Acceptance Criteria

1. WHEN V2bX后端上报用户流量数据 THEN 系统 SHALL 接收并存储流量统计信息
2. WHEN 管理员查看流量统计 THEN 系统 SHALL 显示用户的上传、下载流量，支持按时间段筛选
3. WHEN 用户流量超过限制 THEN 系统 SHALL 自动禁用该用户的服务访问
4. WHEN 管理员查看实时在线用户 THEN 系统 SHALL 显示当前在线用户列表和连接信息
5. WHEN 系统生成流量报表 THEN 系统 SHALL 支持导出CSV格式的流量统计数据
6. WHEN 流量数据需要清理 THEN 系统 SHALL 提供数据归档和清理功能

### Requirement 5

**User Story:** 作为V2bX后端服务，我需要通过API获取节点配置和用户信息，以便正常提供代理服务

#### Acceptance Criteria

1. WHEN V2bX请求节点配置 THEN 系统 SHALL 通过`/api/v1/server/UniProxy/config`接口返回AnyTLS节点配置
2. WHEN V2bX请求用户列表 THEN 系统 SHALL 通过`/api/v1/server/UniProxy/user`接口返回该节点的有效用户列表
3. WHEN V2bX上报用户流量 THEN 系统 SHALL 通过`/api/v1/server/UniProxy/push`接口接收并处理流量数据
4. WHEN V2bX上报在线用户 THEN 系统 SHALL 通过`/api/v1/server/UniProxy/alive`接口接收在线用户信息
5. WHEN V2bX查询在线用户数 THEN 系统 SHALL 通过`/api/v1/server/UniProxy/alivelist`接口返回用户在线统计
6. IF API请求包含无效的认证信息 THEN 系统 SHALL 返回401未授权错误

### Requirement 6

**User Story:** 作为系统管理员，我希望系统具有良好的安全性和稳定性，以便安全可靠地运行

#### Acceptance Criteria

1. WHEN 用户尝试登录 THEN 系统 SHALL 验证用户凭据，支持密码哈希存储和会话管理
2. WHEN 检测到异常访问 THEN 系统 SHALL 实施速率限制和IP封禁机制
3. WHEN 系统运行时 THEN 系统 SHALL 记录详细的操作日志，包括用户操作和API调用
4. WHEN 数据库操作失败 THEN 系统 SHALL 提供错误恢复机制和数据备份功能
5. WHEN 系统配置发生变化 THEN 系统 SHALL 支持配置热重载，无需重启服务
6. WHEN 部署系统 THEN 系统 SHALL 支持Docker容器化部署和环境变量配置

### Requirement 7

**User Story:** 作为普通用户，我希望有一个简洁易用的用户界面，以便方便地管理我的账户和查看使用情况

#### Acceptance Criteria

1. WHEN 用户访问用户面板 THEN 系统 SHALL 提供响应式的Web界面，支持桌面和移动设备
2. WHEN 用户查看账户信息 THEN 系统 SHALL 显示流量使用图表和到期时间倒计时
3. WHEN 用户需要修改密码 THEN 系统 SHALL 提供安全的密码修改功能
4. WHEN 用户查看使用教程 THEN 系统 SHALL 提供AnyTLS客户端配置指南和下载链接
5. WHEN 用户遇到问题 THEN 系统 SHALL 提供在线帮助文档和联系方式
6. IF 用户账户状态异常 THEN 系统 SHALL 在界面上显示明确的状态提示和解决建议

### Requirement 8

**User Story:** 作为系统管理员，我希望能够管理权限组和订阅商品，以便实现订阅服务的商业化运营

#### Acceptance Criteria

1. WHEN 管理员创建权限组 THEN 系统 SHALL 允许设置权限组名称、描述和包含的节点列表
2. WHEN 管理员管理节点权限 THEN 系统 SHALL 支持将节点分配到不同的权限组中
3. WHEN 管理员创建订阅商品 THEN 系统 SHALL 允许设置商品名称、价格、流量限制、有效期、权限组等参数
4. WHEN 管理员编辑商品信息 THEN 系统 SHALL 支持修改商品的所有属性，包括价格和权限组
5. WHEN 用户购买订阅商品 THEN 系统 SHALL 根据商品配置更新用户的流量限制、到期时间和节点访问权限
6. WHEN 用户查看可购买商品 THEN 系统 SHALL 显示商品列表，包含价格、流量、有效期、可用节点等信息
7. IF 用户权限组发生变化 THEN 系统 SHALL 自动更新用户可访问的节点列表和订阅配置

### Requirement 9

**User Story:** 作为普通用户，我希望能够购买和管理订阅商品，以便获得代理服务的使用权限

#### Acceptance Criteria

1. WHEN 用户浏览商品页面 THEN 系统 SHALL 显示可购买的订阅商品，包含详细的规格说明
2. WHEN 用户选择商品 THEN 系统 SHALL 显示商品详情，包括价格、流量、有效期、可用节点列表
3. WHEN 用户购买商品 THEN 系统 SHALL 处理订单并更新用户的订阅权限
4. WHEN 用户查看订阅状态 THEN 系统 SHALL 显示当前订阅的商品信息、剩余流量、到期时间
5. WHEN 用户续费订阅 THEN 系统 SHALL 支持购买相同或不同的商品来延长服务
6. IF 用户订阅过期 THEN 系统 SHALL 限制用户访问权限并提示续费

### Requirement 10

**User Story:** 作为开发团队，我希望使用现代化的前端框架和组件库，以便快速开发出高质量的管理界面

#### Acceptance Criteria

1. WHEN 开发前端界面 THEN 系统 SHALL 使用Ant Design Pro作为基础框架，提供企业级管理后台解决方案
2. WHEN 实现数据表格 THEN 系统 SHALL 使用ProTable组件，支持搜索、筛选、分页、排序等功能
3. WHEN 创建表单界面 THEN 系统 SHALL 使用ProForm组件，提供完整的表单验证和数据处理
4. WHEN 展示统计数据 THEN 系统 SHALL 使用ProCard和Statistic组件，创建美观的数据展示卡片
5. WHEN 处理异步数据 THEN 系统 SHALL 使用React Query进行服务端状态管理，提供缓存和错误处理
6. WHEN 管理客户端状态 THEN 系统 SHALL 使用Zustand进行轻量级状态管理，避免过度复杂化
7. IF 需要图表展示 THEN 系统 SHALL 使用Ant Design Charts提供丰富的数据可视化功能
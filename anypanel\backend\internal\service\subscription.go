package service

import (
	"anypanel/internal/model"
	"anypanel/internal/types"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type SubscriptionService struct {
	db             *gorm.DB
	productService *ProductService
	userService    *UserService
	orderService   *OrderService
}

func NewSubscriptionService(db *gorm.DB, productService *ProductService, userService *UserService, orderService *OrderService) *SubscriptionService {
	return &SubscriptionService{
		db:             db,
		productService: productService,
		userService:    userService,
		orderService:   orderService,
	}
}

// SubscriptionQueryRequest 订阅查询请求
type SubscriptionQueryRequest struct {
	Page      int    `form:"page" binding:"min=1"`
	PageSize  int    `form:"page_size" binding:"min=1,max=100"`
	UserID    uint   `form:"user_id"`
	ProductID uint   `form:"product_id"`
	Status    string `form:"status"`
	SortBy    string `form:"sort_by"`
	SortOrder string `form:"sort_order"`
}

// SubscriptionQueryResponse 订阅查询响应
type SubscriptionQueryResponse struct {
	Total         int64                  `json:"total"`
	Subscriptions []*UserSubscriptionDTO `json:"subscriptions"`
}

// UserSubscriptionDTO 用户订阅数据传输对象
type UserSubscriptionDTO struct {
	ID        uint             `json:"id"`
	UserID    uint             `json:"user_id"`
	User      types.UserDTO    `json:"user"`
	ProductID uint             `json:"product_id"`
	Product   types.ProductDTO `json:"product"`
	OrderID   string           `json:"order_id"`
	Status    string           `json:"status"`
	StartedAt string           `json:"started_at"`
	ExpiredAt string           `json:"expired_at"`
	CreatedAt string           `json:"created_at"`
	UpdatedAt string           `json:"updated_at"`
	DaysLeft  int              `json:"days_left"`
	IsExpired bool             `json:"is_expired"`
	IsActive  bool             `json:"is_active"`
}

// GetUserSubscriptionByID 根据ID获取用户订阅
func (s *SubscriptionService) GetUserSubscriptionByID(subscriptionID uint) (*model.UserSubscription, error) {
	var subscription model.UserSubscription
	if err := s.db.Preload("User").Preload("Product").Preload("Product.PermissionGroup").First(&subscription, subscriptionID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("订阅不存在")
		}
		return nil, err
	}
	return &subscription, nil
}

// GetUserActiveSubscription 获取用户活跃订阅
func (s *SubscriptionService) GetUserActiveSubscription(userID uint) (*model.UserSubscription, error) {
	var subscription model.UserSubscription
	if err := s.db.Where("user_id = ? AND status = ?", userID, "active").
		Preload("User").Preload("Product").Preload("Product.PermissionGroup").
		First(&subscription).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户没有活跃订阅")
		}
		return nil, err
	}
	return &subscription, nil
}

// QuerySubscriptions 查询订阅列表
func (s *SubscriptionService) QuerySubscriptions(req *SubscriptionQueryRequest) (*SubscriptionQueryResponse, error) {
	query := s.db.Model(&model.UserSubscription{})

	// 用户筛选
	if req.UserID > 0 {
		query = query.Where("user_id = ?", req.UserID)
	}

	// 商品筛选
	if req.ProductID > 0 {
		query = query.Where("product_id = ?", req.ProductID)
	}

	// 状态筛选
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 排序
	sortBy := req.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}
	sortOrder := req.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// 分页
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// 查询订阅
	var subscriptions []model.UserSubscription
	if err := query.Preload("User").Preload("Product").Preload("Product.PermissionGroup").Find(&subscriptions).Error; err != nil {
		return nil, err
	}

	// 转换为DTO
	subscriptionDTOs := make([]*UserSubscriptionDTO, len(subscriptions))
	for i, subscription := range subscriptions {
		subscriptionDTOs[i] = s.toUserSubscriptionDTO(&subscription)
	}

	return &SubscriptionQueryResponse{
		Total:         total,
		Subscriptions: subscriptionDTOs,
	}, nil
}

// CreateSubscriptionFromOrder 从订单创建订阅
func (s *SubscriptionService) CreateSubscriptionFromOrder(orderID string) (*UserSubscriptionDTO, error) {
	order, err := s.orderService.GetOrderByOrderID(orderID)
	if err != nil {
		return nil, err
	}

	// 检查订单状态
	if order.Status != "paid" {
		return nil, errors.New("订单未支付")
	}

	// 检查是否已存在该订单的订阅
	var existingSubscription model.UserSubscription
	if err := s.db.Where("order_id = ?", orderID).First(&existingSubscription).Error; err == nil {
		return nil, errors.New("该订单已创建订阅")
	}

	// 计算订阅时间
	startedAt := time.Now()
	expiredAt := startedAt.AddDate(0, 0, order.Product.DurationDays)

	// 停用用户之前的活跃订阅
	if err := s.deactivateUserSubscriptions(order.UserID); err != nil {
		return nil, err
	}

	// 创建订阅
	subscription := &model.UserSubscription{
		UserID:    order.UserID,
		ProductID: order.ProductID,
		OrderID:   orderID,
		Status:    "active",
		StartedAt: startedAt,
		ExpiredAt: expiredAt,
	}

	if err := subscription.Validate(); err != nil {
		return nil, err
	}

	if err := s.db.Create(subscription).Error; err != nil {
		return nil, err
	}

	// 更新用户信息
	user, err := s.userService.GetUserByID(order.UserID)
	if err != nil {
		return nil, err
	}

	// 更新用户的流量限制和设备限制
	user.TrafficLimit = order.Product.TrafficLimit
	user.DeviceLimit = order.Product.DeviceLimit
	user.SpeedLimit = order.Product.SpeedLimit
	user.ExpiredAt = &expiredAt
	user.Status = "active"

	if err := s.db.Save(user).Error; err != nil {
		return nil, err
	}

	// 重新加载以包含关联数据
	if err := s.db.Preload("User").Preload("Product").Preload("Product.PermissionGroup").First(subscription, subscription.ID).Error; err != nil {
		return nil, err
	}

	return s.toUserSubscriptionDTO(subscription), nil
}

// UpdateSubscription 更新订阅
func (s *SubscriptionService) UpdateSubscription(subscriptionID uint, status string) (*UserSubscriptionDTO, error) {
	subscription, err := s.GetUserSubscriptionByID(subscriptionID)
	if err != nil {
		return nil, err
	}

	subscription.Status = status
	if err := subscription.Validate(); err != nil {
		return nil, err
	}

	if err := s.db.Save(subscription).Error; err != nil {
		return nil, err
	}

	// 重新加载以包含关联数据
	if err := s.db.Preload("User").Preload("Product").Preload("Product.PermissionGroup").First(subscription, subscription.ID).Error; err != nil {
		return nil, err
	}

	return s.toUserSubscriptionDTO(subscription), nil
}

// CancelSubscription 取消订阅
func (s *SubscriptionService) CancelSubscription(subscriptionID uint) error {
	subscription, err := s.GetUserSubscriptionByID(subscriptionID)
	if err != nil {
		return err
	}

	if subscription.Status != "active" {
		return errors.New("只能取消活跃的订阅")
	}

	subscription.Status = "cancelled"
	return s.db.Save(subscription).Error
}

// ExtendSubscription 延长订阅
func (s *SubscriptionService) ExtendSubscription(subscriptionID uint, days int) (*UserSubscriptionDTO, error) {
	subscription, err := s.GetUserSubscriptionByID(subscriptionID)
	if err != nil {
		return nil, err
	}

	if subscription.Status != "active" {
		return nil, errors.New("只能延长活跃的订阅")
	}

	// 延长过期时间
	subscription.ExpiredAt = subscription.ExpiredAt.AddDate(0, 0, days)
	if err := subscription.Validate(); err != nil {
		return nil, err
	}

	if err := s.db.Save(subscription).Error; err != nil {
		return nil, err
	}

	// 更新用户过期时间
	user, err := s.userService.GetUserByID(subscription.UserID)
	if err != nil {
		return nil, err
	}
	user.ExpiredAt = &subscription.ExpiredAt
	if err := s.db.Save(user).Error; err != nil {
		return nil, err
	}

	// 重新加载以包含关联数据
	if err := s.db.Preload("User").Preload("Product").Preload("Product.PermissionGroup").First(subscription, subscription.ID).Error; err != nil {
		return nil, err
	}

	return s.toUserSubscriptionDTO(subscription), nil
}

// CheckExpiredSubscriptions 检查过期订阅
func (s *SubscriptionService) CheckExpiredSubscriptions() error {
	// 查找所有过期但状态仍为活跃的订阅
	var expiredSubscriptions []model.UserSubscription
	if err := s.db.Where("status = ? AND expired_at < ?", "active", time.Now()).
		Find(&expiredSubscriptions).Error; err != nil {
		return err
	}

	// 批量更新过期订阅
	for _, subscription := range expiredSubscriptions {
		subscription.Status = "expired"
		if err := s.db.Save(&subscription).Error; err != nil {
			continue // 继续处理其他订阅
		}

		// 更新用户状态
		user, err := s.userService.GetUserByID(subscription.UserID)
		if err != nil {
			continue
		}

		// 检查用户是否还有其他活跃订阅
		var activeSubscriptions int64
		if err := s.db.Model(&model.UserSubscription{}).
			Where("user_id = ? AND status = ?", user.ID, "active").
			Count(&activeSubscriptions).Error; err != nil {
			continue
		}

		// 如果没有其他活跃订阅，将用户状态设为过期
		if activeSubscriptions == 0 {
			user.Status = "expired"
			if err := s.db.Save(user).Error; err != nil {
				continue
			}
		}
	}

	return nil
}

// GetUserSubscriptions 获取用户的所有订阅
func (s *SubscriptionService) GetUserSubscriptions(userID uint) ([]*model.UserSubscription, error) {
	var subscriptions []model.UserSubscription
	if err := s.db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Find(&subscriptions).Error; err != nil {
		return nil, err
	}

	subscriptionPtrs := make([]*model.UserSubscription, len(subscriptions))
	for i := range subscriptions {
		subscriptionPtrs[i] = &subscriptions[i]
	}

	return subscriptionPtrs, nil
}

// GetSubscriptionStats 获取订阅统计信息
func (s *SubscriptionService) GetSubscriptionStats() (map[string]interface{}, error) {
	var totalSubscriptions int64
	var activeSubscriptions int64
	var expiredSubscriptions int64
	var cancelledSubscriptions int64

	// 获取订阅总数
	if err := s.db.Model(&model.UserSubscription{}).Count(&totalSubscriptions).Error; err != nil {
		return nil, err
	}

	// 获取活跃订阅数
	if err := s.db.Model(&model.UserSubscription{}).Where("status = ?", "active").Count(&activeSubscriptions).Error; err != nil {
		return nil, err
	}

	// 获取过期订阅数
	if err := s.db.Model(&model.UserSubscription{}).Where("status = ?", "expired").Count(&expiredSubscriptions).Error; err != nil {
		return nil, err
	}

	// 获取已取消订阅数
	if err := s.db.Model(&model.UserSubscription{}).Where("status = ?", "cancelled").Count(&cancelledSubscriptions).Error; err != nil {
		return nil, err
	}

	stats := map[string]interface{}{
		"total_subscriptions":     totalSubscriptions,
		"active_subscriptions":    activeSubscriptions,
		"expired_subscriptions":   expiredSubscriptions,
		"cancelled_subscriptions": cancelledSubscriptions,
	}

	return stats, nil
}

// GetUserSubscriptionStats 获取用户订阅统计
func (s *SubscriptionService) GetUserSubscriptionStats(userID uint) (map[string]interface{}, error) {
	var totalSubscriptions int64
	var activeSubscriptions int64
	var expiredSubscriptions int64

	// 获取用户订阅总数
	if err := s.db.Model(&model.UserSubscription{}).Where("user_id = ?", userID).Count(&totalSubscriptions).Error; err != nil {
		return nil, err
	}

	// 获取用户活跃订阅数
	if err := s.db.Model(&model.UserSubscription{}).Where("user_id = ? AND status = ?", userID, "active").Count(&activeSubscriptions).Error; err != nil {
		return nil, err
	}

	// 获取用户过期订阅数
	if err := s.db.Model(&model.UserSubscription{}).Where("user_id = ? AND status = ?", userID, "expired").Count(&expiredSubscriptions).Error; err != nil {
		return nil, err
	}

	// 获取当前活跃订阅
	activeSubscription, err := s.GetUserActiveSubscription(userID)
	if err != nil {
		// 用户没有活跃订阅
		return map[string]interface{}{
			"total_subscriptions":     totalSubscriptions,
			"active_subscriptions":    activeSubscriptions,
			"expired_subscriptions":   expiredSubscriptions,
			"has_active_subscription": false,
		}, nil
	}

	stats := map[string]interface{}{
		"total_subscriptions":     totalSubscriptions,
		"active_subscriptions":    activeSubscriptions,
		"expired_subscriptions":   expiredSubscriptions,
		"has_active_subscription": true,
		"current_subscription": map[string]interface{}{
			"id":           activeSubscription.ID,
			"product_name": activeSubscription.Product.Name,
			"started_at":   activeSubscription.StartedAt.Format("2006-01-02 15:04:05"),
			"expired_at":   activeSubscription.ExpiredAt.Format("2006-01-02 15:04:05"),
			"days_left":    activeSubscription.GetRemainingDays(),
		},
	}

	return stats, nil
}

// GetSubscriptionsByProduct 根据商品获取订阅
func (s *SubscriptionService) GetSubscriptionsByProduct(productID uint) ([]*model.UserSubscription, error) {
	var subscriptions []model.UserSubscription
	if err := s.db.Where("product_id = ?", productID).
		Order("created_at DESC").
		Find(&subscriptions).Error; err != nil {
		return nil, err
	}

	subscriptionPtrs := make([]*model.UserSubscription, len(subscriptions))
	for i := range subscriptions {
		subscriptionPtrs[i] = &subscriptions[i]
	}

	return subscriptionPtrs, nil
}

// deactivateUserSubscriptions 停用用户的所有活跃订阅
func (s *SubscriptionService) deactivateUserSubscriptions(userID uint) error {
	return s.db.Model(&model.UserSubscription{}).
		Where("user_id = ? AND status = ?", userID, "active").
		Update("status", "cancelled").Error
}

// 辅助函数
func (s *SubscriptionService) toUserSubscriptionDTO(subscription *model.UserSubscription) *UserSubscriptionDTO {
	return &UserSubscriptionDTO{
		ID:     subscription.ID,
		UserID: subscription.UserID,
		User: types.UserDTO{
			ID:           subscription.User.ID,
			UUID:         subscription.User.UUID,
			Username:     subscription.User.Username,
			Email:        subscription.User.Email,
			Role:         subscription.User.Role,
			Status:       subscription.User.Status,
			TrafficLimit: subscription.User.TrafficLimit,
			TrafficUsed:  subscription.User.TrafficUsed,
			DeviceLimit:  subscription.User.DeviceLimit,
			ExpiredAt: func() string {
				if subscription.User.ExpiredAt != nil {
					return subscription.User.ExpiredAt.Format("2006-01-02 15:04:05")
				}
				return ""
			}(),
			CreatedAt: subscription.User.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt: subscription.User.UpdatedAt.Format("2006-01-02 15:04:05"),
		},
		ProductID: subscription.ProductID,
		Product: types.ProductDTO{
			ID:           subscription.Product.ID,
			Name:         subscription.Product.Name,
			Description:  subscription.Product.Description,
			Price:        subscription.Product.Price,
			TrafficLimit: subscription.Product.TrafficLimit,
			DurationDays: subscription.Product.DurationDays,
			DeviceLimit:  subscription.Product.DeviceLimit,
			SpeedLimit:   subscription.Product.SpeedLimit,
			GroupID:      subscription.Product.PermissionGroupID,
			GroupName:    subscription.Product.PermissionGroup.Name,
			Status:       subscription.Product.Status,
			SortOrder:    subscription.Product.SortOrder,
			CreatedAt:    subscription.Product.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:    subscription.Product.UpdatedAt.Format("2006-01-02 15:04:05"),
		},
		OrderID:   subscription.OrderID,
		Status:    subscription.Status,
		StartedAt: subscription.StartedAt.Format("2006-01-02 15:04:05"),
		ExpiredAt: subscription.ExpiredAt.Format("2006-01-02 15:04:05"),
		CreatedAt: subscription.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt: subscription.UpdatedAt.Format("2006-01-02 15:04:05"),
		DaysLeft:  subscription.GetRemainingDays(),
		IsExpired: subscription.IsExpired(),
		IsActive:  subscription.IsActive(),
	}
}

package api

import (
	"anypanel/internal/config"
	"anypanel/internal/middleware"
	"anypanel/internal/service"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// UserHandler 用户处理器
type UserHandler struct {
	userService     *service.UserService
	authService     *service.AuthService
	trafficService  *service.TrafficService
	loginLogService *service.LoginLogService
}

func NewUserHandler(db *gorm.DB) *UserHandler {
	userService := service.NewUserService(db)
	nodeService := service.NewNodeService(db)
	v2bxService := service.NewV2bXService(db, nodeService, userService, "default-token")

	return &UserHandler{
		userService:     userService,
		authService:     service.NewAuthService(db, nil), // 简化处理
		trafficService:  service.NewTrafficService(db, userService, nodeService, v2bxService),
		loginLogService: service.NewLoginLogService(db),
	}
}

// GetUserByID 根据ID获取用户
func (h *UserHandler) GetUserByID(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的用户ID",
			"message": err.Error(),
		})
		return
	}

	user, err := h.userService.GetUserByID(uint(userID))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "获取用户失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    user,
	})
}

// GetUsers 获取用户列表
func (h *UserHandler) GetUsers(c *gin.Context) {
	var req service.UserQueryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"message": err.Error(),
		})
		return
	}

	resp, err := h.userService.QueryUsers(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "查询用户失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "查询成功",
		"data":    resp,
	})
}

// CreateUser 创建用户
func (h *UserHandler) CreateUser(c *gin.Context) {
	var req service.UserCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"message": err.Error(),
		})
		return
	}

	user, err := h.userService.CreateUser(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "创建用户失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "创建成功",
		"data":    user,
	})
}

// UpdateUser 更新用户
func (h *UserHandler) UpdateUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的用户ID",
			"message": err.Error(),
		})
		return
	}

	var req service.UserUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"message": err.Error(),
		})
		return
	}

	user, err := h.userService.UpdateUser(uint(userID), &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "更新用户失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "更新成功",
		"data":    user,
	})
}

// DeleteUser 删除用户
func (h *UserHandler) DeleteUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的用户ID",
			"message": err.Error(),
		})
		return
	}

	if err := h.userService.DeleteUser(uint(userID)); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "删除用户失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除成功",
	})
}

// UpdateUserStatus 更新用户状态
func (h *UserHandler) UpdateUserStatus(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的用户ID",
			"message": err.Error(),
		})
		return
	}

	var req struct {
		Status string `json:"status" binding:"required,oneof=active inactive expired"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"message": err.Error(),
		})
		return
	}

	if err := h.userService.UpdateUserStatus(uint(userID), req.Status); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "更新用户状态失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "更新成功",
	})
}

// GetUserTrafficStats 获取用户流量统计
func (h *UserHandler) GetUserTrafficStats(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的用户ID",
			"message": err.Error(),
		})
		return
	}

	stats, err := h.userService.GetUserTrafficStats(uint(userID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取流量统计失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    stats,
	})
}

// ResetUserPassword 重置用户密码
func (h *UserHandler) ResetUserPassword(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的用户ID",
			"message": err.Error(),
		})
		return
	}

	var req struct {
		NewPassword string `json:"new_password" binding:"required,min=6"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"message": err.Error(),
		})
		return
	}

	if err := h.userService.ResetUserPassword(uint(userID), req.NewPassword); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "重置密码失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "重置成功",
	})
}

// GetActiveUsers 获取活跃用户列表
func (h *UserHandler) GetActiveUsers(c *gin.Context) {
	users, err := h.userService.GetActiveUsers()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取活跃用户失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    users,
	})
}

// GetExpiredUsers 获取过期用户列表
func (h *UserHandler) GetExpiredUsers(c *gin.Context) {
	users, err := h.userService.GetExpiredUsers()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取过期用户失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    users,
	})
}

// BatchUpdateUsers 批量更新用户
func (h *UserHandler) BatchUpdateUsers(c *gin.Context) {
	var req struct {
		UserIDs []uint `json:"user_ids" binding:"required,min=1"`
		Action  string `json:"action" binding:"required,oneof=activate deactivate delete"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"message": err.Error(),
		})
		return
	}

	var successCount int
	var errors []string

	for _, userID := range req.UserIDs {
		var err error
		switch req.Action {
		case "activate":
			err = h.userService.UpdateUserStatus(userID, "active")
		case "deactivate":
			err = h.userService.UpdateUserStatus(userID, "inactive")
		case "delete":
			err = h.userService.DeleteUser(userID)
		}

		if err != nil {
			errors = append(errors, fmt.Sprintf("用户ID %d: %v", userID, err))
		} else {
			successCount++
		}
	}

	response := gin.H{
		"success": true,
		"message": fmt.Sprintf("批量操作完成，成功: %d, 失败: %d", successCount, len(errors)),
		"data": gin.H{
			"success_count": successCount,
			"failed_count":  len(errors),
			"errors":        errors,
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetUserProfile 获取当前用户资料
func (h *UserHandler) GetUserProfile(c *gin.Context) {
	user, exists := middleware.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "用户未认证",
			"message": "请先登录",
		})
		return
	}

	profile, err := h.authService.GetUserProfile(user.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取用户资料失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    profile,
	})
}

// GetUserTrafficHistory 获取当前用户流量历史
func (h *UserHandler) GetUserTrafficHistory(c *gin.Context) {
	user, exists := middleware.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "用户未认证",
			"message": "请先登录",
		})
		return
	}

	// 获取查询参数
	days := 30 // 默认30天
	if daysStr := c.Query("days"); daysStr != "" {
		if d, err := strconv.Atoi(daysStr); err == nil && d > 0 {
			days = d
		}
	}

	// 获取流量趋势数据
	trends, err := h.trafficService.GetTrafficTrends(days)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取流量历史失败",
			"message": err.Error(),
		})
		return
	}

	// 构建用户特定的流量统计请求
	req := &service.TrafficReportRequest{
		StartTime: time.Now().AddDate(0, 0, -days),
		EndTime:   time.Now(),
		UserID:    &user.ID,
		GroupBy:   "day",
	}

	// 获取用户特定的流量统计
	userTraffic, err := h.trafficService.GetTrafficStats(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取用户流量统计失败",
			"message": err.Error(),
		})
		return
	}

	response := gin.H{
		"success": true,
		"message": "获取成功",
		"data": gin.H{
			"trends":       trends,
			"user_traffic": userTraffic,
			"period_days":  days,
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetUserLoginLogs 获取当前用户登录日志
func (h *UserHandler) GetUserLoginLogs(c *gin.Context) {
	user, exists := middleware.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "用户未认证",
			"message": "请先登录",
		})
		return
	}

	var req service.LoginLogQueryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"message": err.Error(),
		})
		return
	}

	logs, err := h.loginLogService.GetUserLoginLogs(user.ID, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取登录日志失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    logs,
	})
}

// GetUserLoginStats 获取当前用户登录统计
func (h *UserHandler) GetUserLoginStats(c *gin.Context) {
	user, exists := middleware.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "用户未认证",
			"message": "请先登录",
		})
		return
	}

	// 获取查询参数
	days := 30 // 默认30天
	if daysStr := c.Query("days"); daysStr != "" {
		if d, err := strconv.Atoi(daysStr); err == nil && d > 0 {
			days = d
		}
	}

	stats, err := h.loginLogService.GetLoginStats(&user.ID, days)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取登录统计失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    stats,
	})
}

// SetupUserRoutes 设置用户路由
func SetupUserRoutes(router *gin.RouterGroup, db *gorm.DB, cfg *config.Config) {
	userHandler := NewUserHandler(db)

	users := router.Group("/users")
	{
		// 公开路由（如果有）

		// 需要认证的路由
		users.Use(middleware.AuthMiddleware(db, cfg))
		{
			// 用户自己的操作
			users.GET("/profile", userHandler.GetUserProfile)
			users.GET("/traffic-history", userHandler.GetUserTrafficHistory)
			users.GET("/login-logs", userHandler.GetUserLoginLogs)
			users.GET("/login-stats", userHandler.GetUserLoginStats)

			// 管理员权限路由
			admin := users.Group("/admin")
			admin.Use(middleware.AdminMiddleware())
			{
				admin.GET("", userHandler.GetUsers)
				admin.POST("", userHandler.CreateUser)
				admin.GET("/:id", userHandler.GetUserByID)
				admin.PUT("/:id", userHandler.UpdateUser)
				admin.DELETE("/:id", userHandler.DeleteUser)
				admin.PUT("/:id/status", userHandler.UpdateUserStatus)
				admin.GET("/:id/traffic-stats", userHandler.GetUserTrafficStats)
				admin.POST("/:id/reset-password", userHandler.ResetUserPassword)
				admin.GET("/active", userHandler.GetActiveUsers)
				admin.GET("/expired", userHandler.GetExpiredUsers)
				admin.POST("/batch", userHandler.BatchUpdateUsers)
			}
		}
	}
}

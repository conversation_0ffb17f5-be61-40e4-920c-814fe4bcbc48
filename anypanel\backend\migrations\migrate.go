package main

import (
	"anypanel/internal/config"
	"anypanel/internal/model"
	"flag"
	"fmt"
	"log"
	"os"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	var (
		configPath = flag.String("config", "config.yaml", "配置文件路径")
		action     = flag.String("action", "migrate", "操作类型: migrate, rollback, seed")
		force      = flag.Bool("force", false, "强制执行，跳过确认")
	)
	flag.Parse()

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 连接数据库
	db, err := connectDatabase(cfg)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	switch *action {
	case "migrate":
		if err := runMigration(db, *force); err != nil {
			log.Fatalf("数据库迁移失败: %v", err)
		}
		log.Println("数据库迁移完成")
	case "rollback":
		if err := runRollback(db, *force); err != nil {
			log.Fatalf("数据库回滚失败: %v", err)
		}
		log.Println("数据库回滚完成")
	case "seed":
		if err := runSeeder(db, *force); err != nil {
			log.Fatalf("数据库种子数据导入失败: %v", err)
		}
		log.Println("种子数据导入完成")
	case "fresh":
		if err := runFresh(db, *force); err != nil {
			log.Fatalf("数据库重建失败: %v", err)
		}
		log.Println("数据库重建完成")
	default:
		fmt.Printf("未知操作: %s\n", *action)
		fmt.Println("可用操作: migrate, rollback, seed, fresh")
		os.Exit(1)
	}
}

// runMigration 运行数据库迁移
func runMigration(db *gorm.DB, force bool) error {
	if !force {
		fmt.Print("确认执行数据库迁移？(y/N): ")
		var response string
		fmt.Scanln(&response)
		if response != "y" && response != "Y" {
			fmt.Println("迁移已取消")
			return nil
		}
	}

	log.Println("开始数据库迁移...")

	// 自动迁移所有模型
	if err := AutoMigrate(db); err != nil {
		return fmt.Errorf("自动迁移失败: %w", err)
	}

	// 创建索引
	if err := CreateIndexes(db); err != nil {
		return fmt.Errorf("创建索引失败: %w", err)
	}

	return nil
}

// runRollback 回滚数据库
func runRollback(db *gorm.DB, force bool) error {
	if !force {
		fmt.Print("确认回滚数据库？这将删除所有表！(y/N): ")
		var response string
		fmt.Scanln(&response)
		if response != "y" && response != "Y" {
			fmt.Println("回滚已取消")
			return nil
		}
	}

	log.Println("开始数据库回滚...")
	
	// TODO: 实现回滚逻辑
	log.Println("回滚功能待实现")
	
	return nil
}

// runSeeder 运行种子数据
func runSeeder(db *gorm.DB, force bool) error {
	if !force {
		fmt.Print("确认导入种子数据？(y/N): ")
		var response string
		fmt.Scanln(&response)
		if response != "y" && response != "Y" {
			fmt.Println("种子数据导入已取消")
			return nil
		}
	}

	log.Println("开始导入种子数据...")
	
	// TODO: 实现种子数据导入
	log.Println("种子数据导入功能待实现")
	
	return nil
}

// runFresh 重建数据库
func runFresh(db *gorm.DB, force bool) error {
	if !force {
		fmt.Print("确认重建数据库？这将删除所有数据！(y/N): ")
		var response string
		fmt.Scanln(&response)
		if response != "y" && response != "Y" {
			fmt.Println("重建已取消")
			return nil
		}
	}

	log.Println("开始重建数据库...")
	
	// 先回滚
	if err := runRollback(db, true); err != nil {
		return err
	}
	
	// 再迁移
	if err := runMigration(db, true); err != nil {
		return err
	}
	
	// 导入种子数据
	if err := runSeeder(db, true); err != nil {
		return err
	}
	
	return nil
}

// AutoMigrate 自动迁移所有模型
func AutoMigrate(db *gorm.DB) error {
	// 导入model包以确保所有模型被注册
	// 这些是需要迁移的模型，按照依赖关系排序
	models := []interface{}{
		// 基础用户和权限
		&model.User{},
		&model.PermissionGroup{},
		
		// 节点和服务器
		&model.Node{},
		&model.Server{},
		
		// 商品和订单
		&model.Product{},
		&model.Order{},
		&model.Subscription{},
		
		// 流量和统计
		&model.TrafficRecord{},
		&model.UserTrafficStat{},
		
		// 安全相关
		&model.SecurityEvent{},
		&model.IPBanRecord{},
		&model.AuditLog{},
		&model.RateLimitRecord{},
		
		// 监控相关
		&model.MetricRecord{},
		&model.AlertRule{},
		&model.SystemLog{},
		
		// 工单和公告
		&model.Ticket{},
		&model.Announcement{},
	}

	for _, model := range models {
		if err := db.AutoMigrate(model); err != nil {
			return fmt.Errorf("迁移模型 %T 失败: %w", model, err)
		}
		log.Printf("模型 %T 迁移完成", model)
	}

	return nil
}

// CreateIndexes 创建数据库索引
func CreateIndexes(db *gorm.DB) error {
	indexes := []string{
		// 用户表索引
		"CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
		"CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
		"CREATE INDEX IF NOT EXISTS idx_users_status ON users(status)",
		"CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at)",
		
		// 节点表索引
		"CREATE INDEX IF NOT EXISTS idx_nodes_status ON nodes(status)",
		"CREATE INDEX IF NOT EXISTS idx_nodes_type ON nodes(type)",
		"CREATE INDEX IF NOT EXISTS idx_nodes_group_id ON nodes(group_id)",
		
		// 订单表索引
		"CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id)",
		"CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status)",
		"CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at)",
		
		// 订阅表索引
		"CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id)",
		"CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status)",
		"CREATE INDEX IF NOT EXISTS idx_subscriptions_expired_at ON subscriptions(expired_at)",
		
		// 流量记录表索引
		"CREATE INDEX IF NOT EXISTS idx_traffic_records_user_id ON traffic_records(user_id)",
		"CREATE INDEX IF NOT EXISTS idx_traffic_records_node_id ON traffic_records(node_id)",
		"CREATE INDEX IF NOT EXISTS idx_traffic_records_created_at ON traffic_records(created_at)",
		
		// 用户流量统计表索引
		"CREATE INDEX IF NOT EXISTS idx_user_traffic_stats_user_id ON user_traffic_stats(user_id)",
		"CREATE INDEX IF NOT EXISTS idx_user_traffic_stats_date ON user_traffic_stats(date)",
		
		// 安全事件表索引
		"CREATE INDEX IF NOT EXISTS idx_security_events_user_id ON security_events(user_id)",
		"CREATE INDEX IF NOT EXISTS idx_security_events_ip_address ON security_events(ip_address)",
		"CREATE INDEX IF NOT EXISTS idx_security_events_event_type ON security_events(event_type)",
		"CREATE INDEX IF NOT EXISTS idx_security_events_created_at ON security_events(created_at)",
		
		// IP封禁记录表索引
		"CREATE INDEX IF NOT EXISTS idx_ip_ban_records_ip_address ON ip_ban_records(ip_address)",
		"CREATE INDEX IF NOT EXISTS idx_ip_ban_records_status ON ip_ban_records(status)",
		"CREATE INDEX IF NOT EXISTS idx_ip_ban_records_expired_at ON ip_ban_records(expired_at)",
		
		// 审计日志表索引
		"CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id)",
		"CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action)",
		"CREATE INDEX IF NOT EXISTS idx_audit_logs_ip_address ON audit_logs(ip_address)",
		"CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at)",
		
		// 限流记录表索引
		"CREATE INDEX IF NOT EXISTS idx_rate_limit_records_identifier ON rate_limit_records(identifier)",
		"CREATE INDEX IF NOT EXISTS idx_rate_limit_records_resource ON rate_limit_records(resource)",
		"CREATE INDEX IF NOT EXISTS idx_rate_limit_records_created_at ON rate_limit_records(created_at)",
		
		// 指标记录表索引
		"CREATE INDEX IF NOT EXISTS idx_metric_records_metric_name ON metric_records(metric_name)",
		"CREATE INDEX IF NOT EXISTS idx_metric_records_timestamp ON metric_records(timestamp)",
		"CREATE INDEX IF NOT EXISTS idx_metric_records_source ON metric_records(source)",
		
		// 告警规则表索引
		"CREATE INDEX IF NOT EXISTS idx_alert_rules_metric_name ON alert_rules(metric_name)",
		"CREATE INDEX IF NOT EXISTS idx_alert_rules_enabled ON alert_rules(enabled)",
		
		// 系统日志表索引
		"CREATE INDEX IF NOT EXISTS idx_system_logs_level ON system_logs(level)",
		"CREATE INDEX IF NOT EXISTS idx_system_logs_component ON system_logs(component)",
		"CREATE INDEX IF NOT EXISTS idx_system_logs_created_at ON system_logs(created_at)",
		
		// 工单表索引
		"CREATE INDEX IF NOT EXISTS idx_tickets_user_id ON tickets(user_id)",
		"CREATE INDEX IF NOT EXISTS idx_tickets_status ON tickets(status)",
		"CREATE INDEX IF NOT EXISTS idx_tickets_priority ON tickets(priority)",
		"CREATE INDEX IF NOT EXISTS idx_tickets_created_at ON tickets(created_at)",
		
		// 公告表索引
		"CREATE INDEX IF NOT EXISTS idx_announcements_status ON announcements(status)",
		"CREATE INDEX IF NOT EXISTS idx_announcements_priority ON announcements(priority)",
		"CREATE INDEX IF NOT EXISTS idx_announcements_created_at ON announcements(created_at)",
	}

	for _, indexSQL := range indexes {
		if err := db.Exec(indexSQL).Error; err != nil {
			log.Printf("创建索引失败: %s, 错误: %v", indexSQL, err)
			// 继续创建其他索引，不因为一个索引失败而停止
		} else {
			log.Printf("索引创建成功: %s", indexSQL)
		}
	}

	return nil
}

// connectDatabase 连接数据库
func connectDatabase(cfg *config.Config) (*gorm.DB, error) {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
		cfg.DB.Username,
		cfg.DB.Password,
		cfg.DB.Host,
		cfg.DB.Port,
		cfg.DB.Database,
		cfg.DB.Charset,
	)

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, err
	}

	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	sqlDB.SetMaxIdleConns(cfg.DB.MaxIdleConns)
	sqlDB.SetMaxOpenConns(cfg.DB.MaxOpenConns)
	sqlDB.SetConnMaxLifetime(time.Duration(cfg.DB.ConnMaxLifetime) * time.Second)

	return db, nil
}

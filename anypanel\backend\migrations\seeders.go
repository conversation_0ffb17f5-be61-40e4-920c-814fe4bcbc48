package main

import (
	"anypanel/internal/model"
	"fmt"
	"log"

	"gorm.io/gorm"
)

// runMigration 执行数据库迁移
func runMigration(db *gorm.DB, force bool) error {
	if !force {
		fmt.Print("确定要执行数据库迁移吗? (y/N): ")
		var response string
		fmt.Scanln(&response)
		if response != "y" && response != "Y" {
			fmt.Println("操作已取消")
			return nil
		}
	}

	log.Println("开始执行数据库迁移...")

	// 执行自动迁移
	if err := model.AutoMigrate(db); err != nil {
		return fmt.Errorf("自动迁移失败: %v", err)
	}

	// 创建索引
	if err := model.CreateIndexes(db); err != nil {
		return fmt.Errorf("创建索引失败: %v", err)
	}

	log.Println("数据库迁移执行完成")
	return nil
}

// runRollback 执行数据库回滚
func runRollback(db *gorm.DB, force bool) error {
	if !force {
		fmt.Print("确定要回滚数据库吗? 这将删除所有表! (y/N): ")
		var response string
		fmt.Scanln(&response)
		if response != "y" && response != "Y" {
			fmt.Println("操作已取消")
			return nil
		}
	}

	log.Println("开始执行数据库回滚...")

	// 删除所有表（按依赖关系逆序）
	tables := []interface{}{
		&model.HelpDocument{},
		&model.HelpCategory{},
		&model.UserFeedback{},
		&model.Announcement{},
		&model.TicketReply{},
		&model.Ticket{},
		&model.FAQ{},
		&model.FAQCategory{},
		&model.SystemLog{},
		&model.AlertRule{},
		&model.MetricRecord{},
		&model.RateLimitRecord{},
		&model.AuditLog{},
		&model.IPBanRecord{},
		&model.SecurityEvent{},
		&model.PaymentLog{},
		&model.Payment{},
		&model.OnlineUser{},
		&model.TrafficLog{},
		&model.UserSubscription{},
		&model.Order{},
		&model.Product{},
		&model.PermissionGroup{},
		&model.Node{},
		&model.User{},
	}

	for _, table := range tables {
		if err := db.Migrator().DropTable(table); err != nil {
			log.Printf("删除表失败: %v", err)
		}
	}

	log.Println("数据库回滚执行完成")
	return nil
}

// runSeeder 执行种子数据导入
func runSeeder(db *gorm.DB, force bool) error {
	if !force {
		fmt.Print("确定要导入种子数据吗? (y/N): ")
		var response string
		fmt.Scanln(&response)
		if response != "y" && response != "Y" {
			fmt.Println("操作已取消")
			return nil
		}
	}

	log.Println("开始导入种子数据...")

	// 创建默认管理员用户
	if err := createDefaultAdmin(db); err != nil {
		return err
	}

	// 创建默认权限组
	if err := createDefaultPermissionGroups(db); err != nil {
		return err
	}

	// 创建默认产品
	if err := createDefaultProducts(db); err != nil {
		return err
	}

	// 创建默认支付方式
	if err := createDefaultPayments(db); err != nil {
		return err
	}

	// 创建默认FAQ分类和内容
	if err := createDefaultFAQs(db); err != nil {
		return err
	}

	// 创建默认系统公告
	if err := createDefaultAnnouncements(db); err != nil {
		return err
	}

	// 创建默认告警规则
	if err := createDefaultAlertRules(db); err != nil {
		return err
	}

	log.Println("种子数据导入完成")
	return nil
}

// runFresh 重建数据库
func runFresh(db *gorm.DB, force bool) error {
	if !force {
		fmt.Print("确定要重建数据库吗? 这将删除所有数据! (y/N): ")
		var response string
		fmt.Scanln(&response)
		if response != "y" && response != "Y" {
			fmt.Println("操作已取消")
			return nil
		}
	}

	// 先回滚
	if err := runRollback(db, true); err != nil {
		return err
	}

	// 再迁移
	if err := runMigration(db, true); err != nil {
		return err
	}

	// 最后导入种子数据
	if err := runSeeder(db, true); err != nil {
		return err
	}

	return nil
}

// createDefaultAdmin 创建默认管理员
func createDefaultAdmin(db *gorm.DB) error {
	// 检查是否已存在管理员
	var count int64
	db.Model(&model.User{}).Where("role = ?", "admin").Count(&count)
	if count > 0 {
		log.Println("管理员用户已存在，跳过创建")
		return nil
	}

	// 创建默认管理员
	admin := &model.User{
		UUID:         "admin-001",
		Username:     "admin",
		Email:        "<EMAIL>",
		PasswordHash: "$2a$10$N9qo8uLOickgx2ZMRZoMye6FQ/B8kVVJKN9.TKzOJzOJzOJzOJzOJ", // password: admin123
		Role:         "admin",
		Status:       "active",
		TrafficLimit: 0, // 无限制
		DeviceLimit:  0, // 无限制
		SpeedLimit:   0, // 无限制
	}

	if err := db.Create(admin).Error; err != nil {
		return fmt.Errorf("创建管理员用户失败: %v", err)
	}

	log.Println("默认管理员创建完成 (用户名: admin, 密码: admin123)")
	return nil
}

// createDefaultPermissionGroups 创建默认权限组
func createDefaultPermissionGroups(db *gorm.DB) error {
	groups := []*model.PermissionGroup{
		{
			Name:        "免费用户",
			Description: "免费用户权限组，可访问基础节点",
			SortOrder:   1,
		},
		{
			Name:        "VIP用户",
			Description: "VIP用户权限组，可访问高级节点",
			SortOrder:   2,
		},
		{
			Name:        "SVIP用户",
			Description: "SVIP用户权限组，可访问所有节点",
			SortOrder:   3,
		},
	}

	for _, group := range groups {
		var count int64
		db.Model(&model.PermissionGroup{}).Where("name = ?", group.Name).Count(&count)
		if count == 0 {
			if err := db.Create(group).Error; err != nil {
				return fmt.Errorf("创建权限组失败: %v", err)
			}
		}
	}

	log.Println("默认权限组创建完成")
	return nil
}

// createDefaultProducts 创建默认产品
func createDefaultProducts(db *gorm.DB) error {
	// 获取权限组
	var groups []model.PermissionGroup
	db.Find(&groups)
	if len(groups) == 0 {
		return fmt.Errorf("没有找到权限组")
	}

	products := []*model.Product{
		{
			Name:              "基础套餐",
			Description:       "适合轻度使用的基础套餐",
			Price:             9.99,
			TrafficLimit:      10 * 1024 * 1024 * 1024, // 10GB
			DurationDays:      30,
			DeviceLimit:       1,
			SpeedLimit:        10, // 10Mbps
			PermissionGroupID: groups[0].ID,
			Status:            "active",
			SortOrder:         1,
		},
		{
			Name:              "标准套餐",
			Description:       "适合中度使用的标准套餐",
			Price:             19.99,
			TrafficLimit:      50 * 1024 * 1024 * 1024, // 50GB
			DurationDays:      30,
			DeviceLimit:       3,
			SpeedLimit:        50, // 50Mbps
			PermissionGroupID: groups[1].ID,
			Status:            "active",
			SortOrder:         2,
		},
		{
			Name:              "高级套餐",
			Description:       "适合重度使用的高级套餐",
			Price:             39.99,
			TrafficLimit:      200 * 1024 * 1024 * 1024, // 200GB
			DurationDays:      30,
			DeviceLimit:       5,
			SpeedLimit:        100, // 100Mbps
			PermissionGroupID: groups[2].ID,
			Status:            "active",
			SortOrder:         3,
		},
	}

	for _, product := range products {
		var count int64
		db.Model(&model.Product{}).Where("name = ?", product.Name).Count(&count)
		if count == 0 {
			if err := db.Create(product).Error; err != nil {
				return fmt.Errorf("创建产品失败: %v", err)
			}
		}
	}

	log.Println("默认产品创建完成")
	return nil
}

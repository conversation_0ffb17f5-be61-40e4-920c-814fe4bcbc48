package service

import (
	"anypanel/internal/model"
	"encoding/json"
	"fmt"
	"net/url"
	"strings"
)

// AnyTLSConfigGenerator AnyTLS协议配置生成器
type AnyTLSConfigGenerator struct{}

// GenerateURI 生成AnyTLS URI
func (g *AnyTLSConfigGenerator) GenerateURI(node *model.Node, userUUID string) (string, error) {
	if node.Protocol != "anytls" {
		return "", fmt.Errorf("节点协议不是AnyTLS")
	}

	// 解析AnyTLS特定配置
	var anyTLSConfig AnyTLSConfig
	if node.Config != nil {
		if err := json.Unmarshal(node.Config, &anyTLSConfig); err != nil {
			return "", fmt.Errorf("解析AnyTLS配置失败: %v", err)
		}
	}

	// 构建查询参数
	params := url.Values{}
	params.Set("password", node.Password)
	params.Set("uuid", userUUID)

	// 添加填充策略
	if len(anyTLSConfig.PaddingScheme) > 0 {
		params.Set("padding", strings.Join(anyTLSConfig.PaddingScheme, ","))
	}

	// 添加SNI
	if node.ServerName != "" {
		params.Set("sni", node.ServerName)
	}

	// 生成URI
	uri := fmt.Sprintf("anytls://%s:%d?%s", node.Host, node.Port, params.Encode())
	return uri, nil
}

// GenerateConfig 生成AnyTLS配置
func (g *AnyTLSConfigGenerator) GenerateConfig(node *model.Node, userUUID string) (map[string]interface{}, error) {
	if node.Protocol != "anytls" {
		return nil, fmt.Errorf("节点协议不是AnyTLS")
	}

	// 解析AnyTLS特定配置
	var anyTLSConfig AnyTLSConfig
	if node.Config != nil {
		if err := json.Unmarshal(node.Config, &anyTLSConfig); err != nil {
			return nil, fmt.Errorf("解析AnyTLS配置失败: %v", err)
		}
	}

	config := map[string]interface{}{
		"name":           node.Name,
		"type":           "anytls",
		"server":         node.Host,
		"port":           node.Port,
		"password":       node.Password,
		"uuid":           userUUID,
		"padding-scheme": anyTLSConfig.PaddingScheme,
	}

	if node.ServerName != "" {
		config["sni"] = node.ServerName
	}

	return config, nil
}

// ValidateConfig 验证AnyTLS配置
func (g *AnyTLSConfigGenerator) ValidateConfig(config interface{}) bool {
	if configMap, ok := config.(map[string]interface{}); ok {
		// 检查必需字段
		if _, hasName := configMap["name"]; !hasName {
			return false
		}
		if _, hasServer := configMap["server"]; !hasServer {
			return false
		}
		if _, hasPort := configMap["port"]; !hasPort {
			return false
		}
		if _, hasPassword := configMap["password"]; !hasPassword {
			return false
		}
		if _, hasUUID := configMap["uuid"]; !hasUUID {
			return false
		}
		return true
	}
	return false
}

// GetSupportedProtocols 获取支持的协议
func (g *AnyTLSConfigGenerator) GetSupportedProtocols() []string {
	return []string{"anytls"}
}

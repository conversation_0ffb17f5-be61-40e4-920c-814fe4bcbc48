package service

import (
	"anypanel/internal/model"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type FAQService struct {
	db *gorm.DB
}

func NewFAQService(db *gorm.DB) *FAQService {
	return &FAQService{
		db: db,
	}
}

// FAQQueryRequest FAQ查询请求
type FAQQueryRequest struct {
	Page       int    `form:"page" binding:"min=1"`
	PageSize   int    `form:"page_size" binding:"min=1,max=100"`
	CategoryID *uint  `form:"category_id"`
	Search     string `form:"search"`
	Status     string `form:"status"`
	SortBy     string `form:"sort_by"`
	SortOrder  string `form:"sort_order"`
}

// FAQQueryResponse FAQ查询响应
type FAQQueryResponse struct {
	Total int64     `json:"total"`
	FAQs  []*FAQDTO `json:"faqs"`
}

// FAQDTO FAQ数据传输对象
type FAQDTO struct {
	ID         uint         `json:"id"`
	CategoryID uint         `json:"category_id"`
	Question   string       `json:"question"`
	Answer     string       `json:"answer"`
	SortOrder  int          `json:"sort_order"`
	Status     string       `json:"status"`
	ViewCount  int64        `json:"view_count"`
	IsHelpful  int64        `json:"is_helpful"`
	CreatedAt  time.Time    `json:"created_at"`
	UpdatedAt  time.Time    `json:"updated_at"`
	Category   *CategoryDTO `json:"category,omitempty"`
}

// CategoryDTO 分类数据传输对象
type CategoryDTO struct {
	ID          uint   `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
}

// FAQCreateRequest FAQ创建请求
type FAQCreateRequest struct {
	CategoryID uint   `json:"category_id" binding:"required"`
	Question   string `json:"question" binding:"required,max=500"`
	Answer     string `json:"answer" binding:"required"`
	SortOrder  int    `json:"sort_order"`
	Status     string `json:"status" binding:"oneof=active inactive"`
}

// FAQUpdateRequest FAQ更新请求
type FAQUpdateRequest struct {
	CategoryID *uint   `json:"category_id"`
	Question   *string `json:"question" binding:"omitempty,max=500"`
	Answer     *string `json:"answer"`
	SortOrder  *int    `json:"sort_order"`
	Status     *string `json:"status" binding:"omitempty,oneof=active inactive"`
}

// GetFAQs 获取FAQ列表
func (s *FAQService) GetFAQs(req *FAQQueryRequest) (*FAQQueryResponse, error) {
	query := s.db.Model(&model.FAQ{})

	// 分类筛选
	if req.CategoryID != nil {
		query = query.Where("category_id = ?", *req.CategoryID)
	}

	// 搜索条件
	if req.Search != "" {
		searchPattern := "%" + req.Search + "%"
		query = query.Where("question LIKE ? OR answer LIKE ?", searchPattern, searchPattern)
	}

	// 状态筛选
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 排序
	sortBy := req.SortBy
	if sortBy == "" {
		sortBy = "sort_order"
	}
	sortOrder := req.SortOrder
	if sortOrder == "" {
		sortOrder = "asc"
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// 分页
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// 预加载分类信息
	var faqs []model.FAQ
	if err := query.Preload("Category").Find(&faqs).Error; err != nil {
		return nil, err
	}

	// 转换为DTO
	faqDTOs := make([]*FAQDTO, len(faqs))
	for i, faq := range faqs {
		faqDTOs[i] = s.toFAQDTO(&faq)
	}

	return &FAQQueryResponse{
		Total: total,
		FAQs:  faqDTOs,
	}, nil
}

// GetFAQByID 根据ID获取FAQ
func (s *FAQService) GetFAQByID(id uint) (*FAQDTO, error) {
	var faq model.FAQ
	if err := s.db.Preload("Category").First(&faq, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("FAQ不存在")
		}
		return nil, err
	}

	// 增加查看次数
	if err := s.db.Model(&faq).UpdateColumn("view_count", gorm.Expr("view_count + ?", 1)).Error; err != nil {
		// 记录错误但不影响主流程
		// log.Printf("更新FAQ查看次数失败: %v", err)
	}

	return s.toFAQDTO(&faq), nil
}

// CreateFAQ 创建FAQ
func (s *FAQService) CreateFAQ(req *FAQCreateRequest) (*FAQDTO, error) {
	// 检查分类是否存在
	var category model.FAQCategory
	if err := s.db.First(&category, req.CategoryID).Error; err != nil {
		return nil, fmt.Errorf("分类不存在")
	}

	faq := &model.FAQ{
		CategoryID: req.CategoryID,
		Question:   req.Question,
		Answer:     req.Answer,
		SortOrder:  req.SortOrder,
		Status:     req.Status,
	}

	if err := faq.Validate(); err != nil {
		return nil, err
	}

	if err := s.db.Create(faq).Error; err != nil {
		return nil, err
	}

	// 重新加载包含分类信息
	if err := s.db.Preload("Category").First(faq, faq.ID).Error; err != nil {
		return nil, err
	}

	return s.toFAQDTO(faq), nil
}

// UpdateFAQ 更新FAQ
func (s *FAQService) UpdateFAQ(id uint, req *FAQUpdateRequest) (*FAQDTO, error) {
	var faq model.FAQ
	if err := s.db.First(&faq, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("FAQ不存在")
		}
		return nil, err
	}

	// 更新字段
	if req.CategoryID != nil {
		// 检查分类是否存在
		var category model.FAQCategory
		if err := s.db.First(&category, *req.CategoryID).Error; err != nil {
			return nil, fmt.Errorf("分类不存在")
		}
		faq.CategoryID = *req.CategoryID
	}

	if req.Question != nil {
		faq.Question = *req.Question
	}

	if req.Answer != nil {
		faq.Answer = *req.Answer
	}

	if req.SortOrder != nil {
		faq.SortOrder = *req.SortOrder
	}

	if req.Status != nil {
		faq.Status = *req.Status
	}

	if err := faq.Validate(); err != nil {
		return nil, err
	}

	if err := s.db.Save(&faq).Error; err != nil {
		return nil, err
	}

	// 重新加载包含分类信息
	if err := s.db.Preload("Category").First(&faq, faq.ID).Error; err != nil {
		return nil, err
	}

	return s.toFAQDTO(&faq), nil
}

// DeleteFAQ 删除FAQ
func (s *FAQService) DeleteFAQ(id uint) error {
	var faq model.FAQ
	if err := s.db.First(&faq, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("FAQ不存在")
		}
		return err
	}

	return s.db.Delete(&faq).Error
}

// MarkFAQHelpful 标记FAQ为有用
func (s *FAQService) MarkFAQHelpful(id uint) error {
	return s.db.Model(&model.FAQ{}).Where("id = ?", id).
		UpdateColumn("is_helpful", gorm.Expr("is_helpful + ?", 1)).Error
}

// GetFAQCategories 获取FAQ分类列表
func (s *FAQService) GetFAQCategories() ([]*CategoryDTO, error) {
	var categories []model.FAQCategory
	if err := s.db.Where("status = ?", "active").
		Order("sort_order ASC").
		Find(&categories).Error; err != nil {
		return nil, err
	}

	categoryDTOs := make([]*CategoryDTO, len(categories))
	for i, category := range categories {
		categoryDTOs[i] = &CategoryDTO{
			ID:          category.ID,
			Name:        category.Name,
			Description: category.Description,
		}
	}

	return categoryDTOs, nil
}

// GetPopularFAQs 获取热门FAQ
func (s *FAQService) GetPopularFAQs(limit int) ([]*FAQDTO, error) {
	if limit <= 0 {
		limit = 10
	}

	var faqs []model.FAQ
	if err := s.db.Where("status = ?", "active").
		Order("view_count DESC").
		Limit(limit).
		Preload("Category").
		Find(&faqs).Error; err != nil {
		return nil, err
	}

	faqDTOs := make([]*FAQDTO, len(faqs))
	for i, faq := range faqs {
		faqDTOs[i] = s.toFAQDTO(&faq)
	}

	return faqDTOs, nil
}

// SearchFAQs 搜索FAQ
func (s *FAQService) SearchFAQs(keyword string, limit int) ([]*FAQDTO, error) {
	if limit <= 0 {
		limit = 20
	}

	searchPattern := "%" + keyword + "%"
	var faqs []model.FAQ

	if err := s.db.Where("status = ? AND (question LIKE ? OR answer LIKE ?)",
		"active", searchPattern, searchPattern).
		Order("view_count DESC").
		Limit(limit).
		Preload("Category").
		Find(&faqs).Error; err != nil {
		return nil, err
	}

	faqDTOs := make([]*FAQDTO, len(faqs))
	for i, faq := range faqs {
		faqDTOs[i] = s.toFAQDTO(&faq)
	}

	return faqDTOs, nil
}

// 辅助函数
func (s *FAQService) toFAQDTO(faq *model.FAQ) *FAQDTO {
	dto := &FAQDTO{
		ID:         faq.ID,
		CategoryID: faq.CategoryID,
		Question:   faq.Question,
		Answer:     faq.Answer,
		SortOrder:  faq.SortOrder,
		Status:     faq.Status,
		ViewCount:  faq.ViewCount,
		IsHelpful:  faq.IsHelpful,
		CreatedAt:  faq.CreatedAt,
		UpdatedAt:  faq.UpdatedAt,
	}

	// 添加分类信息
	if faq.Category.ID != 0 {
		dto.Category = &CategoryDTO{
			ID:          faq.Category.ID,
			Name:        faq.Category.Name,
			Description: faq.Category.Description,
		}
	}

	return dto
}

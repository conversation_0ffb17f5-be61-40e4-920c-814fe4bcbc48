package service

import (
	"anypanel/internal/model"
	"context"
	"fmt"
	"net"
	"strings"
	"time"

	"gorm.io/gorm"
)

// SecurityService 安全服务
type SecurityService struct {
	db *gorm.DB
}

// NewSecurityService 创建安全服务
func NewSecurityService(db *gorm.DB) *SecurityService {
	return &SecurityService{
		db: db,
	}
}

// SecurityEventService 安全事件服务
type SecurityEventService struct {
	db *gorm.DB
}

// NewSecurityEventService 创建安全事件服务
func NewSecurityEventService(db *gorm.DB) *SecurityEventService {
	return &SecurityEventService{
		db: db,
	}
}

// CreateSecurityEvent 创建安全事件
func (s *SecurityEventService) CreateSecurityEvent(event *model.SecurityEvent) error {
	return s.db.Create(event).Error
}

// LogSecurityEvent 记录安全事件
func (s *SecurityEventService) LogSecurityEvent(userID uint, eventType, ipAddress, userAgent, description, severity, source string, eventData interface{}) error {
	event := &model.SecurityEvent{
		UserID:      userID,
		EventType:   eventType,
		IPAddress:   ipAddress,
		UserAgent:   userAgent,
		Description: description,
		Severity:    severity,
		Source:      source,
		CreatedAt:   time.Now(),
	}

	// TODO: 序列化eventData为JSON
	
	return s.CreateSecurityEvent(event)
}

// GetSecurityEvents 获取安全事件列表
func (s *SecurityEventService) GetSecurityEvents(limit int, offset int, eventType, severity, ipAddress string) ([]*model.SecurityEvent, int64, error) {
	var events []*model.SecurityEvent
	var total int64

	query := s.db.Model(&model.SecurityEvent{})

	// 添加过滤条件
	if eventType != "" {
		query = query.Where("event_type = ?", eventType)
	}
	if severity != "" {
		query = query.Where("severity = ?", severity)
	}
	if ipAddress != "" {
		query = query.Where("ip_address = ?", ipAddress)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据
	if err := query.Preload("User").Order("created_at DESC").Limit(limit).Offset(offset).Find(&events).Error; err != nil {
		return nil, 0, err
	}

	return events, total, nil
}

// IPBanService IP封禁服务
type IPBanService struct {
	db *gorm.DB
}

// NewIPBanService 创建IP封禁服务
func NewIPBanService(db *gorm.DB) *IPBanService {
	return &IPBanService{
		db: db,
	}
}

// IsIPBanned 检查IP是否被封禁
func (s *IPBanService) IsIPBanned(ipAddress string) (bool, *model.IPBanRecord, error) {
	var banRecord model.IPBanRecord
	
	// 查找活跃的封禁记录
	err := s.db.Where("ip_address = ? AND is_active = ?", ipAddress, true).First(&banRecord).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, nil, nil
		}
		return false, nil, err
	}

	// 检查临时封禁是否过期
	if banRecord.BanType == "temporary" && banRecord.ExpiresAt != nil && banRecord.ExpiresAt.Before(time.Now()) {
		// 标记为过期
		s.db.Model(&banRecord).Update("is_active", false)
		return false, nil, nil
	}

	return true, &banRecord, nil
}

// BanIP 封禁IP
func (s *IPBanService) BanIP(ipAddress, reason, banType string, expiresAt *time.Time, createdBy uint) error {
	// 检查IP格式
	if net.ParseIP(ipAddress) == nil {
		return fmt.Errorf("无效的IP地址格式")
	}

	// 检查是否已经被封禁
	isBanned, _, err := s.IsIPBanned(ipAddress)
	if err != nil {
		return err
	}
	if isBanned {
		return fmt.Errorf("IP地址已被封禁")
	}

	banRecord := &model.IPBanRecord{
		IPAddress: ipAddress,
		Reason:    reason,
		BanType:   banType,
		ExpiresAt: expiresAt,
		IsActive:  true,
		CreatedBy: createdBy,
		CreatedAt: time.Now(),
	}

	return s.db.Create(banRecord).Error
}

// UnbanIP 解封IP
func (s *IPBanService) UnbanIP(ipAddress string) error {
	return s.db.Model(&model.IPBanRecord{}).
		Where("ip_address = ? AND is_active = ?", ipAddress, true).
		Update("is_active", false).Error
}

// GetBannedIPs 获取封禁IP列表
func (s *IPBanService) GetBannedIPs(limit int, offset int, activeOnly bool) ([]*model.IPBanRecord, int64, error) {
	var records []*model.IPBanRecord
	var total int64

	query := s.db.Model(&model.IPBanRecord{})
	if activeOnly {
		query = query.Where("is_active = ?", true)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据
	if err := query.Preload("Creator").Order("created_at DESC").Limit(limit).Offset(offset).Find(&records).Error; err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

// AuditLogService 审计日志服务
type AuditLogService struct {
	db *gorm.DB
}

// NewAuditLogService 创建审计日志服务
func NewAuditLogService(db *gorm.DB) *AuditLogService {
	return &AuditLogService{
		db: db,
	}
}

// LogOperation 记录操作日志
func (s *AuditLogService) LogOperation(userID uint, action, resource, resourceID, ipAddress, userAgent string, oldValues, newValues interface{}) error {
	auditLog := &model.AuditLog{
		UserID:     userID,
		Action:     action,
		Resource:   resource,
		ResourceID: resourceID,
		IPAddress:  ipAddress,
		UserAgent:  userAgent,
		Status:     "success",
		CreatedAt:  time.Now(),
	}

	// TODO: 序列化oldValues和newValues为JSON

	return s.db.Create(auditLog).Error
}

// LogFailedOperation 记录失败的操作
func (s *AuditLogService) LogFailedOperation(userID uint, action, resource, resourceID, ipAddress, userAgent, errorMsg string) error {
	auditLog := &model.AuditLog{
		UserID:     userID,
		Action:     action,
		Resource:   resource,
		ResourceID: resourceID,
		IPAddress:  ipAddress,
		UserAgent:  userAgent,
		Status:     "failed",
		ErrorMsg:   errorMsg,
		CreatedAt:  time.Now(),
	}

	return s.db.Create(auditLog).Error
}

// GetAuditLogs 获取审计日志
func (s *AuditLogService) GetAuditLogs(limit int, offset int, userID uint, action, resource string) ([]*model.AuditLog, int64, error) {
	var logs []*model.AuditLog
	var total int64

	query := s.db.Model(&model.AuditLog{})

	// 添加过滤条件
	if userID > 0 {
		query = query.Where("user_id = ?", userID)
	}
	if action != "" {
		query = query.Where("action = ?", action)
	}
	if resource != "" {
		query = query.Where("resource = ?", resource)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据
	if err := query.Preload("User").Order("created_at DESC").Limit(limit).Offset(offset).Find(&logs).Error; err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// RateLimitService 速率限制服务
type RateLimitService struct {
	db  *gorm.DB
	ctx context.Context
}

// NewRateLimitService 创建速率限制服务
func NewRateLimitService(db *gorm.DB) *RateLimitService {
	return &RateLimitService{
		db:  db,
		ctx: context.Background(),
	}
}

// CheckRateLimit 检查速率限制
func (s *RateLimitService) CheckRateLimit(identifier, limitType, endpoint string, limit int64, windowSeconds int64) (bool, error) {
	now := time.Now()
	windowStart := now.Add(-time.Duration(windowSeconds) * time.Second)

	var record model.RateLimitRecord
	err := s.db.Where("identifier = ? AND limit_type = ? AND endpoint = ? AND window_start > ?", 
		identifier, limitType, endpoint, windowStart).First(&record).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 创建新记录
			newRecord := &model.RateLimitRecord{
				Identifier:   identifier,
				LimitType:    limitType,
				Endpoint:     endpoint,
				RequestCount: 1,
				WindowStart:  now,
				LastRequest:  now,
				IsBlocked:    false,
				CreatedAt:    now,
				UpdatedAt:    now,
			}
			return false, s.db.Create(newRecord).Error
		}
		return false, err
	}

	// 更新记录
	record.RequestCount++
	record.LastRequest = now
	record.UpdatedAt = now

	// 检查是否超限
	if record.RequestCount > limit {
		record.IsBlocked = true
		s.db.Save(&record)
		return true, nil // 被限制
	}

	s.db.Save(&record)
	return false, nil // 未被限制
}

// CleanupOldRecords 清理旧的速率限制记录
func (s *RateLimitService) CleanupOldRecords(olderThanHours int) error {
	cutoff := time.Now().Add(-time.Duration(olderThanHours) * time.Hour)
	return s.db.Where("window_start < ?", cutoff).Delete(&model.RateLimitRecord{}).Error
}

// GetRateLimitRecords 获取速率限制记录
func (s *RateLimitService) GetRateLimitRecords(limit int, offset int, identifier, limitType string, blockedOnly bool) ([]*model.RateLimitRecord, int64, error) {
	var records []*model.RateLimitRecord
	var total int64

	query := s.db.Model(&model.RateLimitRecord{})

	// 添加过滤条件
	if identifier != "" {
		query = query.Where("identifier = ?", identifier)
	}
	if limitType != "" {
		query = query.Where("limit_type = ?", limitType)
	}
	if blockedOnly {
		query = query.Where("is_blocked = ?", true)
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取数据
	if err := query.Order("created_at DESC").Limit(limit).Offset(offset).Find(&records).Error; err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

// GetClientIP 获取客户端真实IP地址
func GetClientIP(userAgent, xForwardedFor, xRealIP, remoteAddr string) string {
	// 处理X-Forwarded-For头
	if xForwardedFor != "" {
		ips := strings.Split(xForwardedFor, ",")
		for _, ip := range ips {
			ip = strings.TrimSpace(ip)
			if ip != "" && net.ParseIP(ip) != nil {
				return ip
			}
		}
	}

	// 处理X-Real-IP头
	if xRealIP != "" && net.ParseIP(xRealIP) != nil {
		return xRealIP
	}

	// 处理RemoteAddr
	if remoteAddr != "" {
		ip, _, err := net.SplitHostPort(remoteAddr)
		if err == nil && net.ParseIP(ip) != nil {
			return ip
		}
		// 如果没有端口号，直接返回IP
		if net.ParseIP(remoteAddr) != nil {
			return remoteAddr
		}
	}

	return "unknown"
}

// IsInternalIP 检查是否为内部IP
func IsInternalIP(ip string) bool {
	if ip == "127.0.0.1" || ip == "::1" || ip == "localhost" {
		return true
	}

	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return false
	}

	return parsedIP.IsLoopback() || parsedIP.IsPrivate()
}
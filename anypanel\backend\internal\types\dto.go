package types

import (
	"encoding/json"
)

// PermissionGroupDTO 权限组数据传输对象
type PermissionGroupDTO struct {
	ID          uint          `json:"id"`
	Name        string        `json:"name"`
	Description string        `json:"description"`
	SortOrder   int           `json:"sort_order"`
	CreatedAt   string        `json:"created_at"`
	UpdatedAt   string        `json:"updated_at"`
	NodeCount   int           `json:"node_count"`
	Nodes       []*NodeDTO    `json:"nodes,omitempty"`
	Products    []*ProductDTO `json:"products,omitempty"`
}

// NodeDTO 节点数据传输对象
type NodeDTO struct {
	ID          uint                  `json:"id"`
	Name        string                `json:"name"`
	Protocol    string                `json:"protocol"`
	Host        string                `json:"host"`
	Port        int                   `json:"port"`
	Config      json.RawMessage       `json:"config"`
	ServerName  string                `json:"server_name"`
	Status      string                `json:"status"`
	SortOrder   int                   `json:"sort_order"`
	TrafficRate float64               `json:"traffic_rate"`
	MaxUsers    int                   `json:"max_users"`
	GroupID     uint                  `json:"group_id"`
	GroupName   string                `json:"group_name"`
	Country     string                `json:"country"`
	City        string                `json:"city"`
	OnlineUsers int                   `json:"online_users"`
	GroupIDs    []uint                `json:"group_ids,omitempty"`
	Groups      []*PermissionGroupDTO `json:"groups,omitempty"`
	CreatedAt   string                `json:"created_at"`
	UpdatedAt   string                `json:"updated_at"`
}

// ProductDTO 商品数据传输对象
type ProductDTO struct {
	ID           uint    `json:"id"`
	Name         string  `json:"name"`
	Description  string  `json:"description"`
	Price        float64 `json:"price"`
	TrafficLimit int64   `json:"traffic_limit"`
	DurationDays int     `json:"duration_days"`
	DeviceLimit  int     `json:"device_limit"`
	SpeedLimit   int     `json:"speed_limit"`
	GroupID      uint    `json:"group_id"`
	GroupName    string  `json:"group_name"`
	Status       string  `json:"status"`
	SortOrder    int     `json:"sort_order"`
	CreatedAt    string  `json:"created_at"`
	UpdatedAt    string  `json:"updated_at"`
}

// UserDTO 用户数据传输对象
type UserDTO struct {
	ID           uint   `json:"id"`
	UUID         string `json:"uuid"`
	Username     string `json:"username"`
	Email        string `json:"email"`
	Role         string `json:"role"`
	Status       string `json:"status"`
	TrafficLimit int64  `json:"traffic_limit"`
	TrafficUsed  int64  `json:"traffic_used"`
	DeviceLimit  int    `json:"device_limit"`
	ExpiredAt    string `json:"expired_at"`
	GroupID      uint   `json:"group_id"`
	GroupName    string `json:"group_name"`
	CreatedAt    string `json:"created_at"`
	UpdatedAt    string `json:"updated_at"`
}

// OrderDTO 订单数据传输对象
type OrderDTO struct {
	ID            string  `json:"id"`
	UserID        uint    `json:"user_id"`
	Username      string  `json:"username"`
	ProductID     uint    `json:"product_id"`
	ProductName   string  `json:"product_name"`
	Amount        float64 `json:"amount"`
	Status        string  `json:"status"`
	PaymentMethod string  `json:"payment_method"`
	PaymentID     string  `json:"payment_id"`
	CreatedAt     string  `json:"created_at"`
	UpdatedAt     string  `json:"updated_at"`
}

// SubscriptionDTO 订阅数据传输对象
type SubscriptionDTO struct {
	ID           uint   `json:"id"`
	UserID       uint   `json:"user_id"`
	Username     string `json:"username"`
	GroupID      uint   `json:"group_id"`
	GroupName    string `json:"group_name"`
	TrafficLimit int64  `json:"traffic_limit"`
	TrafficUsed  int64  `json:"traffic_used"`
	DeviceLimit  int    `json:"device_limit"`
	Status       string `json:"status"`
	ExpiredAt    string `json:"expired_at"`
	CreatedAt    string `json:"created_at"`
	UpdatedAt    string `json:"updated_at"`
}

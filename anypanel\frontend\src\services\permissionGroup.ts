import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import apiClient, { PaginatedResponse, PaginationParams } from './api';

// 权限组数据类型
export interface PermissionGroup {
  id: number;
  name: string;
  description: string;
  sort_order: number;
  node_count: number;
  user_count: number;
  created_at: string;
  updated_at: string;
  nodes?: Node[];
}

// 节点数据类型
export interface Node {
  id: number;
  name: string;
  protocol: string;
  host: string;
  port: number;
  status: 'online' | 'offline' | 'maintenance';
  location: string;
  online_users: number;
  max_users: number;
  traffic_rate: number;
  created_at: string;
}

// 权限组创建/编辑表单类型
export interface PermissionGroupForm {
  name: string;
  description?: string;
  sort_order?: number;
  node_ids?: number[];
}

// 权限组使用统计
export interface PermissionGroupStats {
  id: number;
  name: string;
  user_count: number;
  node_count: number;
  monthly_revenue: number;
  traffic_usage: number;
  active_users: number;
}

// 节点分配操作
export interface NodeAssignmentOperation {
  group_id: number;
  node_ids: number[];
  operation: 'assign' | 'unassign';
}

class PermissionGroupService {
  // 获取权限组列表
  async getPermissionGroups(params: PaginationParams): Promise<PaginatedResponse<PermissionGroup>> {
    // 转换分页参数以匹配后端期望的格式
    const backendParams = {
      ...params,
      page: params.current || 1,
      page_size: params.pageSize || 20,
    };
    // 删除前端的分页参数，避免冲突
    delete backendParams.current;
    delete backendParams.pageSize;

    const response = await apiClient.get('/permission-groups/admin', backendParams);

    // 转换后端响应格式为前端期望的分页格式
    return {
      list: response.permission_groups || [],  // 后端返回的是 "permission_groups" 字段
      total: response.total || 0,
      current: params.current || 1,
      pageSize: params.pageSize || 20
    };
  }

  // 获取单个权限组详情
  async getPermissionGroup(id: number): Promise<PermissionGroup> {
    return apiClient.get(`/permission-groups/admin/${id}`);
  }

  // 获取所有权限组（简单列表，用于下拉选择）
  async getAllPermissionGroups(): Promise<PermissionGroup[]> {
    return apiClient.get('/permission-groups/admin/all');
  }

  // 获取所有节点列表（用于分配）
  async getAllNodes(): Promise<Node[]> {
    return apiClient.get('/nodes/admin/all');
  }

  // 创建权限组
  async createPermissionGroup(data: PermissionGroupForm): Promise<PermissionGroup> {
    return apiClient.post('/permission-groups/admin', data);
  }

  // 更新权限组
  async updatePermissionGroup(id: number, data: PermissionGroupForm): Promise<PermissionGroup> {
    return apiClient.put(`/permission-groups/admin/${id}`, data);
  }

  // 删除权限组
  async deletePermissionGroup(id: number): Promise<void> {
    return apiClient.delete(`/permission-groups/admin/${id}`);
  }

  // 批量删除权限组（这个接口后端可能没有，先保留）
  async batchDeletePermissionGroups(ids: number[]): Promise<void> {
    // 批量删除可能需要循环调用单个删除API
    await Promise.all(ids.map(id => this.deletePermissionGroup(id)));
  }

  // 节点分配操作  
  async assignNodes(operation: NodeAssignmentOperation): Promise<void> {
    if (operation.operation === 'assign') {
      return apiClient.post(`/permission-groups/admin/${operation.group_id}/nodes`, {
        node_ids: operation.node_ids,
      });
    } else {
      return apiClient.delete(`/permission-groups/admin/${operation.group_id}/nodes`, {
        data: { node_ids: operation.node_ids },
      });
    }
  }

  // 获取权限组使用统计（这个接口后端可能没有，需要实现或模拟）
  async getPermissionGroupStats(): Promise<PermissionGroupStats[]> {
    // 暂时返回空数组，等后端实现统计接口
    return [];
  }
}

export const permissionGroupService = new PermissionGroupService();

// React Query Hooks
export const usePermissionGroups = (params: PaginationParams) => {
  return useQuery({
    queryKey: ['permission-groups', params],
    queryFn: () => permissionGroupService.getPermissionGroups(params),
    keepPreviousData: true,
  });
};

export const usePermissionGroup = (id: number) => {
  return useQuery({
    queryKey: ['permission-group', id],
    queryFn: () => permissionGroupService.getPermissionGroup(id),
    enabled: !!id,
  });
};

export const useAllPermissionGroups = () => {
  return useQuery({
    queryKey: ['all-permission-groups'],
    queryFn: () => permissionGroupService.getAllPermissionGroups(),
    staleTime: 5 * 60 * 1000, // 5分钟
  });
};

export const useAllNodes = () => {
  return useQuery({
    queryKey: ['all-nodes'],
    queryFn: () => permissionGroupService.getAllNodes(),
    staleTime: 5 * 60 * 1000, // 5分钟
  });
};

export const usePermissionGroupStats = () => {
  return useQuery({
    queryKey: ['permission-group-stats'],
    queryFn: () => permissionGroupService.getPermissionGroupStats(),
    refetchInterval: 5 * 60 * 1000, // 5分钟刷新
  });
};

// Mutations
export const useCreatePermissionGroup = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: PermissionGroupForm) => 
      permissionGroupService.createPermissionGroup(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['permission-groups'] });
      queryClient.invalidateQueries({ queryKey: ['permission-group-stats'] });
      message.success('权限组创建成功');
    },
    onError: () => {
      message.error('权限组创建失败');
    },
  });
};

export const useUpdatePermissionGroup = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: PermissionGroupForm }) => 
      permissionGroupService.updatePermissionGroup(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['permission-groups'] });
      queryClient.invalidateQueries({ queryKey: ['permission-group'] });
      queryClient.invalidateQueries({ queryKey: ['permission-group-stats'] });
      message.success('权限组更新成功');
    },
    onError: () => {
      message.error('权限组更新失败');
    },
  });
};

export const useDeletePermissionGroup = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: number) => permissionGroupService.deletePermissionGroup(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['permission-groups'] });
      queryClient.invalidateQueries({ queryKey: ['permission-group-stats'] });
      message.success('权限组删除成功');
    },
    onError: () => {
      message.error('权限组删除失败');
    },
  });
};

export const useBatchDeletePermissionGroups = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (ids: number[]) => permissionGroupService.batchDeletePermissionGroups(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['permission-groups'] });
      queryClient.invalidateQueries({ queryKey: ['permission-group-stats'] });
      message.success('批量删除成功');
    },
    onError: () => {
      message.error('批量删除失败');
    },
  });
};

export const useAssignNodes = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (operation: NodeAssignmentOperation) => 
      permissionGroupService.assignNodes(operation),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['permission-groups'] });
      queryClient.invalidateQueries({ queryKey: ['permission-group'] });
      message.success('节点分配成功');
    },
    onError: () => {
      message.error('节点分配失败');
    },
  });
};
{"go.toolsManagement.autoUpdate": true, "go.useLanguageServer": true, "go.lintOnSave": "file", "go.lintTool": "golangci-lint", "go.lintFlags": ["--fast"], "go.testFlags": ["-v", "-race"], "go.coverOnSave": true, "go.buildFlags": [], "go.buildTags": "", "go.testTimeout": "30s", "go.gopath": "", "go.goroot": "", "go.inferGopath": false, "go.gotoSymbol.includeImports": true, "go.gotoSymbol.includeGoroot": true, "go.useCodeSnippetsOnFunctionSuggest": true, "go.formatTool": "goimports", "go.generateTestsFlags": [], "go.suggest.tests": true, "go.alternateTools": {}, "go.editorContextMenuCommands": {"toggleTestFile": true, "addTags": true, "removeTags": true, "testAtCursor": false, "testFile": false, "testPackage": false, "generateTestsForFile": true, "generateTestsForPackage": true, "addTagsToTestFile": true, "removeTagsFromTestFile": true, "fillStruct": true}, "go.liveErrors": {"enabled": true, "delay": 500}, "go.codeLens": {"runtest": true}, "typescript.preferences.preferTypeOnlyAutoImports": true, "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "editor.codeActionsOnSave": {"source.fixAll.eslint": true, "source.organizeImports": true}, "editor.formatOnSave": true, "editor.formatOnPaste": true, "editor.tabSize": 2, "editor.insertSpaces": true, "files.associations": {"*.css": "postcss"}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true}, "files.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true}, "files.watcherExclude": {"**/node_modules/**": true, "**/dist/**": true, "**/build/**": true}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "eslint.workingDirectories": ["./frontend"], "terminal.integrated.shell.windows": "C:\\Program Files\\Git\\bin\\bash.exe", "terminal.integrated.shell.linux": "/bin/bash", "terminal.integrated.shell.osx": "/bin/zsh"}
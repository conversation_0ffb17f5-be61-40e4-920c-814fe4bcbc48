# AnyPanel 开发环境配置文件
# 基础配置
debug: true
# 服务器配置
server:
  address: ":8080"
  port: 8080
  timeout: 30

# 数据库配置
database:
  type: mysql
  host: localhost
  port: 3306
  username: anypanel
  password: hxc1314520
  dbname: anypanel    # 数据库名称
  charset: utf8mb4
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600s

# Redis配置  
redis:
  host: localhost
  port: 6379
  password: ""
  db: 0
  pool_size: 10

# JWT配置
jwt:
  secret: "anypanel-dev-secret-key-at-least-32-chars"
  expire_time: 86400      # 24小时
  refresh_expire_time: 604800  # 7天
  access_token_expire: 24h
  refresh_token_expire: 168h    # 7天
  refresh_token_lifetime: 720h  # 30天

# 以下配置项已迁移到数据库中进行动态管理
# 可通过管理员后台的"系统配置管理"页面进行配置，支持热重载
# - V2bX兼容配置
# - 日志配置  
# - CORS配置
# - 限流配置
# - 上传配置
# - 支付配置
# - 安全配置
# - 监控配置
# - 邮件配置
# - 短信配置
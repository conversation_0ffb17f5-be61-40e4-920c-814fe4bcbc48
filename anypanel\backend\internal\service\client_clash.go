package service

import (
	"anypanel/internal/model"
	"fmt"

	"gopkg.in/yaml.v3"
)

// ClashConfigGenerator Clash配置生成器
type ClashConfigGenerator struct{}

// GenerateSubscription 生成Clash订阅配置
func (g *ClashConfigGenerator) GenerateSubscription(nodes []*model.Node, userUUID string) (*SubscriptionResult, error) {
	var proxies []map[string]interface{}

	// 为每个节点生成代理配置
	for _, node := range nodes {
		if node.Protocol == "anytls" {
			proxy, err := g.generateAnyTLSProxy(node, userUUID)
			if err != nil {
				continue // 跳过配置生成失败的节点
			}
			proxies = append(proxies, proxy)
		}
		// 可以在这里添加其他协议的支持
	}

	// 如果没有可用的代理，返回空配置
	if len(proxies) == 0 {
		return &SubscriptionResult{
			Content:     "",
			ContentType: "text/plain",
			Filename:    "empty.txt",
		}, nil
	}

	// 生成Clash配置
	clashConfig := g.generateClashConfig(proxies)

	// 转换为YAML格式
	yamlContent, err := yaml.Marshal(clashConfig)
	if err != nil {
		return nil, fmt.Errorf("生成YAML配置失败: %v", err)
	}

	return &SubscriptionResult{
		Content:     string(yamlContent),
		ContentType: "application/x-yaml",
		Filename:    "clash.yaml",
	}, nil
}

// generateAnyTLSProxy 生成AnyTLS代理配置
func (g *ClashConfigGenerator) generateAnyTLSProxy(node *model.Node, userUUID string) (map[string]interface{}, error) {
	handler, err := GetProtocolHandler("anytls")
	if err != nil {
		return nil, err
	}

	config, err := handler.GenerateConfig(node, userUUID)
	if err != nil {
		return nil, err
	}

	// 转换为Clash格式的代理配置
	proxy := map[string]interface{}{
		"name":           node.Name,
		"type":           "anytls",
		"server":         config["server"],
		"port":           config["port"],
		"password":       config["password"],
		"uuid":           config["uuid"],
		"padding-scheme": config["padding-scheme"],
	}

	// 添加SNI（如果存在）
	if sni, ok := config["sni"]; ok && sni != "" {
		proxy["sni"] = sni
	}

	return proxy, nil
}

// generateClashConfig 生成完整的Clash配置
func (g *ClashConfigGenerator) generateClashConfig(proxies []map[string]interface{}) map[string]interface{} {
	proxyNames := getProxyNames(proxies)

	config := map[string]interface{}{
		"port":                7890,
		"socks-port":          7891,
		"allow-lan":           false,
		"mode":                "rule",
		"log-level":           "info",
		"external-controller": "127.0.0.1:9090",
		"proxies":             proxies,
		"proxy-groups": []map[string]interface{}{
			{
				"name":    "🚀 节点选择",
				"type":    "select",
				"proxies": append([]string{"♻️ 自动选择", "🎯 全球直连"}, proxyNames...),
			},
			{
				"name":      "♻️ 自动选择",
				"type":      "url-test",
				"proxies":   proxyNames,
				"url":       "http://www.gstatic.com/generate_204",
				"interval":  300,
				"tolerance": 50,
			},
			{
				"name":    "🎯 全球直连",
				"type":    "select",
				"proxies": []string{"DIRECT"},
			},
		},
		"rules": []string{
			// 本地网络直连
			"DOMAIN-SUFFIX,local,🎯 全球直连",
			"IP-CIDR,*********/8,🎯 全球直连",
			"IP-CIDR,**********/12,🎯 全球直连",
			"IP-CIDR,***********/16,🎯 全球直连",
			"IP-CIDR,10.0.0.0/8,🎯 全球直连",
			"IP-CIDR,********/8,🎯 全球直连",
			"IP-CIDR,**********/10,🎯 全球直连",
			// 中国大陆直连
			"GEOIP,CN,🎯 全球直连",
			// 其他流量走代理
			"MATCH,🚀 节点选择",
		},
	}

	return config
}

// GetSupportedProtocols 获取支持的协议
func (g *ClashConfigGenerator) GetSupportedProtocols() []string {
	return []string{"anytls"}
}

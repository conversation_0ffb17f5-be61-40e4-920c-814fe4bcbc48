package api

import (
	"anypanel/internal/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type OnlineUserAPI struct {
	onlineUserService *service.OnlineUserService
}

func NewOnlineUserAPI(onlineUserService *service.OnlineUserService) *OnlineUserAPI {
	return &OnlineUserAPI{
		onlineUserService: onlineUserService,
	}
}

// GetOnlineUserStats 获取在线用户统计
func (api *OnlineUserAPI) GetOnlineUserStats(c *gin.Context) {
	stats, err := api.onlineUserService.GetOnlineUserStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取在线用户统计失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取在线用户统计成功",
		"data":    stats,
	})
}

// CheckConnectionLimits 检查连接限制
func (api *OnlineUserAPI) CheckConnectionLimits(c *gin.Context) {
	checks, err := api.onlineUserService.CheckConnectionLimits()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "检查连接限制失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "检查连接限制完成",
		"data":    checks,
	})
}

// MonitorConnectionViolations 监控连接违规
func (api *OnlineUserAPI) MonitorConnectionViolations(c *gin.Context) {
	violations, err := api.onlineUserService.MonitorConnectionViolations()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "监控连接违规失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "连接违规监控完成",
		"data":    violations,
	})
}

// GetRealtimeConnectionStats 获取实时连接统计
func (api *OnlineUserAPI) GetRealtimeConnectionStats(c *gin.Context) {
	stats, err := api.onlineUserService.GetRealtimeConnectionStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取实时连接统计失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取实时连接统计成功",
		"data":    stats,
	})
}

// GetUserOnlineConnections 获取用户的在线连接
func (api *OnlineUserAPI) GetUserOnlineConnections(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的用户ID",
		})
		return
	}

	connections, err := api.onlineUserService.GetUserOnlineConnections(uint(userID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取用户在线连接失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取用户在线连接成功",
		"data":    connections,
	})
}

// GetNodeOnlineUsers 获取节点的在线用户
func (api *OnlineUserAPI) GetNodeOnlineUsers(c *gin.Context) {
	nodeIDStr := c.Param("id")
	nodeID, err := strconv.ParseUint(nodeIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的节点ID",
		})
		return
	}

	connections, err := api.onlineUserService.GetNodeOnlineUsers(uint(nodeID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取节点在线用户失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取节点在线用户成功",
		"data":    connections,
	})
}

// DisconnectUser 强制断开用户连接
func (api *OnlineUserAPI) DisconnectUser(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的用户ID",
		})
		return
	}

	// 可选参数
	var nodeID *uint
	if nodeIDStr := c.Query("node_id"); nodeIDStr != "" {
		if id, err := strconv.ParseUint(nodeIDStr, 10, 32); err == nil {
			id32 := uint(id)
			nodeID = &id32
		}
	}

	var ipAddress *string
	if ip := c.Query("ip_address"); ip != "" {
		ipAddress = &ip
	}

	err = api.onlineUserService.DisconnectUser(uint(userID), nodeID, ipAddress)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "断开用户连接失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "断开用户连接成功",
	})
}

// GetConnectionHistory 获取连接历史
func (api *OnlineUserAPI) GetConnectionHistory(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的用户ID",
		})
		return
	}

	limitStr := c.DefaultQuery("limit", "50")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 50
	}

	history, err := api.onlineUserService.GetConnectionHistory(uint(userID), limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取连接历史失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取连接历史成功",
		"data":    history,
	})
}

// CleanupExpiredConnections 清理过期连接
func (api *OnlineUserAPI) CleanupExpiredConnections(c *gin.Context) {
	err := api.onlineUserService.CleanupExpiredConnections()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "清理过期连接失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "清理过期连接完成",
	})
}

// AutoCleanupExpiredOnlineUsers 自动清理过期在线用户
func (api *OnlineUserAPI) AutoCleanupExpiredOnlineUsers(c *gin.Context) {
	err := api.onlineUserService.AutoCleanupExpiredOnlineUsers()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "自动清理过期在线用户失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "自动清理过期在线用户完成",
	})
}

// GetOnlineUserCleanupStats 获取在线用户清理统计
func (api *OnlineUserAPI) GetOnlineUserCleanupStats(c *gin.Context) {
	stats, err := api.onlineUserService.GetOnlineUserCleanupStats()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取在线用户清理统计失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取在线用户清理统计成功",
		"data":    stats,
	})
}

// ValidateUserConnection 验证用户连接权限
func (api *OnlineUserAPI) ValidateUserConnection(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的用户ID",
		})
		return
	}

	nodeIDStr := c.Query("node_id")
	nodeID, err := strconv.ParseUint(nodeIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的节点ID",
		})
		return
	}

	ipAddress := c.Query("ip_address")
	if ipAddress == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "IP地址不能为空",
		})
		return
	}

	err = api.onlineUserService.ValidateUserConnection(uint(userID), uint(nodeID), ipAddress)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "用户连接权限验证失败",
			"valid":   false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "用户连接权限验证成功",
		"valid":   true,
	})
}
# AnyPanel 配置文件

# 调试模式
debug: true

# 服务器配置
server:
  address: ":8080"
  port: 8080
  timeout: 30

# 数据库配置
database:
  host: localhost
  port: 3306
  username: root
  password: ""
  database: anypanel
  charset: utf8mb4
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600

# Redis配置
redis:
  host: localhost
  port: 6379
  password: ""
  db: 0
  pool_size: 10

# JWT配置
jwt:
  secret: "your-jwt-secret-key"
  expire_time: 86400        # 24小时
  refresh_expire_time: 604800  # 7天

# V2bX节点服务配置
v2bx:
  # 全局服务器token，所有节点使用同一个token进行验证
  # 建议修改为复杂的随机字符串
  server_token: "anypanel-server-token"

# 日志配置
log:
  level: info
  file: "logs/anypanel.log"
  max_size: 100    # MB
  max_backups: 10
  max_age: 30      # 天

# CORS配置
cors:
  allowed_origins:
    - "http://localhost:3000"
    - "http://localhost:5173"
  allowed_methods:
    - "GET"
    - "POST"
    - "PUT"
    - "DELETE"
    - "OPTIONS"
  allowed_headers:
    - "Origin"
    - "Content-Type"
    - "Accept"
    - "Authorization"

# 限流配置
rate_limit:
  enabled: true
  requests_per_minute: 60
  burst: 10

# 文件上传配置
upload:
  max_size: 10485760  # 10MB
  allowed_types:
    - "image/jpeg"
    - "image/png"
    - "image/gif"
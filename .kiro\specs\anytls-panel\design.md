# Design Document

## Overview

AnyTLS Panel是一个多用户代理管理面板，当前版本专注于AnyTLS协议，采用**面板前后端分离**的架构。系统由React前端界面、Go后端API、MySQL数据库组成，完全兼容V2bX节点服务端的API接口规范。

### 设计理念

1. **协议专注性**：当前版本专门为AnyTLS协议优化，提供最佳的用户体验
2. **架构可扩展性**：底层架构设计支持未来添加其他代理协议（如Vmess、Trojan、Shadowsocks等）
3. **模块化设计**：协议相关的逻辑封装在独立模块中，便于扩展和维护

### 架构分离说明

1. **面板前后端分离**：
   - 前端：React + TypeScript 开发的Web界面，提供管理员和用户操作界面
   - 后端：Go + Gin 开发的API服务，处理业务逻辑和数据管理

2. **面板与节点服务分离**：
   - AnyTLS Panel：我们开发的管理面板，负责用户管理、配置生成、流量统计
   - V2bX节点服务端：部署在各服务器上的代理服务，通过API与面板通信
   - AnyTLS代理服务：实际处理用户流量的代理程序

系统核心功能包括用户管理、节点管理、流量统计、订阅生成等，提供管理员和普通用户两种角色的Web界面。

## Architecture

### 系统架构图

```mermaid
graph TB
    subgraph "用户访问层"
        A[管理员 Web 界面<br/>React + Ant Design]
        B[普通用户 Web 界面<br/>React + Ant Design]
        C[代理客户端<br/>订阅配置消费者]
    end
    
    subgraph "负载均衡层"
        D[Nginx<br/>反向代理 & SSL终结]
    end
    
    subgraph "AnyTLS Panel 管理面板"
        E[面板后端 API<br/>Go + Gin Framework]
        F[订阅生成服务<br/>配置文件生成]
    end
    
    subgraph "数据存储层"
        G[MySQL 数据库<br/>用户 & 节点数据]
        H[Redis 缓存<br/>会话 & 限流]
    end
    
    subgraph "节点服务层"
        I[V2bX 节点服务端 #1<br/>运行在服务器A]
        J[V2bX 节点服务端 #2<br/>运行在服务器B]
        K[V2bX 节点服务端 #N<br/>运行在服务器N]
    end
    
    subgraph "实际代理层"
        L[AnyTLS 代理服务 #1<br/>处理用户流量]
        M[AnyTLS 代理服务 #2<br/>处理用户流量]
        N[AnyTLS 代理服务 #N<br/>处理用户流量]
    end
    
    %% 面板前后端通信
    A --> D
    B --> D
    D --> E
    
    %% 订阅配置获取
    C --> D
    D --> F
    
    %% 数据存储
    E --> G
    E --> H
    F --> G
    
    %% V2bX API 通信
    I -.->|获取配置/上报数据| E
    J -.->|获取配置/上报数据| E
    K -.->|获取配置/上报数据| E
    
    %% 节点服务管理代理服务
    I --> L
    J --> M
    K --> N
    
    %% 用户实际代理连接
    C -.->|代理流量| L
    C -.->|代理流量| M
    C -.->|代理流量| N
```

### 技术栈选择

**前端技术栈:**
- React 18 + TypeScript
- Ant Design Pro (企业级管理后台框架)
- Ant Design + Pro Components UI组件库
- Axios + React Query HTTP客户端和状态管理
- React Router v6 路由管理
- Ant Design Charts 图表库
- Zustand 轻量级状态管理
- Less + CSS Modules 样式方案

**后端技术栈:**
- Go 1.21+ 
- Gin Web框架
- GORM数据库ORM
- JWT身份认证
- Redis缓存
- Logrus日志库

**数据库:**
- MySQL 8.0 主数据库
- Redis 7.0 缓存和会话存储

**部署:**
- Docker容器化
- Nginx反向代理
- Let's Encrypt SSL证书

### 前端架构最佳实践

**1. 项目结构**
```
src/
├── components/          # 通用组件
│   ├── AnyTLSConfig/   # AnyTLS配置相关组件
│   ├── Charts/         # 图表组件
│   ├── Forms/          # 表单组件
│   └── Tables/         # 表格组件
├── pages/              # 页面组件
│   ├── admin/          # 管理员页面
│   ├── user/           # 用户页面
│   └── auth/           # 认证页面
├── services/           # API服务
├── utils/              # 工具函数
├── hooks/              # 自定义Hooks
├── stores/             # Zustand状态管理
├── types/              # TypeScript类型定义
└── constants/          # 常量定义
```

**2. 状态管理策略**
```typescript
// 使用Zustand进行轻量级状态管理
interface UserStore {
  userInfo: UserInfo | null;
  setUserInfo: (user: UserInfo) => void;
  logout: () => void;
}

export const useUserStore = create<UserStore>((set) => ({
  userInfo: null,
  setUserInfo: (user) => set({ userInfo: user }),
  logout: () => set({ userInfo: null }),
}));

// 使用React Query进行服务端状态管理
export const useUserList = (params: UserListParams) => {
  return useQuery({
    queryKey: ['users', params],
    queryFn: () => getUserList(params),
    staleTime: 5 * 60 * 1000, // 5分钟
  });
};
```

**3. 组件设计原则**
- 单一职责：每个组件只负责一个功能
- 可复用性：通用组件支持多种配置
- 类型安全：完整的TypeScript类型定义
- 性能优化：使用React.memo和useMemo优化渲染

**4. API集成模式**
```typescript
// 统一的API客户端
class ApiClient {
  private instance: AxiosInstance;
  
  constructor() {
    this.instance = axios.create({
      baseURL: '/api',
      timeout: 10000,
    });
    
    this.setupInterceptors();
  }
  
  private setupInterceptors() {
    // 请求拦截器：添加认证token
    this.instance.interceptors.request.use((config) => {
      const token = getToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });
    
    // 响应拦截器：统一错误处理
    this.instance.interceptors.response.use(
      (response) => response.data,
      (error) => {
        if (error.response?.status === 401) {
          // 处理认证失败
          logout();
        }
        return Promise.reject(error);
      }
    );
  }
}
```

## Components and Interfaces

### 前端组件架构

```mermaid
graph TD
    A[App.tsx<br/>应用根组件] --> B[ProLayout<br/>Ant Design Pro布局]
    
    B --> C[AdminRoutes<br/>管理员路由]
    B --> D[UserRoutes<br/>用户路由]
    B --> E[AuthRoutes<br/>认证路由]
    
    C --> F[AdminDashboard<br/>管理员仪表板]
    C --> G[UserManagement<br/>用户管理]
    C --> H[NodeManagement<br/>节点管理]
    C --> I[TrafficAnalytics<br/>流量分析]
    C --> J[SystemSettings<br/>系统设置]
    
    D --> K[UserDashboard<br/>用户仪表板]
    D --> L[SubscriptionCenter<br/>订阅中心]
    D --> M[ProfileSettings<br/>个人设置]
    D --> N[UsageHistory<br/>使用历史]
    
    E --> O[LoginPage<br/>登录页面]
    E --> P[RegisterPage<br/>注册页面]
    E --> Q[ForgotPassword<br/>忘记密码]
    
    G --> R[UserTable<br/>ProTable用户表格]
    G --> S[UserForm<br/>ProForm用户表单]
    G --> T[UserModal<br/>用户操作弹窗]
    
    H --> U[NodeTable<br/>ProTable节点表格]
    H --> V[NodeForm<br/>ProForm节点表单]
    H --> W[NodeStatus<br/>节点状态监控]
    
    I --> X[TrafficChart<br/>流量图表]
    I --> Y[OnlineUsers<br/>在线用户统计]
    I --> Z[ReportExport<br/>报表导出]
```

### 基于Ant Design Pro的组件设计

**1. 管理员仪表板组件**
```typescript
// src/pages/admin/Dashboard/index.tsx
import { PageContainer, ProCard, Statistic } from '@ant-design/pro-components';
import { Col, Row } from 'antd';
import { UserOutlined, NodeIndexOutlined, CloudServerOutlined } from '@ant-design/icons';
import { TrafficChart, OnlineUsersChart, NodeStatusChart } from './components';

const AdminDashboard: React.FC = () => {
  const { data: stats } = useQuery(['admin-stats'], getAdminStats);
  
  return (
    <PageContainer
      header={{
        title: '管理员仪表板',
        breadcrumb: {},
      }}
    >
      <Row gutter={[16, 16]}>
        <Col span={6}>
          <ProCard>
            <Statistic
              title="总用户数"
              value={stats?.totalUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </ProCard>
        </Col>
        <Col span={6}>
          <ProCard>
            <Statistic
              title="在线用户"
              value={stats?.onlineUsers}
              prefix={<CloudServerOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </ProCard>
        </Col>
        <Col span={6}>
          <ProCard>
            <Statistic
              title="活跃节点"
              value={stats?.activeNodes}
              prefix={<NodeIndexOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </ProCard>
        </Col>
        <Col span={6}>
          <ProCard>
            <Statistic
              title="今日流量"
              value={stats?.todayTraffic}
              suffix="GB"
              valueStyle={{ color: '#cf1322' }}
            />
          </ProCard>
        </Col>
      </Row>
      
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col span={12}>
          <ProCard title="流量趋势" headerBordered>
            <TrafficChart />
          </ProCard>
        </Col>
        <Col span={12}>
          <ProCard title="在线用户分布" headerBordered>
            <OnlineUsersChart />
          </ProCard>
        </Col>
      </Row>
      
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col span={24}>
          <ProCard title="节点状态监控" headerBordered>
            <NodeStatusChart />
          </ProCard>
        </Col>
      </Row>
    </PageContainer>
  );
};
```

**2. 用户管理组件**
```typescript
// src/pages/admin/UserManagement/index.tsx
import { PageContainer, ProTable, ProColumns } from '@ant-design/pro-components';
import { Button, Tag, Space, Modal } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { UserForm } from './components/UserForm';
import { formatBytes, formatDate } from '@/utils';

const UserManagement: React.FC = () => {
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [currentUser, setCurrentUser] = useState<UserInfo | null>(null);

  const columns: ProColumns<UserInfo>[] = [
    {
      title: 'UUID',
      dataIndex: 'uuid',
      copyable: true,
      ellipsis: true,
      width: 200,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      sorter: true,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      copyable: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: {
        active: { text: '正常', status: 'Success' },
        inactive: { text: '禁用', status: 'Error' },
        expired: { text: '过期', status: 'Warning' },
      },
    },
    {
      title: '流量使用',
      dataIndex: 'traffic_used',
      render: (_, record) => (
        <div>
          <div>{formatBytes(record.traffic_used)} / {formatBytes(record.traffic_limit)}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            使用率: {((record.traffic_used / record.traffic_limit) * 100).toFixed(1)}%
          </div>
        </div>
      ),
      sorter: true,
    },
    {
      title: '设备限制',
      dataIndex: 'device_limit',
      width: 100,
    },
    {
      title: '到期时间',
      dataIndex: 'expired_at',
      render: (text) => text ? formatDate(text) : '永不过期',
      sorter: true,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      render: (text) => formatDate(text),
      sorter: true,
    },
    {
      title: '操作',
      valueType: 'option',
      width: 150,
      render: (_, record) => [
        <Button
          key="edit"
          type="link"
          icon={<EditOutlined />}
          onClick={() => handleEdit(record)}
        >
          编辑
        </Button>,
        <Button
          key="delete"
          type="link"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleDelete(record)}
        >
          删除
        </Button>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<UserInfo>
        columns={columns}
        request={async (params, sort, filter) => {
          const { data } = await getUserList({
            ...params,
            sort,
            filter,
          });
          return {
            data: data.list,
            success: true,
            total: data.total,
          };
        }}
        rowKey="id"
        pagination={{
          defaultPageSize: 20,
          showSizeChanger: true,
        }}
        search={{
          labelWidth: 'auto',
        }}
        toolBarRender={() => [
          <Button
            key="create"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            新建用户
          </Button>,
        ]}
        options={{
          setting: {
            listsHeight: 400,
          },
        }}
      />
      
      <Modal
        title="创建用户"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
        width={600}
      >
        <UserForm
          onFinish={handleCreate}
          onCancel={() => setCreateModalVisible(false)}
        />
      </Modal>
      
      <Modal
        title="编辑用户"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
        width={600}
      >
        <UserForm
          initialValues={currentUser}
          onFinish={handleUpdate}
          onCancel={() => setEditModalVisible(false)}
        />
      </Modal>
    </PageContainer>
  );
};
```

**3. AnyTLS节点管理组件**
```typescript
// src/pages/admin/NodeManagement/index.tsx
import { PageContainer, ProTable, ProColumns, ProForm, ProFormText, ProFormDigit, ProFormTextArea, ProFormSelect } from '@ant-design/pro-components';
import { Badge, Button, Space, Modal, message } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, PlayCircleOutlined, PauseCircleOutlined } from '@ant-design/icons';

const NodeManagement: React.FC = () => {
  const columns: ProColumns<NodeInfo>[] = [
    {
      title: '节点名称',
      dataIndex: 'name',
      sorter: true,
    },
    {
      title: '服务器地址',
      dataIndex: 'host',
      copyable: true,
    },
    {
      title: '端口',
      dataIndex: 'port',
      width: 80,
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (_, record) => {
        const statusMap = {
          online: { color: 'green', text: '在线' },
          offline: { color: 'red', text: '离线' },
          maintenance: { color: 'orange', text: '维护中' },
        };
        const status = statusMap[record.status];
        return <Badge color={status.color} text={status.text} />;
      },
      filters: [
        { text: '在线', value: 'online' },
        { text: '离线', value: 'offline' },
        { text: '维护中', value: 'maintenance' },
      ],
    },
    {
      title: '在线用户',
      dataIndex: 'online_users',
      render: (_, record) => `${record.online_users || 0} / ${record.max_users || '∞'}`,
      sorter: true,
    },
    {
      title: '流量倍率',
      dataIndex: 'traffic_rate',
      render: (text) => `${text}x`,
      sorter: true,
    },
    {
      title: '填充策略',
      dataIndex: 'padding_scheme',
      render: (schemes) => {
        if (!schemes || schemes.length === 0) return '默认';
        return schemes.join(', ');
      },
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      render: (text) => formatDate(text),
      sorter: true,
    },
    {
      title: '操作',
      valueType: 'option',
      width: 200,
      render: (_, record) => [
        <Button
          key="toggle"
          type="link"
          icon={record.status === 'online' ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
          onClick={() => handleToggleStatus(record)}
        >
          {record.status === 'online' ? '停用' : '启用'}
        </Button>,
        <Button
          key="edit"
          type="link"
          icon={<EditOutlined />}
          onClick={() => handleEdit(record)}
        >
          编辑
        </Button>,
        <Button
          key="delete"
          type="link"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleDelete(record)}
        >
          删除
        </Button>,
      ],
    },
  ];

  return (
    <PageContainer>
      <ProTable<NodeInfo>
        columns={columns}
        request={getNodeList}
        rowKey="id"
        toolBarRender={() => [
          <Button
            key="create"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            新建节点
          </Button>,
        ]}
      />
      
      <Modal
        title="AnyTLS节点配置"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
        width={800}
      >
        <ProForm
          onFinish={handleCreate}
          layout="horizontal"
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
        >
          <ProFormText
            name="name"
            label="节点名称"
            rules={[{ required: true, message: '请输入节点名称' }]}
            placeholder="例如: 香港节点01"
          />
          <ProFormText
            name="host"
            label="服务器地址"
            rules={[
              { required: true, message: '请输入服务器地址' },
              { pattern: /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$|^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, message: '请输入有效的IP地址或域名' }
            ]}
            placeholder="例如: ******* 或 example.com"
          />
          <ProFormDigit
            name="port"
            label="端口"
            rules={[{ required: true, message: '请输入端口号' }]}
            min={1}
            max={65535}
            placeholder="例如: 8443"
          />
          <ProFormText.Password
            name="password"
            label="连接密码"
            rules={[{ required: true, message: '请输入连接密码' }]}
            placeholder="AnyTLS连接密码"
          />
          <ProFormText
            name="server_name"
            label="SNI服务器名"
            placeholder="例如: example.com (可选)"
          />
          <ProFormTextArea
            name="padding_scheme"
            label="填充策略"
            placeholder="每行一个策略，例如: random:100-200"
            fieldProps={{
              rows: 4,
            }}
            extra="AnyTLS填充策略配置，用于流量混淆"
          />
          <ProFormDigit
            name="max_users"
            label="最大用户数"
            min={0}
            placeholder="0表示无限制"
          />
          <ProFormDigit
            name="traffic_rate"
            label="流量倍率"
            min={0.1}
            max={10}
            fieldProps={{ step: 0.1 }}
            initialValue={1.0}
            extra="流量计费倍率，1.0表示正常计费"
          />
          <ProFormSelect
            name="status"
            label="初始状态"
            options={[
              { label: '在线', value: 'online' },
              { label: '离线', value: 'offline' },
              { label: '维护中', value: 'maintenance' },
            ]}
            initialValue="offline"
          />
        </ProForm>
      </Modal>
    </PageContainer>
  );
};
```

**4. 多客户端订阅中心组件**
```typescript
// src/pages/user/SubscriptionCenter/index.tsx
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { Tabs, Button, QRCode, Typography, Space, Divider, Alert } from 'antd';
import { CopyOutlined, DownloadOutlined, AndroidOutlined, AppleOutlined, WindowsOutlined } from '@ant-design/icons';
import { useState } from 'react';

const { TabPane } = Tabs;
const { Text, Link } = Typography;

interface SubscriptionInfo {
  url: string;
  qrCode: string;
  filename: string;
  description: string;
  clientInfo: {
    name: string;
    icon: React.ReactNode;
    downloadUrl: string;
    platforms: string[];
  };
}

const SubscriptionCenter: React.FC = () => {
  const [activeClient, setActiveClient] = useState('clash');
  
  const subscriptions: Record<string, SubscriptionInfo> = {
    clash: {
      url: '/api/user/subscription/clash',
      qrCode: 'clash://install-config?url=...',
      filename: 'clash.yaml',
      description: 'Clash 是一个跨平台的代理客户端，支持规则分流',
      clientInfo: {
        name: 'Clash',
        icon: <AndroidOutlined />,
        downloadUrl: 'https://github.com/Dreamacro/clash/releases',
        platforms: ['Windows', 'macOS', 'Linux', 'Android'],
      },
    },
    'clash-meta': {
      url: '/api/user/subscription/clash-meta',
      qrCode: 'clash://install-config?url=...',
      filename: 'clash-meta.yaml',
      description: 'Clash Meta 是 Clash 的增强版本，支持更多协议',
      clientInfo: {
        name: 'Clash Meta',
        icon: <AndroidOutlined />,
        downloadUrl: 'https://github.com/MetaCubeX/Clash.Meta/releases',
        platforms: ['Windows', 'macOS', 'Linux', 'Android'],
      },
    },
    'sing-box': {
      url: '/api/user/subscription/sing-box',
      qrCode: 'sing-box://import-config?url=...',
      filename: 'sing-box.json',
      description: 'Sing-box 是新一代通用代理平台',
      clientInfo: {
        name: 'Sing-box',
        icon: <WindowsOutlined />,
        downloadUrl: 'https://github.com/SagerNet/sing-box/releases',
        platforms: ['Windows', 'macOS', 'Linux', 'Android', 'iOS'],
      },
    },
    v2ray: {
      url: '/api/user/subscription/v2ray',
      qrCode: 'v2ray://import?url=...',
      filename: 'v2ray.txt',
      description: '通用 V2Ray 订阅格式，兼容大多数客户端',
      clientInfo: {
        name: 'V2Ray',
        icon: <WindowsOutlined />,
        downloadUrl: 'https://github.com/v2fly/v2ray-core/releases',
        platforms: ['Windows', 'macOS', 'Linux'],
      },
    },
    shadowrocket: {
      url: '/api/user/subscription/shadowrocket',
      qrCode: 'shadowrocket://add/sub?url=...',
      filename: 'shadowrocket.txt',
      description: 'Shadowrocket 是 iOS 平台的优秀代理客户端',
      clientInfo: {
        name: 'Shadowrocket',
        icon: <AppleOutlined />,
        downloadUrl: 'https://apps.apple.com/app/shadowrocket/id932747118',
        platforms: ['iOS'],
      },
    },
    'quantumult-x': {
      url: '/api/user/subscription/quantumult-x',
      qrCode: 'quantumult-x:///add-resource?remote-resource=...',
      filename: 'quantumult-x.txt',
      description: 'Quantumult X 是功能强大的 iOS 代理客户端',
      clientInfo: {
        name: 'Quantumult X',
        icon: <AppleOutlined />,
        downloadUrl: 'https://apps.apple.com/app/quantumult-x/id1443988620',
        platforms: ['iOS'],
      },
    },
  };

  const currentSub = subscriptions[activeClient];

  const handleCopyUrl = async () => {
    try {
      await navigator.clipboard.writeText(currentSub.url);
      message.success('订阅链接已复制到剪贴板');
    } catch (error) {
      message.error('复制失败，请手动复制');
    }
  };

  const handleDownloadConfig = () => {
    const link = document.createElement('a');
    link.href = currentSub.url;
    link.download = currentSub.filename;
    link.click();
  };

  return (
    <PageContainer
      header={{
        title: '订阅中心',
        subTitle: '选择您的客户端类型获取相应的订阅配置',
      }}
    >
      <Alert
        message="使用说明"
        description="请根据您使用的客户端类型选择相应的订阅格式。每种客户端都有专门优化的配置文件。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <ProCard>
        <Tabs activeKey={activeClient} onChange={setActiveClient} type="card">
          {Object.entries(subscriptions).map(([key, sub]) => (
            <TabPane
              tab={
                <Space>
                  {sub.clientInfo.icon}
                  {sub.clientInfo.name}
                </Space>
              }
              key={key}
            >
              <div style={{ display: 'flex', gap: 24 }}>
                {/* 左侧：二维码和基本信息 */}
                <div style={{ flex: '0 0 300px', textAlign: 'center' }}>
                  <QRCode value={currentSub.qrCode} size={200} />
                  <div style={{ marginTop: 16 }}>
                    <Text type="secondary">扫描二维码快速导入</Text>
                  </div>
                  
                  <Divider />
                  
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Button
                      type="primary"
                      icon={<CopyOutlined />}
                      onClick={handleCopyUrl}
                      block
                      size="large"
                    >
                      复制订阅链接
                    </Button>
                    <Button
                      icon={<DownloadOutlined />}
                      onClick={handleDownloadConfig}
                      block
                      size="large"
                    >
                      下载配置文件
                    </Button>
                  </Space>
                </div>

                {/* 右侧：详细信息和使用指南 */}
                <div style={{ flex: 1 }}>
                  <Typography>
                    <Text strong style={{ fontSize: 16 }}>
                      {currentSub.clientInfo.name}
                    </Text>
                    <br />
                    <Text type="secondary">{currentSub.description}</Text>
                  </Typography>

                  <Divider orientation="left">支持平台</Divider>
                  <Space wrap>
                    {currentSub.clientInfo.platforms.map(platform => (
                      <Button key={platform} size="small" type="dashed">
                        {platform}
                      </Button>
                    ))}
                  </Space>

                  <Divider orientation="left">客户端下载</Divider>
                  <Link href={currentSub.clientInfo.downloadUrl} target="_blank">
                    点击下载 {currentSub.clientInfo.name} 客户端
                  </Link>

                  <Divider orientation="left">使用方法</Divider>
                  <div>
                    {activeClient === 'clash' && (
                      <ol>
                        <li>下载并安装 Clash 客户端</li>
                        <li>复制上方的订阅链接</li>
                        <li>在 Clash 中添加配置文件，粘贴订阅链接</li>
                        <li>更新配置文件并选择代理节点</li>
                        <li>开启系统代理即可使用</li>
                      </ol>
                    )}
                    {activeClient === 'sing-box' && (
                      <ol>
                        <li>下载并安装 Sing-box 客户端</li>
                        <li>下载上方的配置文件</li>
                        <li>在 Sing-box 中导入配置文件</li>
                        <li>启动服务即可使用</li>
                      </ol>
                    )}
                    {(activeClient === 'v2ray' || activeClient === 'shadowrocket' || activeClient === 'quantumult-x') && (
                      <ol>
                        <li>下载并安装相应的客户端</li>
                        <li>复制上方的订阅链接</li>
                        <li>在客户端中添加订阅，粘贴链接</li>
                        <li>更新订阅并选择节点</li>
                        <li>开启代理即可使用</li>
                      </ol>
                    )}
                  </div>

                  <Divider orientation="left">订阅信息</Divider>
                  <Space direction="vertical">
                    <Text>
                      <Text strong>订阅链接：</Text>
                      <Text code copyable style={{ wordBreak: 'break-all' }}>
                        {currentSub.url}
                      </Text>
                    </Text>
                    <Text>
                      <Text strong>文件名：</Text>
                      <Text code>{currentSub.filename}</Text>
                    </Text>
                    <Text type="secondary">
                      订阅链接会自动更新，无需手动刷新
                    </Text>
                  </Space>
                </div>
              </div>
            </TabPane>
          ))}
        </Tabs>
      </ProCard>
    </PageContainer>
  );
};
```

### 后端API模块设计

```mermaid
graph TD
    A[main.go<br/>应用入口] --> B[Router<br/>路由配置]
    
    B --> C[Auth Middleware<br/>身份认证]
    B --> D[Rate Limit Middleware<br/>速率限制]
    B --> E[CORS Middleware<br/>跨域处理]
    
    C --> F[Admin API<br/>管理员接口]
    C --> G[User API<br/>用户接口]
    C --> H[V2bX API<br/>后端兼容接口]
    
    F --> I[UserController<br/>用户管理]
    F --> J[NodeController<br/>节点管理]
    F --> K[TrafficController<br/>流量统计]
    
    G --> L[ProfileController<br/>个人资料]
    G --> M[SubscriptionController<br/>订阅管理]
    
    H --> N[UniProxyController<br/>V2bX兼容API]
    
    I --> O[UserService<br/>用户业务逻辑]
    J --> P[NodeService<br/>节点业务逻辑]
    K --> Q[TrafficService<br/>流量业务逻辑]
    L --> O
    M --> R[SubscriptionService<br/>订阅业务逻辑]
    N --> S[V2bXService<br/>V2bX兼容服务]
```

### 核心接口定义

**管理员API接口:**
```
# 用户管理
GET    /api/admin/users              # 获取用户列表
POST   /api/admin/users              # 创建用户
PUT    /api/admin/users/:id          # 更新用户
DELETE /api/admin/users/:id          # 删除用户

# 节点管理
GET    /api/admin/nodes              # 获取节点列表
POST   /api/admin/nodes              # 创建节点
PUT    /api/admin/nodes/:id          # 更新节点
DELETE /api/admin/nodes/:id          # 删除节点

# 权限组管理
GET    /api/admin/permission-groups  # 获取权限组列表
POST   /api/admin/permission-groups  # 创建权限组
PUT    /api/admin/permission-groups/:id # 更新权限组
DELETE /api/admin/permission-groups/:id # 删除权限组
POST   /api/admin/permission-groups/:id/nodes # 添加节点到权限组
DELETE /api/admin/permission-groups/:id/nodes/:nodeId # 从权限组移除节点

# 商品管理
GET    /api/admin/products           # 获取商品列表
POST   /api/admin/products           # 创建商品
PUT    /api/admin/products/:id       # 更新商品
DELETE /api/admin/products/:id       # 删除商品

# 订单管理
GET    /api/admin/orders             # 获取订单列表
PUT    /api/admin/orders/:id/status  # 更新订单状态

# 统计信息
GET    /api/admin/traffic            # 获取流量统计
GET    /api/admin/online             # 获取在线用户
GET    /api/admin/revenue            # 获取收入统计
```

**用户API接口:**
```
# 用户资料
GET    /api/user/profile             # 获取用户资料
PUT    /api/user/profile             # 更新用户资料
GET    /api/user/traffic             # 获取流量使用情况

# 商品和订阅
GET    /api/user/products            # 获取可购买商品列表
GET    /api/user/products/:id        # 获取商品详情
POST   /api/user/orders              # 创建订单
GET    /api/user/orders              # 获取用户订单列表
GET    /api/user/orders/:id          # 获取订单详情
POST   /api/user/orders/:id/pay      # 支付订单

GET    /api/user/subscription        # 获取当前订阅信息
GET    /api/user/subscription/history # 获取订阅历史

# 多客户端订阅接口
GET    /api/user/subscription/clash          # Clash订阅
GET    /api/user/subscription/clash-meta     # Clash Meta订阅
GET    /api/user/subscription/sing-box       # Sing-box订阅
GET    /api/user/subscription/v2ray          # V2Ray订阅
GET    /api/user/subscription/shadowrocket   # Shadowrocket订阅
GET    /api/user/subscription/quantumult-x   # QuantumultX订阅
GET    /api/user/subscription/surge          # Surge订阅

# 通用订阅接口（支持client参数）
GET    /api/subscription/{token}?client=clash    # 带token的订阅链接
```

**V2bX兼容API接口:**
```
GET    /api/v1/server/UniProxy/config     # 获取节点配置
GET    /api/v1/server/UniProxy/user       # 获取用户列表
POST   /api/v1/server/UniProxy/push       # 上报用户流量
POST   /api/v1/server/UniProxy/alive      # 上报在线用户
GET    /api/v1/server/UniProxy/alivelist  # 获取在线统计
```

### 协议扩展架构设计

**1. 协议抽象接口**
```typescript
// 协议配置生成接口
interface ProtocolConfigGenerator {
  generateUri(node: NodeInfo, userUuid: string): string;
  generateConfig(node: NodeInfo, userUuid: string): any;
  validateConfig(config: any): boolean;
}

// AnyTLS协议实现
class AnyTLSConfigGenerator implements ProtocolConfigGenerator {
  generateUri(node: NodeInfo, userUuid: string): string {
    const config = node.config as AnyTLSConfig;
    const params = new URLSearchParams({
      password: node.password,
      uuid: userUuid,
    });
    
    if (config.paddingScheme?.length) {
      params.set('padding', config.paddingScheme.join(','));
    }
    
    if (node.serverName) {
      params.set('sni', node.serverName);
    }
    
    return `anytls://${node.host}:${node.port}?${params.toString()}`;
  }
  
  generateConfig(node: NodeInfo, userUuid: string): any {
    return {
      name: node.name,
      type: 'anytls',
      server: node.host,
      port: node.port,
      password: node.password,
      uuid: userUuid,
      'padding-scheme': (node.config as AnyTLSConfig).paddingScheme,
      sni: node.serverName,
    };
  }
  
  validateConfig(config: AnyTLSConfig): boolean {
    // AnyTLS配置验证逻辑
    return true;
  }
}

// 协议工厂
class ProtocolFactory {
  private static generators: Map<string, ProtocolConfigGenerator> = new Map([
    ['anytls', new AnyTLSConfigGenerator()],
    // 未来可以添加其他协议
    // ['vmess', new VmessConfigGenerator()],
    // ['trojan', new TrojanConfigGenerator()],
  ]);
  
  static getGenerator(protocol: string): ProtocolConfigGenerator {
    const generator = this.generators.get(protocol);
    if (!generator) {
      throw new Error(`Unsupported protocol: ${protocol}`);
    }
    return generator;
  }
  
  static getSupportedProtocols(): string[] {
    return Array.from(this.generators.keys());
  }
}
```

**2. 当前AnyTLS实现**
```typescript
interface AnyTLSConfig {
  paddingScheme?: string[];
}

interface NodeInfo {
  id: number;
  name: string;
  protocol: string;
  host: string;
  port: number;
  password: string;
  config: AnyTLSConfig | any; // 根据protocol类型确定具体配置
  serverName?: string;
  status: 'online' | 'offline' | 'maintenance';
}
```

**2. 填充策略配置界面**
```typescript
// 填充策略编辑器组件
const PaddingSchemeEditor: React.FC<{
  value?: string[];
  onChange?: (schemes: string[]) => void;
}> = ({ value = [], onChange }) => {
  const [schemes, setSchemes] = useState(value);
  
  const handleAdd = () => {
    const newSchemes = [...schemes, 'random:100-200'];
    setSchemes(newSchemes);
    onChange?.(newSchemes);
  };
  
  const handleRemove = (index: number) => {
    const newSchemes = schemes.filter((_, i) => i !== index);
    setSchemes(newSchemes);
    onChange?.(newSchemes);
  };
  
  return (
    <div>
      {schemes.map((scheme, index) => (
        <Input.Group key={index} compact style={{ marginBottom: 8 }}>
          <Input
            style={{ width: 'calc(100% - 32px)' }}
            value={scheme}
            onChange={(e) => {
              const newSchemes = [...schemes];
              newSchemes[index] = e.target.value;
              setSchemes(newSchemes);
              onChange?.(newSchemes);
            }}
            placeholder="例如: random:100-200"
          />
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleRemove(index)}
          />
        </Input.Group>
      ))}
      <Button
        type="dashed"
        onClick={handleAdd}
        block
        icon={<PlusOutlined />}
      >
        添加填充策略
      </Button>
    </div>
  );
};
```

**3. 多客户端订阅配置生成**
```typescript
// 客户端类型枚举
enum ClientType {
  CLASH = 'clash',
  CLASH_META = 'clash-meta', 
  SING_BOX = 'sing-box',
  V2RAY = 'v2ray',
  SHADOWROCKET = 'shadowrocket',
  QUANTUMULT_X = 'quantumult-x',
  SURGE = 'surge',
  GENERIC = 'generic' // 通用格式
}

// 客户端配置生成接口
interface ClientConfigGenerator {
  generateSubscription(nodes: NodeInfo[], userUuid: string): string;
  getContentType(): string;
  getFileExtension(): string;
}

// Clash配置生成器
class ClashConfigGenerator implements ClientConfigGenerator {
  generateSubscription(nodes: NodeInfo[], userUuid: string): string {
    const proxies = nodes.map(node => {
      if (node.protocol === 'anytls') {
        const config = node.config as AnyTLSConfig;
        return {
          name: node.name,
          type: 'anytls',
          server: node.host,
          port: node.port,
          password: node.password,
          uuid: userUuid,
          'padding-scheme': config.paddingScheme,
          sni: node.serverName,
        };
      }
      // 其他协议处理...
    });

    const clashConfig = {
      port: 7890,
      'socks-port': 7891,
      'allow-lan': false,
      mode: 'rule',
      'log-level': 'info',
      'external-controller': '127.0.0.1:9090',
      proxies,
      'proxy-groups': [
        {
          name: '🚀 节点选择',
          type: 'select',
          proxies: ['♻️ 自动选择', '🎯 全球直连', ...proxies.map(p => p.name)],
        },
        {
          name: '♻️ 自动选择',
          type: 'url-test',
          proxies: proxies.map(p => p.name),
          url: 'http://www.gstatic.com/generate_204',
          interval: 300,
        },
      ],
      rules: [
        'DOMAIN-SUFFIX,local,🎯 全球直连',
        'IP-CIDR,*********/8,🎯 全球直连',
        'IP-CIDR,**********/12,🎯 全球直连',
        'IP-CIDR,***********/16,🎯 全球直连',
        'IP-CIDR,10.0.0.0/8,🎯 全球直连',
        'IP-CIDR,********/8,🎯 全球直连',
        'IP-CIDR,**********/10,🎯 全球直连',
        'GEOIP,CN,🎯 全球直连',
        'MATCH,🚀 节点选择',
      ],
    };

    return yaml.stringify(clashConfig);
  }

  getContentType(): string {
    return 'application/x-yaml';
  }

  getFileExtension(): string {
    return 'yaml';
  }
}

// Sing-box配置生成器
class SingBoxConfigGenerator implements ClientConfigGenerator {
  generateSubscription(nodes: NodeInfo[], userUuid: string): string {
    const outbounds = nodes.map(node => {
      if (node.protocol === 'anytls') {
        const config = node.config as AnyTLSConfig;
        return {
          tag: node.name,
          type: 'anytls',
          server: node.host,
          server_port: node.port,
          password: node.password,
          uuid: userUuid,
          padding_scheme: config.paddingScheme,
          tls: {
            enabled: true,
            server_name: node.serverName,
          },
        };
      }
      // 其他协议处理...
    });

    const singboxConfig = {
      log: {
        level: 'info',
        timestamp: true,
      },
      inbounds: [
        {
          type: 'mixed',
          listen: '127.0.0.1',
          listen_port: 2080,
        },
      ],
      outbounds: [
        {
          tag: 'proxy',
          type: 'selector',
          outbounds: outbounds.map(o => o.tag),
        },
        {
          tag: 'direct',
          type: 'direct',
        },
        {
          tag: 'block',
          type: 'block',
        },
        ...outbounds,
      ],
      route: {
        geoip: {
          path: 'geoip.db',
        },
        geosite: {
          path: 'geosite.db',
        },
        rules: [
          {
            geosite: 'cn',
            outbound: 'direct',
          },
          {
            geoip: 'cn',
            outbound: 'direct',
          },
        ],
        final: 'proxy',
      },
    };

    return JSON.stringify(singboxConfig, null, 2);
  }

  getContentType(): string {
    return 'application/json';
  }

  getFileExtension(): string {
    return 'json';
  }
}

// V2Ray配置生成器
class V2RayConfigGenerator implements ClientConfigGenerator {
  generateSubscription(nodes: NodeInfo[], userUuid: string): string {
    // 生成V2Ray格式的订阅链接（Base64编码的URI列表）
    const uris = nodes.map(node => {
      const generator = ProtocolFactory.getGenerator(node.protocol);
      return generator.generateUri(node, userUuid);
    });
    
    return btoa(uris.join('\n'));
  }

  getContentType(): string {
    return 'text/plain';
  }

  getFileExtension(): string {
    return 'txt';
  }
}

// 客户端配置工厂
class ClientConfigFactory {
  private static generators: Map<ClientType, ClientConfigGenerator> = new Map([
    [ClientType.CLASH, new ClashConfigGenerator()],
    [ClientType.CLASH_META, new ClashConfigGenerator()], // 复用Clash格式
    [ClientType.SING_BOX, new SingBoxConfigGenerator()],
    [ClientType.V2RAY, new V2RayConfigGenerator()],
    [ClientType.SHADOWROCKET, new V2RayConfigGenerator()], // 复用V2Ray格式
    [ClientType.QUANTUMULT_X, new V2RayConfigGenerator()], // 复用V2Ray格式
    [ClientType.GENERIC, new V2RayConfigGenerator()], // 默认使用V2Ray格式
  ]);

  static getGenerator(clientType: ClientType): ClientConfigGenerator {
    const generator = this.generators.get(clientType);
    if (!generator) {
      throw new Error(`Unsupported client type: ${clientType}`);
    }
    return generator;
  }

  static getSupportedClients(): ClientType[] {
    return Array.from(this.generators.keys());
  }
}

// 统一的订阅生成接口
export const generateSubscription = async (
  userUuid: string, 
  clientType: ClientType = ClientType.GENERIC
): Promise<{
  content: string;
  contentType: string;
  filename: string;
}> => {
  const nodes = await getAvailableNodes(userUuid);
  const generator = ClientConfigFactory.getGenerator(clientType);
  
  return {
    content: generator.generateSubscription(nodes, userUuid),
    contentType: generator.getContentType(),
    filename: `subscription.${generator.getFileExtension()}`,
  };
};
```

**4. 后端多客户端订阅生成架构**
```go
// 客户端类型
type ClientType string

const (
    ClientClash       ClientType = "clash"
    ClientClashMeta   ClientType = "clash-meta"
    ClientSingBox     ClientType = "sing-box"
    ClientV2Ray       ClientType = "v2ray"
    ClientShadowrocket ClientType = "shadowrocket"
    ClientQuantumultX ClientType = "quantumult-x"
    ClientSurge       ClientType = "surge"
    ClientGeneric     ClientType = "generic"
)

// 订阅生成结果
type SubscriptionResult struct {
    Content     string `json:"content"`
    ContentType string `json:"content_type"`
    Filename    string `json:"filename"`
}

// 客户端配置生成接口
type ClientConfigGenerator interface {
    GenerateSubscription(nodes []*Node, userUUID string) (*SubscriptionResult, error)
    GetSupportedProtocols() []string
}

// Clash配置生成器
type ClashConfigGenerator struct{}

func (g *ClashConfigGenerator) GenerateSubscription(nodes []*Node, userUUID string) (*SubscriptionResult, error) {
    var proxies []map[string]interface{}
    
    for _, node := range nodes {
        switch node.Protocol {
        case "anytls":
            var config AnyTLSConfig
            if err := json.Unmarshal(node.Config, &config); err != nil {
                continue
            }
            
            proxy := map[string]interface{}{
                "name":           node.Name,
                "type":           "anytls",
                "server":         node.Host,
                "port":           node.Port,
                "password":       node.Password,
                "uuid":           userUUID,
                "padding-scheme": config.PaddingScheme,
                "sni":            node.ServerName,
            }
            proxies = append(proxies, proxy)
        }
    }
    
    clashConfig := map[string]interface{}{
        "port":                7890,
        "socks-port":          7891,
        "allow-lan":           false,
        "mode":                "rule",
        "log-level":           "info",
        "external-controller": "127.0.0.1:9090",
        "proxies":             proxies,
        "proxy-groups": []map[string]interface{}{
            {
                "name":    "🚀 节点选择",
                "type":    "select",
                "proxies": append([]string{"♻️ 自动选择", "🎯 全球直连"}, getProxyNames(proxies)...),
            },
            {
                "name":     "♻️ 自动选择",
                "type":     "url-test",
                "proxies":  getProxyNames(proxies),
                "url":      "http://www.gstatic.com/generate_204",
                "interval": 300,
            },
        },
        "rules": []string{
            "DOMAIN-SUFFIX,local,🎯 全球直连",
            "IP-CIDR,*********/8,🎯 全球直连",
            "IP-CIDR,**********/12,🎯 全球直连",
            "IP-CIDR,***********/16,🎯 全球直连",
            "IP-CIDR,10.0.0.0/8,🎯 全球直连",
            "GEOIP,CN,🎯 全球直连",
            "MATCH,🚀 节点选择",
        },
    }
    
    yamlContent, err := yaml.Marshal(clashConfig)
    if err != nil {
        return nil, err
    }
    
    return &SubscriptionResult{
        Content:     string(yamlContent),
        ContentType: "application/x-yaml",
        Filename:    "clash.yaml",
    }, nil
}

func (g *ClashConfigGenerator) GetSupportedProtocols() []string {
    return []string{"anytls"}
}

// Sing-box配置生成器
type SingBoxConfigGenerator struct{}

func (g *SingBoxConfigGenerator) GenerateSubscription(nodes []*Node, userUUID string) (*SubscriptionResult, error) {
    var outbounds []map[string]interface{}
    var outboundTags []string
    
    for _, node := range nodes {
        switch node.Protocol {
        case "anytls":
            var config AnyTLSConfig
            if err := json.Unmarshal(node.Config, &config); err != nil {
                continue
            }
            
            outbound := map[string]interface{}{
                "tag":         node.Name,
                "type":        "anytls",
                "server":      node.Host,
                "server_port": node.Port,
                "password":    node.Password,
                "uuid":        userUUID,
                "padding_scheme": config.PaddingScheme,
                "tls": map[string]interface{}{
                    "enabled":     true,
                    "server_name": node.ServerName,
                },
            }
            outbounds = append(outbounds, outbound)
            outboundTags = append(outboundTags, node.Name)
        }
    }
    
    singboxConfig := map[string]interface{}{
        "log": map[string]interface{}{
            "level":     "info",
            "timestamp": true,
        },
        "inbounds": []map[string]interface{}{
            {
                "type":        "mixed",
                "listen":      "127.0.0.1",
                "listen_port": 2080,
            },
        },
        "outbounds": append([]map[string]interface{}{
            {
                "tag":       "proxy",
                "type":      "selector",
                "outbounds": outboundTags,
            },
            {
                "tag":  "direct",
                "type": "direct",
            },
            {
                "tag":  "block",
                "type": "block",
            },
        }, outbounds...),
        "route": map[string]interface{}{
            "geoip": map[string]interface{}{
                "path": "geoip.db",
            },
            "geosite": map[string]interface{}{
                "path": "geosite.db",
            },
            "rules": []map[string]interface{}{
                {
                    "geosite":  "cn",
                    "outbound": "direct",
                },
                {
                    "geoip":    "cn",
                    "outbound": "direct",
                },
            },
            "final": "proxy",
        },
    }
    
    jsonContent, err := json.MarshalIndent(singboxConfig, "", "  ")
    if err != nil {
        return nil, err
    }
    
    return &SubscriptionResult{
        Content:     string(jsonContent),
        ContentType: "application/json",
        Filename:    "sing-box.json",
    }, nil
}

func (g *SingBoxConfigGenerator) GetSupportedProtocols() []string {
    return []string{"anytls"}
}

// V2Ray URI生成器
type V2RayConfigGenerator struct{}

func (g *V2RayConfigGenerator) GenerateSubscription(nodes []*Node, userUUID string) (*SubscriptionResult, error) {
    var uris []string
    
    for _, node := range nodes {
        handler, err := GetProtocolHandler(node.Protocol)
        if err != nil {
            continue
        }
        
        uri, err := handler.GenerateURI(node, userUUID)
        if err != nil {
            continue
        }
        
        uris = append(uris, uri)
    }
    
    content := base64.StdEncoding.EncodeToString([]byte(strings.Join(uris, "\n")))
    
    return &SubscriptionResult{
        Content:     content,
        ContentType: "text/plain",
        Filename:    "subscription.txt",
    }, nil
}

func (g *V2RayConfigGenerator) GetSupportedProtocols() []string {
    return []string{"anytls"}
}

// 客户端配置工厂
var clientGenerators = map[ClientType]ClientConfigGenerator{
    ClientClash:       &ClashConfigGenerator{},
    ClientClashMeta:   &ClashConfigGenerator{},
    ClientSingBox:     &SingBoxConfigGenerator{},
    ClientV2Ray:       &V2RayConfigGenerator{},
    ClientShadowrocket: &V2RayConfigGenerator{},
    ClientQuantumultX: &V2RayConfigGenerator{},
    ClientGeneric:     &V2RayConfigGenerator{},
}

func GetClientGenerator(clientType ClientType) (ClientConfigGenerator, error) {
    generator, exists := clientGenerators[clientType]
    if !exists {
        return nil, fmt.Errorf("unsupported client type: %s", clientType)
    }
    return generator, nil
}

// 订阅服务
type SubscriptionService struct {
    nodeService *NodeService
    userService *UserService
}

func (s *SubscriptionService) GenerateSubscription(userUUID string, clientType ClientType) (*SubscriptionResult, error) {
    // 获取用户可用节点
    nodes, err := s.nodeService.GetAvailableNodes(userUUID)
    if err != nil {
        return nil, err
    }
    
    // 检查用户状态
    user, err := s.userService.GetUserByUUID(userUUID)
    if err != nil {
        return nil, err
    }
    
    if user.Status != "active" || (user.ExpiredAt != nil && user.ExpiredAt.Before(time.Now())) {
        // 返回空配置
        return &SubscriptionResult{
            Content:     "",
            ContentType: "text/plain",
            Filename:    "empty.txt",
        }, nil
    }
    
    // 生成订阅配置
    generator, err := GetClientGenerator(clientType)
    if err != nil {
        return nil, err
    }
    
    return generator.GenerateSubscription(nodes, userUUID)
}
```

## Data Models

### 数据库表设计

**用户表 (users)**
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    uuid VARCHAR(36) UNIQUE NOT NULL,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('admin', 'user') DEFAULT 'user',
    status ENUM('active', 'inactive', 'expired') DEFAULT 'active',
    traffic_limit BIGINT DEFAULT 0,
    traffic_used BIGINT DEFAULT 0,
    device_limit INT DEFAULT 1,
    speed_limit INT DEFAULT 0,
    expired_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**节点表 (nodes)**
```sql
CREATE TABLE nodes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    protocol VARCHAR(20) NOT NULL DEFAULT 'anytls',
    host VARCHAR(255) NOT NULL,
    port INT NOT NULL,
    password VARCHAR(255) NOT NULL,
    config JSON,  -- 协议特定配置，当前存储AnyTLS的padding_scheme等
    server_name VARCHAR(255),
    status ENUM('online', 'offline', 'maintenance') DEFAULT 'offline',
    sort_order INT DEFAULT 0,
    traffic_rate DECIMAL(10,2) DEFAULT 1.0,
    max_users INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_protocol (protocol)
);
```

**用户节点关联表 (user_nodes)**
```sql
CREATE TABLE user_nodes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    node_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (node_id) REFERENCES nodes(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_node (user_id, node_id)
);
```

**流量统计表 (traffic_logs)**
```sql
CREATE TABLE traffic_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    node_id INT NOT NULL,
    upload BIGINT DEFAULT 0,
    download BIGINT DEFAULT 0,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (node_id) REFERENCES nodes(id) ON DELETE CASCADE,
    INDEX idx_user_time (user_id, recorded_at),
    INDEX idx_node_time (node_id, recorded_at)
);
```

**在线用户表 (online_users)**
```sql
CREATE TABLE online_users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    node_id INT NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    connected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (node_id) REFERENCES nodes(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_node_ip (user_id, node_id, ip_address)
);
```

**权限组表 (permission_groups)**
```sql
CREATE TABLE permission_groups (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

**权限组节点关联表 (permission_group_nodes)**
```sql
CREATE TABLE permission_group_nodes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    group_id INT NOT NULL,
    node_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (group_id) REFERENCES permission_groups(id) ON DELETE CASCADE,
    FOREIGN KEY (node_id) REFERENCES nodes(id) ON DELETE CASCADE,
    UNIQUE KEY unique_group_node (group_id, node_id)
);
```

**订阅商品表 (products)**
```sql
CREATE TABLE products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    traffic_limit BIGINT NOT NULL COMMENT '流量限制(字节)',
    duration_days INT NOT NULL COMMENT '有效期(天)',
    device_limit INT DEFAULT 1 COMMENT '设备数限制',
    speed_limit INT DEFAULT 0 COMMENT '速度限制(Mbps)',
    permission_group_id INT NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (permission_group_id) REFERENCES permission_groups(id)
);
```

**用户订阅表 (user_subscriptions)**
```sql
CREATE TABLE user_subscriptions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    product_id INT NOT NULL,
    order_id VARCHAR(50) UNIQUE,
    status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expired_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id),
    INDEX idx_user_status (user_id, status),
    INDEX idx_expired_at (expired_at)
);
```

**订单表 (orders)**
```sql
CREATE TABLE orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_id VARCHAR(50) UNIQUE NOT NULL,
    user_id INT NOT NULL,
    product_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'paid', 'cancelled', 'refunded') DEFAULT 'pending',
    payment_method VARCHAR(50),
    payment_id VARCHAR(100),
    paid_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id),
    INDEX idx_user_status (user_id, status),
    INDEX idx_order_id (order_id)
);
```

### Go数据模型

**用户模型:**
```go
type User struct {
    ID           uint      `json:"id" gorm:"primaryKey"`
    UUID         string    `json:"uuid" gorm:"uniqueIndex;size:36"`
    Username     string    `json:"username" gorm:"uniqueIndex;size:50"`
    Email        string    `json:"email" gorm:"uniqueIndex;size:100"`
    PasswordHash string    `json:"-" gorm:"size:255"`
    Role         string    `json:"role" gorm:"type:enum('admin','user');default:'user'"`
    Status       string    `json:"status" gorm:"type:enum('active','inactive','expired');default:'active'"`
    TrafficLimit int64     `json:"traffic_limit" gorm:"default:0"`
    TrafficUsed  int64     `json:"traffic_used" gorm:"default:0"`
    DeviceLimit  int       `json:"device_limit" gorm:"default:1"`
    SpeedLimit   int       `json:"speed_limit" gorm:"default:0"`
    ExpiredAt    *time.Time `json:"expired_at"`
    CreatedAt    time.Time `json:"created_at"`
    UpdatedAt    time.Time `json:"updated_at"`
    
    Nodes        []Node    `json:"nodes" gorm:"many2many:user_nodes;"`
}
```

**节点模型:**
```go
type Node struct {
    ID          uint            `json:"id" gorm:"primaryKey"`
    Name        string          `json:"name" gorm:"size:100"`
    Protocol    string          `json:"protocol" gorm:"size:20;default:'anytls'"`
    Host        string          `json:"host" gorm:"size:255"`
    Port        int             `json:"port"`
    Password    string          `json:"password" gorm:"size:255"`
    Config      datatypes.JSON  `json:"config"` // 协议特定配置
    ServerName  string          `json:"server_name" gorm:"size:255"`
    Status      string          `json:"status" gorm:"type:enum('online','offline','maintenance');default:'offline'"`
    SortOrder   int             `json:"sort_order" gorm:"default:0"`
    TrafficRate float64         `json:"traffic_rate" gorm:"type:decimal(10,2);default:1.0"`
    MaxUsers    int             `json:"max_users" gorm:"default:0"`
    CreatedAt   time.Time       `json:"created_at"`
    UpdatedAt   time.Time       `json:"updated_at"`
    
    Users       []User          `json:"users" gorm:"many2many:user_nodes;"`
}

// AnyTLS协议特定配置
type AnyTLSConfig struct {
    PaddingScheme []string `json:"padding_scheme,omitempty"`
}

// 获取AnyTLS配置的便捷方法
func (n *Node) GetAnyTLSConfig() (*AnyTLSConfig, error) {
    if n.Protocol != "anytls" {
        return nil, fmt.Errorf("node is not anytls protocol")
    }
    
    var config AnyTLSConfig
    if err := json.Unmarshal(n.Config, &config); err != nil {
        return nil, err
    }
    return &config, nil
}

**权限组模型:**
```go
type PermissionGroup struct {
    ID          uint      `json:"id" gorm:"primaryKey"`
    Name        string    `json:"name" gorm:"uniqueIndex;size:100"`
    Description string    `json:"description" gorm:"type:text"`
    SortOrder   int       `json:"sort_order" gorm:"default:0"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
    
    Nodes       []Node    `json:"nodes" gorm:"many2many:permission_group_nodes;"`
    Products    []Product `json:"products" gorm:"foreignKey:PermissionGroupID"`
}
```

**订阅商品模型:**
```go
type Product struct {
    ID                  uint             `json:"id" gorm:"primaryKey"`
    Name                string           `json:"name" gorm:"size:100"`
    Description         string           `json:"description" gorm:"type:text"`
    Price               float64          `json:"price" gorm:"type:decimal(10,2)"`
    TrafficLimit        int64            `json:"traffic_limit"`
    DurationDays        int              `json:"duration_days"`
    DeviceLimit         int              `json:"device_limit" gorm:"default:1"`
    SpeedLimit          int              `json:"speed_limit" gorm:"default:0"`
    PermissionGroupID   uint             `json:"permission_group_id"`
    Status              string           `json:"status" gorm:"type:enum('active','inactive');default:'active'"`
    SortOrder           int              `json:"sort_order" gorm:"default:0"`
    CreatedAt           time.Time        `json:"created_at"`
    UpdatedAt           time.Time        `json:"updated_at"`
    
    PermissionGroup     PermissionGroup  `json:"permission_group" gorm:"foreignKey:PermissionGroupID"`
    UserSubscriptions   []UserSubscription `json:"user_subscriptions" gorm:"foreignKey:ProductID"`
    Orders              []Order          `json:"orders" gorm:"foreignKey:ProductID"`
}
```

**用户订阅模型:**
```go
type UserSubscription struct {
    ID        uint      `json:"id" gorm:"primaryKey"`
    UserID    uint      `json:"user_id"`
    ProductID uint      `json:"product_id"`
    OrderID   string    `json:"order_id" gorm:"size:50"`
    Status    string    `json:"status" gorm:"type:enum('active','expired','cancelled');default:'active'"`
    StartedAt time.Time `json:"started_at"`
    ExpiredAt time.Time `json:"expired_at"`
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
    
    User      User      `json:"user" gorm:"foreignKey:UserID"`
    Product   Product   `json:"product" gorm:"foreignKey:ProductID"`
}
```

**订单模型:**
```go
type Order struct {
    ID            uint      `json:"id" gorm:"primaryKey"`
    OrderID       string    `json:"order_id" gorm:"uniqueIndex;size:50"`
    UserID        uint      `json:"user_id"`
    ProductID     uint      `json:"product_id"`
    Amount        float64   `json:"amount" gorm:"type:decimal(10,2)"`
    Status        string    `json:"status" gorm:"type:enum('pending','paid','cancelled','refunded');default:'pending'"`
    PaymentMethod string    `json:"payment_method" gorm:"size:50"`
    PaymentID     string    `json:"payment_id" gorm:"size:100"`
    PaidAt        *time.Time `json:"paid_at"`
    CreatedAt     time.Time `json:"created_at"`
    UpdatedAt     time.Time `json:"updated_at"`
    
    User          User      `json:"user" gorm:"foreignKey:UserID"`
    Product       Product   `json:"product" gorm:"foreignKey:ProductID"`
}
```

**更新用户模型以支持订阅关联:**
```go
type User struct {
    ID           uint      `json:"id" gorm:"primaryKey"`
    UUID         string    `json:"uuid" gorm:"uniqueIndex;size:36"`
    Username     string    `json:"username" gorm:"uniqueIndex;size:50"`
    Email        string    `json:"email" gorm:"uniqueIndex;size:100"`
    PasswordHash string    `json:"-" gorm:"size:255"`
    Role         string    `json:"role" gorm:"type:enum('admin','user');default:'user'"`
    Status       string    `json:"status" gorm:"type:enum('active','inactive','expired');default:'active'"`
    TrafficLimit int64     `json:"traffic_limit" gorm:"default:0"`
    TrafficUsed  int64     `json:"traffic_used" gorm:"default:0"`
    DeviceLimit  int       `json:"device_limit" gorm:"default:1"`
    SpeedLimit   int       `json:"speed_limit" gorm:"default:0"`
    ExpiredAt    *time.Time `json:"expired_at"`
    CreatedAt    time.Time `json:"created_at"`
    UpdatedAt    time.Time `json:"updated_at"`
    
    // 关联关系
    Nodes             []Node             `json:"nodes" gorm:"many2many:user_nodes;"`
    UserSubscriptions []UserSubscription `json:"user_subscriptions" gorm:"foreignKey:UserID"`
    Orders            []Order            `json:"orders" gorm:"foreignKey:UserID"`
}

// 获取用户当前有效订阅
func (u *User) GetActiveSubscription() (*UserSubscription, error) {
    var subscription UserSubscription
    err := db.Where("user_id = ? AND status = 'active' AND expired_at > ?", 
        u.ID, time.Now()).First(&subscription).Error
    if err != nil {
        return nil, err
    }
    return &subscription, nil
}

// 获取用户可访问的节点（基于权限组）
func (u *User) GetAccessibleNodes() ([]Node, error) {
    subscription, err := u.GetActiveSubscription()
    if err != nil {
        return nil, err
    }
    
    var nodes []Node
    err = db.Joins("JOIN permission_group_nodes ON nodes.id = permission_group_nodes.node_id").
        Joins("JOIN products ON products.permission_group_id = permission_group_nodes.group_id").
        Where("products.id = ? AND nodes.status = 'online'", subscription.ProductID).
        Find(&nodes).Error
    
    return nodes, err
}
```

## Error Handling

### 错误处理策略

**HTTP状态码规范:**
- 200: 请求成功
- 201: 创建成功
- 400: 请求参数错误
- 401: 未授权访问
- 403: 权限不足
- 404: 资源不存在
- 409: 资源冲突
- 429: 请求过于频繁
- 500: 服务器内部错误

**统一错误响应格式:**
```go
type ErrorResponse struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
    Details string `json:"details,omitempty"`
}
```

**错误处理中间件:**
```go
func ErrorHandler() gin.HandlerFunc {
    return func(c *gin.Context) {
        c.Next()
        
        if len(c.Errors) > 0 {
            err := c.Errors.Last()
            
            switch e := err.Err.(type) {
            case *ValidationError:
                c.JSON(400, ErrorResponse{
                    Code:    400,
                    Message: "Validation failed",
                    Details: e.Error(),
                })
            case *AuthError:
                c.JSON(401, ErrorResponse{
                    Code:    401,
                    Message: "Authentication failed",
                })
            default:
                c.JSON(500, ErrorResponse{
                    Code:    500,
                    Message: "Internal server error",
                })
            }
        }
    }
}
```

### 数据库错误处理

**连接池配置:**
```go
func InitDB() *gorm.DB {
    dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
        config.DB.Username,
        config.DB.Password,
        config.DB.Host,
        config.DB.Port,
        config.DB.Database,
    )
    
    db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
        Logger: logger.Default.LogMode(logger.Info),
    })
    if err != nil {
        log.Fatal("Failed to connect to database:", err)
    }
    
    sqlDB, _ := db.DB()
    sqlDB.SetMaxIdleConns(10)
    sqlDB.SetMaxOpenConns(100)
    sqlDB.SetConnMaxLifetime(time.Hour)
    
    return db
}
```

## 扩展性设计

### 协议扩展路径

**当前状态：AnyTLS专用**
- 所有功能针对AnyTLS协议优化
- 用户界面专门为AnyTLS设计
- 配置生成专注于AnyTLS格式

**未来扩展计划：**
1. **第二阶段**：添加Vmess协议支持
   - 扩展协议工厂，添加VmessConfigGenerator
   - 在节点管理界面添加Vmess配置表单
   - 更新订阅生成逻辑支持Vmess格式

2. **第三阶段**：添加Trojan协议支持
   - 继续扩展协议处理系统
   - 添加Trojan特定的配置界面

3. **第四阶段**：支持Shadowsocks等其他协议

### 扩展实现策略

**前端扩展**
```typescript
// 协议配置组件工厂
const ProtocolConfigComponents = {
  anytls: AnyTLSConfigForm,
  // 未来添加
  // vmess: VmessConfigForm,
  // trojan: TrojanConfigForm,
};

// 动态渲染协议配置表单
const NodeConfigForm = ({ protocol }: { protocol: string }) => {
  const ConfigComponent = ProtocolConfigComponents[protocol];
  if (!ConfigComponent) {
    return <div>不支持的协议类型</div>;
  }
  return <ConfigComponent />;
};
```

**后端扩展**
```go
// 协议注册系统
func RegisterProtocol(name string, handler ProtocolHandler) {
    protocolHandlers[name] = handler
}

// 启动时注册所有协议
func init() {
    RegisterProtocol("anytls", &AnyTLSHandler{})
    // 未来可以添加
    // RegisterProtocol("vmess", &VmessHandler{})
    // RegisterProtocol("trojan", &TrojanHandler{})
}
```

**数据库兼容性**
- 使用JSON字段存储协议特定配置，保证向后兼容
- protocol字段支持添加新的协议类型
- 现有AnyTLS数据无需迁移

### 迁移策略

**从单协议到多协议的平滑过渡：**
1. 保持现有AnyTLS功能不变
2. 逐步添加协议选择界面
3. 保证现有用户数据和配置的兼容性
4. 提供协议迁移工具（如需要）

## Testing Strategy

### 测试层级

**单元测试:**
- 所有Service层业务逻辑
- 数据模型验证
- 工具函数测试
- 覆盖率目标: 80%+

**集成测试:**
- API接口测试
- 数据库操作测试
- 第三方服务集成测试

**端到端测试:**
- 用户注册登录流程
- 订阅生成和配置下载
- V2bX API兼容性测试

### 测试工具和框架

**后端测试:**
```go
// 使用testify进行单元测试
func TestUserService_CreateUser(t *testing.T) {
    // 测试用户创建逻辑
    service := NewUserService(mockDB)
    user, err := service.CreateUser(&CreateUserRequest{
        Username: "testuser",
        Email:    "<EMAIL>",
        Password: "password123",
    })
    
    assert.NoError(t, err)
    assert.NotEmpty(t, user.UUID)
    assert.Equal(t, "testuser", user.Username)
}
```

**前端测试:**
```typescript
// 使用Jest和React Testing Library
import { render, screen, fireEvent } from '@testing-library/react';
import UserManagement from './UserManagement';

test('should display user list', async () => {
    render(<UserManagement />);
    
    const userList = await screen.findByTestId('user-list');
    expect(userList).toBeInTheDocument();
});
```

### CI/CD流水线

**GitHub Actions配置:**
```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-go@v3
        with:
          go-version: 1.21
      - name: Run tests
        run: go test -v -cover ./...
      
  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Build Docker image
        run: docker build -t anytls-panel .
```

### 性能测试

**负载测试指标:**
- API响应时间 < 200ms (95th percentile)
- 并发用户数 > 1000
- 数据库连接池利用率 < 80%
- 内存使用率 < 512MB

**监控和告警:**
- Prometheus + Grafana监控
- 关键指标告警
- 日志聚合和分析

## 商品系统组件设计

### 管理员商品管理界面
```typescript
// 商品管理组件，支持创建、编辑、删除商品
const ProductManagement: React.FC = () => {
  // 商品列表表格，显示价格、流量、有效期、权限组等信息
  // 支持商品状态管理和排序设置
  // 集成权限组选择器，关联节点权限
};
```

### 权限组管理界面
```typescript
// 权限组管理组件，管理节点访问权限
const PermissionGroupManagement: React.FC = () => {
  // 权限组列表和节点分配界面
  // 支持拖拽方式分配节点到权限组
  // 显示每个权限组包含的节点数量和用户数量
};
```

### 用户商品购买界面
```typescript
// 商品商店组件，用户购买订阅
const ProductStore: React.FC = () => {
  // 商品卡片展示，包含价格、流量、有效期等信息
  // 购买流程：选择商品 -> 确认订单 -> 支付 -> 激活订阅
  // 支持多种支付方式集成
};
```

### 订阅管理界面
```typescript
// 用户订阅管理组件
const SubscriptionManagement: React.FC = () => {
  // 当前订阅状态展示
  // 订阅历史记录
  // 续费和升级功能
  // 基于权限组的节点访问控制
};
```

## 商品系统业务流程

### 商品购买流程
1. **用户浏览商品** → 显示可购买的订阅套餐
2. **选择商品** → 查看商品详情和权限组包含的节点
3. **创建订单** → 生成订单并锁定商品价格
4. **支付处理** → 集成支付宝、微信支付等支付方式
5. **激活订阅** → 更新用户权限和订阅状态
6. **生成配置** → 基于权限组生成可访问节点的订阅配置

### 权限组管理流程
1. **创建权限组** → 设置权限组名称和描述
2. **分配节点** → 将节点分配到不同权限组
3. **创建商品** → 商品关联特定权限组
4. **用户购买** → 用户获得权限组对应的节点访问权限
5. **订阅生成** → 只包含用户权限组内的节点

### 订阅状态管理
- **active**: 订阅有效，用户可正常使用
- **expired**: 订阅过期，限制用户访问
- **cancelled**: 订阅取消，回收用户权限
package service

import (
	"anypanel/internal/model"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type AnnouncementService struct {
	db *gorm.DB
}

func NewAnnouncementService(db *gorm.DB) *AnnouncementService {
	return &AnnouncementService{
		db: db,
	}
}

// AnnouncementQueryRequest 公告查询请求
type AnnouncementQueryRequest struct {
	Page      int    `form:"page" binding:"min=1"`
	PageSize  int    `form:"page_size" binding:"min=1,max=100"`
	Type      string `form:"type"`
	Priority  string `form:"priority"`
	Status    string `form:"status"`
	IsSticky  *bool  `form:"is_sticky"`
	Search    string `form:"search"`
	SortBy    string `form:"sort_by"`
	SortOrder string `form:"sort_order"`
}

// AnnouncementQueryResponse 公告查询响应
type AnnouncementQueryResponse struct {
	Total         int64              `json:"total"`
	Announcements []*AnnouncementDTO `json:"announcements"`
}

// AnnouncementDTO 公告数据传输对象
type AnnouncementDTO struct {
	ID          uint       `json:"id"`
	Title       string     `json:"title"`
	Content     string     `json:"content"`
	Type        string     `json:"type"`
	Priority    string     `json:"priority"`
	IsSticky    bool       `json:"is_sticky"`
	ShowOnLogin bool       `json:"show_on_login"`
	Status      string     `json:"status"`
	PublishedAt *time.Time `json:"published_at"`
	ExpiresAt   *time.Time `json:"expires_at"`
	ViewCount   int64      `json:"view_count"`
	CreatedBy   uint       `json:"created_by"`
	CreatorName string     `json:"creator_name"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
}

// AnnouncementCreateRequest 公告创建请求
type AnnouncementCreateRequest struct {
	Title       string     `json:"title" binding:"required,max=255"`
	Content     string     `json:"content" binding:"required"`
	Type        string     `json:"type" binding:"oneof=info warning success error"`
	Priority    string     `json:"priority" binding:"oneof=low medium high"`
	IsSticky    bool       `json:"is_sticky"`
	ShowOnLogin bool       `json:"show_on_login"`
	Status      string     `json:"status" binding:"oneof=draft published"`
	ExpiresAt   *time.Time `json:"expires_at"`
}

// AnnouncementUpdateRequest 公告更新请求
type AnnouncementUpdateRequest struct {
	Title       *string    `json:"title" binding:"omitempty,max=255"`
	Content     *string    `json:"content"`
	Type        *string    `json:"type" binding:"omitempty,oneof=info warning success error"`
	Priority    *string    `json:"priority" binding:"omitempty,oneof=low medium high"`
	IsSticky    *bool      `json:"is_sticky"`
	ShowOnLogin *bool      `json:"show_on_login"`
	Status      *string    `json:"status" binding:"omitempty,oneof=draft published archived"`
	ExpiresAt   *time.Time `json:"expires_at"`
}

// GetAnnouncements 获取公告列表
func (s *AnnouncementService) GetAnnouncements(req *AnnouncementQueryRequest) (*AnnouncementQueryResponse, error) {
	query := s.db.Model(&model.Announcement{})

	// 类型筛选
	if req.Type != "" {
		query = query.Where("type = ?", req.Type)
	}

	// 优先级筛选
	if req.Priority != "" {
		query = query.Where("priority = ?", req.Priority)
	}

	// 状态筛选
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 置顶筛选
	if req.IsSticky != nil {
		query = query.Where("is_sticky = ?", *req.IsSticky)
	}

	// 搜索条件
	if req.Search != "" {
		searchPattern := "%" + req.Search + "%"
		query = query.Where("title LIKE ? OR content LIKE ?", searchPattern, searchPattern)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 排序
	sortBy := req.SortBy
	if sortBy == "" {
		sortBy = "is_sticky DESC, created_at"
	}
	sortOrder := req.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}

	if sortBy == "is_sticky DESC, created_at" {
		query = query.Order("is_sticky DESC, created_at DESC")
	} else {
		query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))
	}

	// 分页
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// 预加载创建者信息
	var announcements []model.Announcement
	if err := query.Preload("Creator").Find(&announcements).Error; err != nil {
		return nil, err
	}

	// 转换为DTO
	announcementDTOs := make([]*AnnouncementDTO, len(announcements))
	for i, announcement := range announcements {
		announcementDTOs[i] = s.toAnnouncementDTO(&announcement)
	}

	return &AnnouncementQueryResponse{
		Total:         total,
		Announcements: announcementDTOs,
	}, nil
}

// GetAnnouncementByID 根据ID获取公告
func (s *AnnouncementService) GetAnnouncementByID(id uint) (*AnnouncementDTO, error) {
	var announcement model.Announcement
	if err := s.db.Preload("Creator").First(&announcement, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("公告不存在")
		}
		return nil, err
	}

	// 增加查看次数
	if err := s.db.Model(&announcement).UpdateColumn("view_count", gorm.Expr("view_count + ?", 1)).Error; err != nil {
		// 记录错误但不影响主流程
		// log.Printf("更新公告查看次数失败: %v", err)
	}

	return s.toAnnouncementDTO(&announcement), nil
}

// GetPublicAnnouncements 获取公开的公告（用户端）
func (s *AnnouncementService) GetPublicAnnouncements(limit int) ([]*AnnouncementDTO, error) {
	if limit <= 0 {
		limit = 10
	}

	var announcements []model.Announcement
	now := time.Now()

	query := s.db.Where("status = ? AND (published_at IS NULL OR published_at <= ?) AND (expires_at IS NULL OR expires_at > ?)",
		"published", now, now)

	if err := query.Order("is_sticky DESC, priority DESC, created_at DESC").
		Limit(limit).
		Preload("Creator").
		Find(&announcements).Error; err != nil {
		return nil, err
	}

	announcementDTOs := make([]*AnnouncementDTO, len(announcements))
	for i, announcement := range announcements {
		announcementDTOs[i] = s.toAnnouncementDTO(&announcement)
	}

	return announcementDTOs, nil
}

// GetLoginAnnouncements 获取登录页显示的公告
func (s *AnnouncementService) GetLoginAnnouncements() ([]*AnnouncementDTO, error) {
	var announcements []model.Announcement
	now := time.Now()

	query := s.db.Where("status = ? AND show_on_login = ? AND (published_at IS NULL OR published_at <= ?) AND (expires_at IS NULL OR expires_at > ?)",
		"published", true, now, now)

	if err := query.Order("priority DESC, created_at DESC").
		Limit(5).
		Preload("Creator").
		Find(&announcements).Error; err != nil {
		return nil, err
	}

	announcementDTOs := make([]*AnnouncementDTO, len(announcements))
	for i, announcement := range announcements {
		announcementDTOs[i] = s.toAnnouncementDTO(&announcement)
	}

	return announcementDTOs, nil
}

// CreateAnnouncement 创建公告
func (s *AnnouncementService) CreateAnnouncement(createdBy uint, req *AnnouncementCreateRequest) (*AnnouncementDTO, error) {
	announcement := &model.Announcement{
		Title:       req.Title,
		Content:     req.Content,
		Type:        req.Type,
		Priority:    req.Priority,
		IsSticky:    req.IsSticky,
		ShowOnLogin: req.ShowOnLogin,
		Status:      req.Status,
		ExpiresAt:   req.ExpiresAt,
		CreatedBy:   createdBy,
	}

	// 如果状态为已发布，设置发布时间
	if req.Status == "published" {
		now := time.Now()
		announcement.PublishedAt = &now
	}

	if err := announcement.Validate(); err != nil {
		return nil, err
	}

	if err := s.db.Create(announcement).Error; err != nil {
		return nil, err
	}

	// 重新加载包含创建者信息
	if err := s.db.Preload("Creator").First(announcement, announcement.ID).Error; err != nil {
		return nil, err
	}

	return s.toAnnouncementDTO(announcement), nil
}

// UpdateAnnouncement 更新公告
func (s *AnnouncementService) UpdateAnnouncement(id uint, req *AnnouncementUpdateRequest) (*AnnouncementDTO, error) {
	var announcement model.Announcement
	if err := s.db.First(&announcement, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("公告不存在")
		}
		return nil, err
	}

	// 更新字段
	if req.Title != nil {
		announcement.Title = *req.Title
	}

	if req.Content != nil {
		announcement.Content = *req.Content
	}

	if req.Type != nil {
		announcement.Type = *req.Type
	}

	if req.Priority != nil {
		announcement.Priority = *req.Priority
	}

	if req.IsSticky != nil {
		announcement.IsSticky = *req.IsSticky
	}

	if req.ShowOnLogin != nil {
		announcement.ShowOnLogin = *req.ShowOnLogin
	}

	if req.Status != nil {
		// 如果状态从草稿改为已发布，设置发布时间
		if announcement.Status != "published" && *req.Status == "published" {
			now := time.Now()
			announcement.PublishedAt = &now
		}
		announcement.Status = *req.Status
	}

	if req.ExpiresAt != nil {
		announcement.ExpiresAt = req.ExpiresAt
	}

	if err := announcement.Validate(); err != nil {
		return nil, err
	}

	if err := s.db.Save(&announcement).Error; err != nil {
		return nil, err
	}

	// 重新加载包含创建者信息
	if err := s.db.Preload("Creator").First(&announcement, announcement.ID).Error; err != nil {
		return nil, err
	}

	return s.toAnnouncementDTO(&announcement), nil
}

// DeleteAnnouncement 删除公告
func (s *AnnouncementService) DeleteAnnouncement(id uint) error {
	var announcement model.Announcement
	if err := s.db.First(&announcement, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("公告不存在")
		}
		return err
	}

	return s.db.Delete(&announcement).Error
}

// PublishAnnouncement 发布公告
func (s *AnnouncementService) PublishAnnouncement(id uint) error {
	var announcement model.Announcement
	if err := s.db.First(&announcement, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("公告不存在")
		}
		return err
	}

	now := time.Now()
	return s.db.Model(&announcement).Updates(map[string]interface{}{
		"status":       "published",
		"published_at": &now,
	}).Error
}

// ArchiveAnnouncement 归档公告
func (s *AnnouncementService) ArchiveAnnouncement(id uint) error {
	var announcement model.Announcement
	if err := s.db.First(&announcement, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("公告不存在")
		}
		return err
	}

	return s.db.Model(&announcement).Update("status", "archived").Error
}

// GetAnnouncementStats 获取公告统计信息
func (s *AnnouncementService) GetAnnouncementStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总公告数
	var total int64
	if err := s.db.Model(&model.Announcement{}).Count(&total).Error; err != nil {
		return nil, err
	}
	stats["total"] = total

	// 按状态统计
	var statusStats []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}
	if err := s.db.Model(&model.Announcement{}).
		Select("status, COUNT(*) as count").
		Group("status").
		Scan(&statusStats).Error; err != nil {
		return nil, err
	}
	stats["by_status"] = statusStats

	// 按类型统计
	var typeStats []struct {
		Type  string `json:"type"`
		Count int64  `json:"count"`
	}
	if err := s.db.Model(&model.Announcement{}).
		Select("type, COUNT(*) as count").
		Group("type").
		Scan(&typeStats).Error; err != nil {
		return nil, err
	}
	stats["by_type"] = typeStats

	// 今日新增
	var todayCount int64
	today := time.Now().Format("2006-01-02")
	if err := s.db.Model(&model.Announcement{}).
		Where("DATE(created_at) = ?", today).
		Count(&todayCount).Error; err != nil {
		return nil, err
	}
	stats["today_count"] = todayCount

	return stats, nil
}

// CleanupExpiredAnnouncements 清理过期公告
func (s *AnnouncementService) CleanupExpiredAnnouncements() error {
	now := time.Now()

	// 将过期的公告状态改为归档
	return s.db.Model(&model.Announcement{}).
		Where("expires_at IS NOT NULL AND expires_at <= ? AND status = ?", now, "published").
		Update("status", "archived").Error
}

// 辅助函数
func (s *AnnouncementService) toAnnouncementDTO(announcement *model.Announcement) *AnnouncementDTO {
	dto := &AnnouncementDTO{
		ID:          announcement.ID,
		Title:       announcement.Title,
		Content:     announcement.Content,
		Type:        announcement.Type,
		Priority:    announcement.Priority,
		IsSticky:    announcement.IsSticky,
		ShowOnLogin: announcement.ShowOnLogin,
		Status:      announcement.Status,
		PublishedAt: announcement.PublishedAt,
		ExpiresAt:   announcement.ExpiresAt,
		ViewCount:   announcement.ViewCount,
		CreatedBy:   announcement.CreatedBy,
		CreatedAt:   announcement.CreatedAt,
		UpdatedAt:   announcement.UpdatedAt,
	}

	// 添加创建者信息
	if announcement.Creator.ID != 0 {
		dto.CreatorName = announcement.Creator.Username
	}

	return dto
}

package utils

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"errors"
	"fmt"
	"io"

	"golang.org/x/crypto/bcrypt"
)

// CryptoService 加密服务
type CryptoService struct {
	key []byte
}

// NewCryptoService 创建加密服务
func NewCryptoService(secretKey string) *CryptoService {
	// 使用SHA256生成32字节的密钥
	hash := sha256.Sum256([]byte(secretKey))
	return &CryptoService{
		key: hash[:],
	}
}

// EncryptString 加密字符串
func (c *CryptoService) EncryptString(plaintext string) (string, error) {
	if plaintext == "" {
		return "", nil
	}

	// 创建AES cipher
	block, err := aes.NewCipher(c.key)
	if err != nil {
		return "", err
	}

	// 使用GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	// 生成随机nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}

	// 加密数据
	ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)

	// 返回base64编码的结果
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// DecryptString 解密字符串
func (c *CryptoService) DecryptString(ciphertext string) (string, error) {
	if ciphertext == "" {
		return "", nil
	}

	// base64解码
	data, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}

	// 创建AES cipher
	block, err := aes.NewCipher(c.key)
	if err != nil {
		return "", err
	}

	// 使用GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return "", errors.New("密文太短")
	}

	// 分离nonce和密文
	nonce, cipherBytes := data[:nonceSize], data[nonceSize:]

	// 解密数据
	plaintext, err := gcm.Open(nil, nonce, cipherBytes, nil)
	if err != nil {
		return "", err
	}

	return string(plaintext), nil
}

// HashPassword 使用bcrypt哈希密码
func HashPassword(password string) (string, error) {
	hash, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hash), nil
}

// CheckPassword 验证密码
func CheckPassword(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

// GenerateRandomKey 生成随机密钥
func GenerateRandomKey(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// HashSHA256 使用SHA256哈希
func HashSHA256(data string) string {
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:])
}

// MaskSensitiveData 掩码敏感数据
func MaskSensitiveData(data string, maskChar string, showFirst, showLast int) string {
	if data == "" {
		return ""
	}

	dataLen := len(data)
	if dataLen <= showFirst+showLast {
		// 如果数据长度小于等于显示的字符数，全部掩码
		return maskChar + maskChar + maskChar + maskChar
	}

	masked := data[:showFirst]
	for i := showFirst; i < dataLen-showLast; i++ {
		masked += maskChar
	}
	masked += data[dataLen-showLast:]

	return masked
}

// MaskEmail 掩码邮箱地址
func MaskEmail(email string) string {
	if email == "" {
		return ""
	}

	atIndex := -1
	for i, char := range email {
		if char == '@' {
			atIndex = i
			break
		}
	}

	if atIndex == -1 {
		return MaskSensitiveData(email, "*", 2, 2)
	}

	localPart := email[:atIndex]
	domainPart := email[atIndex:]

	// 掩码本地部分
	var maskedLocal string
	if len(localPart) <= 3 {
		maskedLocal = localPart[:1] + "***"
	} else {
		maskedLocal = MaskSensitiveData(localPart, "*", 2, 1)
	}

	return maskedLocal + domainPart
}

// MaskPhone 掩码手机号
func MaskPhone(phone string) string {
	if phone == "" {
		return ""
	}

	// 假设手机号格式为11位
	if len(phone) == 11 {
		return MaskSensitiveData(phone, "*", 3, 4)
	}

	// 其他长度的电话号码
	return MaskSensitiveData(phone, "*", 2, 2)
}

// MaskCreditCard 掩码信用卡号
func MaskCreditCard(cardNumber string) string {
	if cardNumber == "" {
		return ""
	}

	// 移除空格和其他分隔符
	cleaned := ""
	for _, char := range cardNumber {
		if char >= '0' && char <= '9' {
			cleaned += string(char)
		}
	}

	if len(cleaned) >= 13 {
		return MaskSensitiveData(cleaned, "*", 4, 4)
	}

	return MaskSensitiveData(cleaned, "*", 2, 2)
}

// SanitizeUserInput 清理用户输入，防止XSS
func SanitizeUserInput(input string) string {
	if input == "" {
		return ""
	}

	// 简单的XSS防护，移除常见的危险字符
	dangerous := []string{
		"<script", "</script>", "<iframe", "</iframe>",
		"javascript:", "vbscript:", "onload=", "onerror=",
		"onclick=", "onmouseover=", "onfocus=", "onblur=",
	}

	sanitized := input
	for _, danger := range dangerous {
		for i := 0; i < len(sanitized); i++ {
			if i+len(danger) <= len(sanitized) {
				substr := sanitized[i : i+len(danger)]
				if substr == danger {
					// 替换为安全字符
					sanitized = sanitized[:i] + "[FILTERED]" + sanitized[i+len(danger):]
					i += len("[FILTERED]") - 1
				}
			}
		}
	}

	return sanitized
}

// ValidatePassword 验证密码强度
func ValidatePassword(password string) error {
	if len(password) < 8 {
		return fmt.Errorf("密码长度必须至少8位")
	}

	hasUpper := false
	hasLower := false
	hasDigit := false
	hasSpecial := false

	for _, char := range password {
		switch {
		case char >= 'A' && char <= 'Z':
			hasUpper = true
		case char >= 'a' && char <= 'z':
			hasLower = true
		case char >= '0' && char <= '9':
			hasDigit = true
		case char >= 33 && char <= 126: // ASCII printable characters
			hasSpecial = true
		}
	}

	missing := []string{}
	if !hasUpper {
		missing = append(missing, "大写字母")
	}
	if !hasLower {
		missing = append(missing, "小写字母")
	}
	if !hasDigit {
		missing = append(missing, "数字")
	}
	if !hasSpecial {
		missing = append(missing, "特殊字符")
	}

	if len(missing) > 0 {
		return fmt.Errorf("密码必须包含: %v", missing)
	}

	return nil
}

// SecureCompare 安全比较两个字符串，防止时序攻击
func SecureCompare(a, b string) bool {
	if len(a) != len(b) {
		return false
	}

	var result byte
	for i := 0; i < len(a); i++ {
		result |= a[i] ^ b[i]
	}

	return result == 0
}

// GenerateSecureToken 生成安全令牌
func GenerateSecureToken(length int) (string, error) {
	if length <= 0 {
		length = 32
	}

	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}

	return base64.URLEncoding.EncodeToString(bytes), nil
}
package service

import (
	"crypto"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// AlipayF2FGateway 支付宝当面付网关
type AlipayF2FGateway struct {
	config *AlipayF2FConfig
}

// AlipayF2FConfig 支付宝当面付配置
type AlipayF2FConfig struct {
	AppID       string `json:"app_id"`
	PrivateKey  string `json:"private_key"`
	PublicKey   string `json:"public_key"`
	ProductName string `json:"product_name"`
}

// AlipayF2FRequest 支付宝请求
type AlipayF2FRequest struct {
	AppID      string `json:"app_id"`
	Method     string `json:"method"`
	Format     string `json:"format"`
	Charset    string `json:"charset"`
	SignType   string `json:"sign_type"`
	Sign       string `json:"sign"`
	Timestamp  string `json:"timestamp"`
	Version    string `json:"version"`
	NotifyURL  string `json:"notify_url"`
	BizContent string `json:"biz_content"`
}

// AlipayF2FBizContent 业务内容
type AlipayF2FBizContent struct {
	OutTradeNo  string `json:"out_trade_no"`
	TotalAmount string `json:"total_amount"`
	Subject     string `json:"subject"`
	Body        string `json:"body,omitempty"`
	TimeExpire  string `json:"time_expire,omitempty"`
}

// AlipayF2FResponse 支付宝响应
type AlipayF2FResponse struct {
	Code                            string `json:"code"`
	Msg                             string `json:"msg"`
	SubCode                         string `json:"sub_code"`
	SubMsg                          string `json:"sub_msg"`
	Sign                            string `json:"sign"`
	AlipayF2FTradePrecreateResponse struct {
		Code       string `json:"code"`
		Msg        string `json:"msg"`
		OutTradeNo string `json:"out_trade_no"`
		QrCode     string `json:"qr_code"`
	} `json:"alipay_trade_precreate_response"`
}

// NewAlipayF2FGateway 创建支付宝当面付网关
func NewAlipayF2FGateway(paymentConfig map[string]interface{}) *AlipayF2FGateway {
	config := &AlipayF2FConfig{
		AppID:       getString(paymentConfig, "app_id"),
		PrivateKey:  getString(paymentConfig, "private_key"),
		PublicKey:   getString(paymentConfig, "public_key"),
		ProductName: getString(paymentConfig, "product_name"),
	}

	if config.ProductName == "" {
		config.ProductName = "AnyPanel - 订阅"
	}

	return &AlipayF2FGateway{config: config}
}

// Form 获取支付配置表单
func (g *AlipayF2FGateway) Form() map[string]FormField {
	return map[string]FormField{
		"app_id": {
			Label:       "支付宝APPID",
			Description: "支付宝开放平台应用ID",
			Type:        "input",
			Required:    true,
		},
		"private_key": {
			Label:       "支付宝私钥",
			Description: "支付宝应用私钥（PKCS1格式）",
			Type:        "textarea",
			Required:    true,
		},
		"public_key": {
			Label:       "支付宝公钥",
			Description: "支付宝应用公钥（对应支付宝公钥）",
			Type:        "textarea",
			Required:    true,
		},
		"product_name": {
			Label:       "自定义商品名称",
			Description: "将会体现在支付宝账单中",
			Type:        "input",
			Required:    false,
			Value:       "AnyPanel - 订阅",
		},
	}
}

// Pay 发起支付
func (g *AlipayF2FGateway) Pay(order PaymentOrder) (*PaymentResult, error) {
	if g.config.AppID == "" || g.config.PrivateKey == "" || g.config.PublicKey == "" {
		return nil, errors.New("支付宝配置不完整")
	}

	// 构建业务内容
	bizContent := AlipayF2FBizContent{
		OutTradeNo:  order.OrderID,
		TotalAmount: fmt.Sprintf("%.2f", order.TotalAmount),
		Subject:     g.config.ProductName,
		Body:        order.Body,
	}

	// 设置过期时间（30分钟）
	expireTime := time.Now().Add(30 * time.Minute).Format("2006-01-02 15:04:05")
	bizContent.TimeExpire = expireTime

	bizContentJSON, err := json.Marshal(bizContent)
	if err != nil {
		return nil, err
	}

	// 构建请求参数
	params := map[string]string{
		"app_id":      g.config.AppID,
		"method":      "alipay.trade.precreate",
		"format":      "JSON",
		"charset":     "UTF-8",
		"sign_type":   "RSA2",
		"timestamp":   time.Now().Format("2006-01-02 15:04:05"),
		"version":     "1.0",
		"notify_url":  order.NotifyURL,
		"biz_content": string(bizContentJSON),
	}

	// 生成签名
	sign, err := g.generateSign(params)
	if err != nil {
		return nil, err
	}
	params["sign"] = sign

	// 发送请求
	response, err := g.sendRequest(params)
	if err != nil {
		return nil, err
	}

	// 验证响应签名
	if err := g.verifyResponse(response); err != nil {
		return nil, err
	}

	// 检查响应状态
	if response.Code != "10000" {
		return nil, fmt.Errorf("支付宝支付失败: %s - %s", response.Msg, response.SubMsg)
	}

	return &PaymentResult{
		Type:          0, // 二维码
		Data:          response.AlipayF2FTradePrecreateResponse.QrCode,
		TransactionID: response.AlipayF2FTradePrecreateResponse.OutTradeNo,
	}, nil
}

// Notify 处理支付回调
func (g *AlipayF2FGateway) Notify(params map[string]interface{}) (*PaymentNotifyResult, error) {
	// 验证签名
	if err := g.verifyNotify(params); err != nil {
		return nil, err
	}

	// 检查支付状态
	tradeStatus := getString(params, "trade_status")
	if tradeStatus != "TRADE_SUCCESS" && tradeStatus != "TRADE_FINISHED" {
		return nil, errors.New("支付状态不是成功")
	}

	return &PaymentNotifyResult{
		TradeNo:    getString(params, "out_trade_no"),
		CallbackNo: getString(params, "trade_no"),
		Amount:     getString(params, "total_amount"),
		Status:     "success",
	}, nil
}

// Refund 处理退款
func (g *AlipayF2FGateway) Refund(refundOrder PaymentRefundOrder) (*PaymentRefundResult, error) {
	// TODO: 实现退款逻辑
	return &PaymentRefundResult{
		Success: false,
		Message: "退款功能暂未实现",
	}, nil
}

// 内部方法

// generateSign 生成签名
func (g *AlipayF2FGateway) generateSign(params map[string]string) (string, error) {
	// 构建待签名字符串
	var sortedKeys []string
	for k := range params {
		if k != "sign" && k != "sign_type" {
			sortedKeys = append(sortedKeys, k)
		}
	}

	// 排序
	for i := 0; i < len(sortedKeys)-1; i++ {
		for j := i + 1; j < len(sortedKeys); j++ {
			if sortedKeys[i] > sortedKeys[j] {
				sortedKeys[i], sortedKeys[j] = sortedKeys[j], sortedKeys[i]
			}
		}
	}

	// 构建查询字符串
	var queryBuilder strings.Builder
	for _, k := range sortedKeys {
		if params[k] != "" {
			if queryBuilder.Len() > 0 {
				queryBuilder.WriteString("&")
			}
			queryBuilder.WriteString(k)
			queryBuilder.WriteString("=")
			queryBuilder.WriteString(url.QueryEscape(params[k]))
		}
	}

	queryString := queryBuilder.String()

	// 解析私钥
	privateKey, err := g.parsePrivateKey(g.config.PrivateKey)
	if err != nil {
		return "", err
	}

	// 签名
	hashed := sha256.Sum256([]byte(queryString))
	signature, err := rsa.SignPKCS1v15(nil, privateKey, crypto.SHA256, hashed[:])
	if err != nil {
		return "", err
	}

	return base64.StdEncoding.EncodeToString(signature), nil
}

// verifySign 验证签名
func (g *AlipayF2FGateway) verifySign(params map[string]string, sign string) error {
	// 构建待签名字符串
	var sortedKeys []string
	for k := range params {
		if k != "sign" && k != "sign_type" {
			sortedKeys = append(sortedKeys, k)
		}
	}

	// 排序
	for i := 0; i < len(sortedKeys)-1; i++ {
		for j := i + 1; j < len(sortedKeys); j++ {
			if sortedKeys[i] > sortedKeys[j] {
				sortedKeys[i], sortedKeys[j] = sortedKeys[j], sortedKeys[i]
			}
		}
	}

	// 构建查询字符串
	var queryBuilder strings.Builder
	for _, k := range sortedKeys {
		if params[k] != "" {
			if queryBuilder.Len() > 0 {
				queryBuilder.WriteString("&")
			}
			queryBuilder.WriteString(k)
			queryBuilder.WriteString("=")
			queryBuilder.WriteString(url.QueryEscape(params[k]))
		}
	}

	queryString := queryBuilder.String()

	// 解析公钥
	publicKey, err := g.parsePublicKey(g.config.PublicKey)
	if err != nil {
		return err
	}

	// 解码签名
	signature, err := base64.StdEncoding.DecodeString(sign)
	if err != nil {
		return err
	}

	// 验证签名
	hashed := sha256.Sum256([]byte(queryString))
	return rsa.VerifyPKCS1v15(publicKey, crypto.SHA256, hashed[:], signature)
}

// verifyResponse 验证响应签名
func (g *AlipayF2FGateway) verifyResponse(response *AlipayF2FResponse) error {
	// 将响应转换为map
	responseMap := make(map[string]interface{})
	responseBytes, _ := json.Marshal(response)
	_ = json.Unmarshal(responseBytes, &responseMap)

	// 提取签名
	sign := response.Sign
	if sign == "" {
		return errors.New("响应缺少签名")
	}

	// 删除签名字段
	delete(responseMap, "sign")

	// 转换为字符串map
	stringMap := make(map[string]string)
	for k, v := range responseMap {
		stringMap[k] = fmt.Sprintf("%v", v)
	}

	// 验证签名
	return g.verifySign(stringMap, sign)
}

// verifyNotify 验证回调签名
func (g *AlipayF2FGateway) verifyNotify(params map[string]interface{}) error {
	// 转换为字符串map
	stringMap := make(map[string]string)
	for k, v := range params {
		if k != "sign" && k != "sign_type" {
			stringMap[k] = fmt.Sprintf("%v", v)
		}
	}

	// 获取签名
	sign := getString(params, "sign")
	if sign == "" {
		return errors.New("回调缺少签名")
	}

	// 验证签名
	return g.verifySign(stringMap, sign)
}

// sendRequest 发送请求
func (g *AlipayF2FGateway) sendRequest(params map[string]string) (*AlipayF2FResponse, error) {
	// 构建表单数据
	form := url.Values{}
	for k, v := range params {
		form.Set(k, v)
	}

	// 发送请求
	resp, err := http.PostForm("https://openapi.alipay.com/gateway.do", form)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var response AlipayF2FResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, err
	}

	return &response, nil
}

// parsePrivateKey 解析私钥
func (g *AlipayF2FGateway) parsePrivateKey(privateKey string) (*rsa.PrivateKey, error) {
	// 移除PEM格式的头尾
	privateKey = strings.ReplaceAll(privateKey, "-----BEGIN RSA PRIVATE KEY-----", "")
	privateKey = strings.ReplaceAll(privateKey, "-----END RSA PRIVATE KEY-----", "")
	privateKey = strings.ReplaceAll(privateKey, "\n", "")
	privateKey = strings.ReplaceAll(privateKey, " ", "")

	// Base64解码
	keyBytes, err := base64.StdEncoding.DecodeString(privateKey)
	if err != nil {
		return nil, err
	}

	// 解析私钥
	return x509.ParsePKCS1PrivateKey(keyBytes)
}

// parsePublicKey 解析公钥
func (g *AlipayF2FGateway) parsePublicKey(publicKey string) (*rsa.PublicKey, error) {
	// 移除PEM格式的头尾
	publicKey = strings.ReplaceAll(publicKey, "-----BEGIN PUBLIC KEY-----", "")
	publicKey = strings.ReplaceAll(publicKey, "-----END PUBLIC KEY-----", "")
	publicKey = strings.ReplaceAll(publicKey, "\n", "")
	publicKey = strings.ReplaceAll(publicKey, " ", "")

	// Base64解码
	keyBytes, err := base64.StdEncoding.DecodeString(publicKey)
	if err != nil {
		return nil, err
	}

	// 解析公钥
	pub, err := x509.ParsePKIXPublicKey(keyBytes)
	if err != nil {
		return nil, err
	}

	return pub.(*rsa.PublicKey), nil
}

// getString 从map中获取字符串值
func getString(m map[string]interface{}, key string) string {
	if val, ok := m[key]; ok {
		return fmt.Sprintf("%v", val)
	}
	return ""
}

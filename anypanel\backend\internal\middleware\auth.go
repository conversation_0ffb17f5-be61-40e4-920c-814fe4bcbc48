package middleware

import (
	"anypanel/internal/config"
	"anypanel/internal/model"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"gorm.io/gorm"
)

// JWTClaims JWT声明结构
type JWTClaims struct {
	UserID   uint   `json:"user_id"`
	Username string `json:"username"`
	Role     string `json:"role"`
	jwt.RegisteredClaims
}

// AuthMiddleware JWT认证中间件
func AuthMiddleware(db *gorm.DB, cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
			c.Abort()
			return
		}

		// 提取Bearer token
		tokenString := strings.Replace(authHeader, "Bearer ", "", 1)
		if tokenString == authHeader {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Bearer token required"})
			c.Abort()
			return
		}

		// 解析JWT token
		claims := &JWTClaims{}
		token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
			return []byte(cfg.JWT.Secret), nil
		})

		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
			c.Abort()
			return
		}

		if !token.Valid {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
			c.Abort()
			return
		}

		// 验证用户是否存在且状态正常
		var user model.User
		if err := db.First(&user, claims.UserID).Error; err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
			c.Abort()
			return
		}

		if user.Status != "active" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User account is not active"})
			c.Abort()
			return
		}

		// 检查用户是否过期
		if user.ExpiredAt != nil && user.ExpiredAt.Before(time.Now()) {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User account has expired"})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("role", claims.Role)
		c.Set("user", &user)

		c.Next()
	}
}

// AdminMiddleware 管理员权限中间件
func AdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		role, exists := c.Get("role")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
			c.Abort()
			return
		}

		if role != "admin" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Admin access required"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// GenerateToken 生成JWT token
func GenerateToken(user *model.User, cfg *config.Config) (string, error) {
	claims := &JWTClaims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     user.Role,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Duration(cfg.JWT.ExpireTime) * time.Second)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Issuer:    "anypanel",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(cfg.JWT.Secret))
}

// GenerateRefreshToken 生成刷新token
func GenerateRefreshToken(user *model.User, cfg *config.Config) (string, error) {
	claims := &JWTClaims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     user.Role,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Duration(cfg.JWT.RefreshExpireTime) * time.Second)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Issuer:    "anypanel",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(cfg.JWT.Secret))
}

// ParseToken 解析JWT token
func ParseToken(tokenString string, cfg *config.Config) (*JWTClaims, error) {
	claims := &JWTClaims{}
	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		return []byte(cfg.JWT.Secret), nil
	})

	if err != nil {
		return nil, err
	}

	if !token.Valid {
		return nil, err
	}

	return claims, nil
}

// GetCurrentUser 获取当前用户信息
func GetCurrentUser(c *gin.Context) (*model.User, bool) {
	user, exists := c.Get("user")
	if !exists {
		return nil, false
	}

	return user.(*model.User), true
}

// RequireAuth 需要认证的路由（兼容函数）
func RequireAuth() gin.HandlerFunc {
	return AuthMiddleware(nil, nil)
}

// RequireAdmin 需要管理员权限的路由（兼容函数）
func RequireAdmin() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		AuthMiddleware(nil, nil)(c)
		if !c.IsAborted() {
			AdminMiddleware()(c)
		}
	})
}

// AuthMiddlewareCompat 兼容的认证中间件，使用默认配置
func AuthMiddlewareCompat(db *gorm.DB) gin.HandlerFunc {
	// 创建默认配置，使用和配置文件中相同的密钥
	defaultCfg := &config.Config{
		JWT: config.JWT{
			Secret: "anypanel-dev-secret-key-at-least-32-chars", // 使用配置文件中的密钥
		},
	}
	return AuthMiddleware(db, defaultCfg)
}

// CORSMiddleware 跨域中间件
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.Header("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// RateLimitMiddleware 限流中间件
func RateLimitMiddleware(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取客户端IP
		clientIP := getClientIP(c)

		// 检查IP是否被封禁
		if isIPBanned(db, clientIP) {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "IP地址已被封禁",
				"code":  "IP_BANNED",
				"ip":    clientIP,
			})
			c.Abort()
			return
		}

		// 检查速率限制
		if isRateLimited(db, clientIP, c.Request.RequestURI) {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":       "请求过于频繁，请稍后再试",
				"code":        "RATE_LIMITED",
				"retry_after": 60,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequestIDMiddleware 请求ID中间件
func RequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()
	}
}

// LoggerMiddleware 日志中间件
func LoggerMiddleware() gin.HandlerFunc {
	return gin.Logger()
}

// RecoveryMiddleware 恢复中间件
func RecoveryMiddleware() gin.HandlerFunc {
	return gin.Recovery()
}

// getClientIP 获取客户端真实IP地址
func getClientIP(c *gin.Context) string {
	// 优先检查X-Forwarded-For头
	xForwardedFor := c.GetHeader("X-Forwarded-For")
	if xForwardedFor != "" {
		// X-Forwarded-For可能包含多个IP，取第一个
		ips := strings.Split(xForwardedFor, ",")
		if len(ips) > 0 {
			ip := strings.TrimSpace(ips[0])
			if ip != "" {
				return ip
			}
		}
	}

	// 检查X-Real-IP头
	xRealIP := c.GetHeader("X-Real-IP")
	if xRealIP != "" {
		return xRealIP
	}

	// 最后使用RemoteAddr
	clientIP := c.ClientIP()
	return clientIP
}

// isIPBanned 检查IP是否被封禁
func isIPBanned(db *gorm.DB, ipAddress string) bool {
	var count int64
	err := db.Model(&model.IPBanRecord{}).
		Where("ip_address = ? AND is_active = ? AND (ban_type = 'permanent' OR expires_at > ?)",
			ipAddress, true, time.Now()).
		Count(&count).Error

	if err != nil {
		return false
	}

	return count > 0
}

// isRateLimited 检查是否触发速率限制
func isRateLimited(db *gorm.DB, ipAddress, endpoint string) bool {
	// 基础速率限制配置
	var limits = map[string]struct {
		requests int64
		window   int64 // 秒
	}{
		"default": {requests: 100, window: 60},  // 默认：每分钟100请求
		"login":   {requests: 5, window: 300},   // 登录：每5分钟5次
		"api":     {requests: 1000, window: 60}, // API：每分钟1000请求
	}

	// 确定使用哪个限制规则
	limitKey := "default"
	if strings.Contains(endpoint, "/login") || strings.Contains(endpoint, "/auth") {
		limitKey = "login"
	} else if strings.Contains(endpoint, "/api/") {
		limitKey = "api"
	}

	limit := limits[limitKey]

	// 检查当前时间窗口内的请求数量
	windowStart := time.Now().Add(-time.Duration(limit.window) * time.Second)

	var record model.RateLimitRecord
	err := db.Where("identifier = ? AND limit_type = 'ip' AND endpoint = ? AND window_start > ?",
		ipAddress, endpoint, windowStart).First(&record).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 创建新记录
			newRecord := &model.RateLimitRecord{
				Identifier:   ipAddress,
				LimitType:    "ip",
				Endpoint:     endpoint,
				RequestCount: 1,
				WindowStart:  time.Now(),
				LastRequest:  time.Now(),
				IsBlocked:    false,
				CreatedAt:    time.Now(),
				UpdatedAt:    time.Now(),
			}
			db.Create(newRecord)
			return false
		}
		return false
	}

	// 更新记录
	record.RequestCount++
	record.LastRequest = time.Now()
	record.UpdatedAt = time.Now()

	// 检查是否超限
	if record.RequestCount > limit.requests {
		record.IsBlocked = true
		db.Save(&record)
		return true
	}

	db.Save(&record)
	return false
}

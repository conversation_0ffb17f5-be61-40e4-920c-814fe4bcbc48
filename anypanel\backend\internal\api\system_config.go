package api

import (
	"net/http"
	"strconv"

	"anypanel/internal/middleware"
	"anypanel/internal/model"
	"anypanel/internal/service"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SystemConfigHandler 系统配置处理器
type SystemConfigHandler struct {
	configService *service.SystemConfigService
}

// NewSystemConfigHandler 创建系统配置处理器
func NewSystemConfigHandler(configService *service.SystemConfigService) *SystemConfigHandler {
	return &SystemConfigHandler{
		configService: configService,
	}
}

// GetAllConfigs 获取所有配置
func (h *SystemConfigHandler) GetAllConfigs(c *gin.Context) {
	configs, err := h.configService.GetAllConfigs()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取配置失败",
			"error":   err.Error(),
		})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data":    configs,
	})
}

// GetConfigsByCategory 根据分类获取配置
func (h *SystemConfigHandler) GetConfigsByCategory(c *gin.Context) {
	category := c.Param("category")
	if category == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "分类参数不能为空",
		})
		return
	}

	configs, err := h.configService.GetConfigsByCategory(category)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取配置失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    configs,
	})
}

// GetConfig 获取单个配置
func (h *SystemConfigHandler) GetConfig(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "配置key不能为空",
		})
		return
	}

	config, err := h.configService.GetConfigByKey(key)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"message": "配置不存在",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    config,
	})
}

// CreateConfig 创建配置
func (h *SystemConfigHandler) CreateConfig(c *gin.Context) {
	var config model.SystemConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	if err := h.configService.CreateOrUpdateConfig(&config); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "创建配置失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "配置创建成功",
		"data":    config,
	})
}

// UpdateConfig 更新配置
func (h *SystemConfigHandler) UpdateConfig(c *gin.Context) {
	id := c.Param("id")
	configID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "配置ID格式错误",
		})
		return
	}

	var config model.SystemConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	config.ID = uint(configID)
	if err := h.configService.CreateOrUpdateConfig(&config); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "更新配置失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "配置更新成功",
		"data":    config,
	})
}

// DeleteConfig 删除配置
func (h *SystemConfigHandler) DeleteConfig(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "配置key不能为空",
		})
		return
	}

	if err := h.configService.DeleteConfig(key); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "删除配置失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "配置删除成功",
	})
}

// BatchUpdateConfigs 批量更新配置
func (h *SystemConfigHandler) BatchUpdateConfigs(c *gin.Context) {
	var configs []model.SystemConfig
	if err := c.ShouldBindJSON(&configs); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	for _, config := range configs {
		if err := h.configService.CreateOrUpdateConfig(&config); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "批量更新配置失败",
				"error":   err.Error(),
			})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "配置批量更新成功",
	})
}

// ReloadConfig 重新加载配置（触发热重载）
func (h *SystemConfigHandler) ReloadConfig(c *gin.Context) {
	// 触发系统配置重新初始化
	if err := h.configService.InitDefaultConfigs(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "配置重载失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "配置重载成功",
	})
}

// SetupSystemConfigRoutes 设置系统配置路由
func SetupSystemConfigRoutes(router *gin.RouterGroup, db *gorm.DB) {
	configService := service.NewSystemConfigService(db, "default-encryption-key")
	handler := NewSystemConfigHandler(configService)

	config := router.Group("/config")
	{
		// 需要管理员权限的路由
		config.Use(middleware.AuthMiddlewareCompat(db))
		config.Use(middleware.AdminMiddleware())
		{
			config.GET("", handler.GetAllConfigs)
			config.GET("/category/:category", handler.GetConfigsByCategory)
			config.GET("/:key", handler.GetConfig)
			config.POST("", handler.CreateConfig)
			config.PUT("/:id", handler.UpdateConfig)
			config.DELETE("/:key", handler.DeleteConfig)
			config.POST("/batch", handler.BatchUpdateConfigs)
			config.POST("/reload", handler.ReloadConfig)
		}
	}
}

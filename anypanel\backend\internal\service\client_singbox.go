package service

import (
	"anypanel/internal/model"
	"encoding/json"
	"fmt"
)

// SingBoxConfigGenerator Sing-box配置生成器
type SingBoxConfigGenerator struct{}

// GenerateSubscription 生成Sing-box订阅配置
func (g *SingBoxConfigGenerator) GenerateSubscription(nodes []*model.Node, userUUID string) (*SubscriptionResult, error) {
	var outbounds []map[string]interface{}
	var outboundTags []string

	// 为每个节点生成出站配置
	for _, node := range nodes {
		if node.Protocol == "anytls" {
			outbound, err := g.generateAnyTLSOutbound(node, userUUID)
			if err != nil {
				continue // 跳过配置生成失败的节点
			}
			outbounds = append(outbounds, outbound)
			outboundTags = append(outboundTags, outbound["tag"].(string))
		}
		// 可以在这里添加其他协议的支持
	}

	// 如果没有可用的出站，返回空配置
	if len(outbounds) == 0 {
		return &SubscriptionResult{
			Content:     "",
			ContentType: "text/plain",
			Filename:    "empty.txt",
		}, nil
	}

	// 生成Sing-box配置
	singboxConfig := g.generateSingBoxConfig(outbounds, outboundTags)

	// 转换为JSON格式
	jsonContent, err := json.MarshalIndent(singboxConfig, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("生成JSON配置失败: %v", err)
	}

	return &SubscriptionResult{
		Content:     string(jsonContent),
		ContentType: "application/json",
		Filename:    "sing-box.json",
	}, nil
}

// generateAnyTLSOutbound 生成AnyTLS出站配置
func (g *SingBoxConfigGenerator) generateAnyTLSOutbound(node *model.Node, userUUID string) (map[string]interface{}, error) {
	handler, err := GetProtocolHandler("anytls")
	if err != nil {
		return nil, err
	}

	config, err := handler.GenerateConfig(node, userUUID)
	if err != nil {
		return nil, err
	}

	// 转换为Sing-box格式的出站配置
	outbound := map[string]interface{}{
		"tag":         node.Name,
		"type":        "anytls",
		"server":      config["server"],
		"server_port": config["port"],
		"password":    config["password"],
		"uuid":        config["uuid"],
	}

	// 添加填充策略
	if paddingScheme, ok := config["padding-scheme"].([]string); ok && len(paddingScheme) > 0 {
		outbound["padding_scheme"] = paddingScheme
	}

	// 添加TLS配置
	tlsConfig := map[string]interface{}{
		"enabled": true,
	}
	if sni, ok := config["sni"]; ok && sni != "" {
		tlsConfig["server_name"] = sni
	}
	outbound["tls"] = tlsConfig

	return outbound, nil
}

// generateSingBoxConfig 生成完整的Sing-box配置
func (g *SingBoxConfigGenerator) generateSingBoxConfig(outbounds []map[string]interface{}, outboundTags []string) map[string]interface{} {
	config := map[string]interface{}{
		"log": map[string]interface{}{
			"level":     "info",
			"timestamp": true,
		},
		"inbounds": []map[string]interface{}{
			{
				"type":                       "mixed",
				"listen":                     "127.0.0.1",
				"listen_port":                2080,
				"sniff":                      true,
				"sniff_override_destination": true,
			},
			{
				"type":                       "tun",
				"interface_name":             "utun-anytls",
				"inet4_address":              "**********/30",
				"mtu":                        9000,
				"auto_route":                 true,
				"strict_route":               true,
				"sniff":                      true,
				"sniff_override_destination": true,
			},
		},
		"outbounds": append([]map[string]interface{}{
			{
				"tag":       "proxy",
				"type":      "selector",
				"outbounds": outboundTags,
			},
			{
				"tag":  "direct",
				"type": "direct",
			},
			{
				"tag":  "block",
				"type": "block",
			},
		}, outbounds...),
		"route": map[string]interface{}{
			"geoip": map[string]interface{}{
				"path": "geoip.db",
			},
			"geosite": map[string]interface{}{
				"path": "geosite.db",
			},
			"rules": []map[string]interface{}{
				// 本地网络直连
				{
					"ip_cidr":  []string{"*********/8", "10.0.0.0/8", "**********/12", "***********/16"},
					"outbound": "direct",
				},
				// 中国大陆网站直连
				{
					"geosite":  []string{"cn"},
					"outbound": "direct",
				},
				// 中国大陆IP直连
				{
					"geoip":    []string{"cn"},
					"outbound": "direct",
				},
				// 广告和恶意网站拦截
				{
					"geosite":  []string{"category-ads-all"},
					"outbound": "block",
				},
			},
			"final":                 "proxy",
			"auto_detect_interface": true,
		},
	}

	return config
}

// GetSupportedProtocols 获取支持的协议
func (g *SingBoxConfigGenerator) GetSupportedProtocols() []string {
	return []string{"anytls"}
}

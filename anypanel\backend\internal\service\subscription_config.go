package service

import (
	"anypanel/internal/model"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// ClientType 客户端类型
type ClientType string

const (
	ClientClash        ClientType = "clash"
	ClientClashMeta    ClientType = "clash-meta"
	ClientSingBox      ClientType = "sing-box"
	ClientV2Ray        ClientType = "v2ray"
	ClientShadowrocket ClientType = "shadowrocket"
	ClientQuantumultX  ClientType = "quantumult-x"
	ClientSurge        ClientType = "surge"
	ClientGeneric      ClientType = "generic"
)

// SubscriptionResult 订阅生成结果
type SubscriptionResult struct {
	Content     string `json:"content"`
	ContentType string `json:"content_type"`
	Filename    string `json:"filename"`
}

// ClientConfigGenerator 客户端配置生成器接口
type ClientConfigGenerator interface {
	GenerateSubscription(nodes []*model.Node, userUUID string) (*SubscriptionResult, error)
	GetSupportedProtocols() []string
}

// ProtocolConfigGenerator 协议配置生成器接口
type ProtocolConfigGenerator interface {
	GenerateURI(node *model.Node, userUUID string) (string, error)
	GenerateConfig(node *model.Node, userUUID string) (map[string]interface{}, error)
	ValidateConfig(config interface{}) bool
}

// SubscriptionService 订阅服务
type SubscriptionConfigService struct {
	db                  *gorm.DB
	nodeService         *NodeService
	userService         *UserService
	productService      *ProductService
	subscriptionService *SubscriptionService
}

// NewSubscriptionConfigService 创建订阅配置服务
func NewSubscriptionConfigService(db *gorm.DB, nodeService *NodeService, userService *UserService, productService *ProductService, subscriptionService *SubscriptionService) *SubscriptionConfigService {
	return &SubscriptionConfigService{
		db:                  db,
		nodeService:         nodeService,
		userService:         userService,
		productService:      productService,
		subscriptionService: subscriptionService,
	}
}

// GetAvailableNodes 获取用户可用的节点列表（基于权限组）
func (s *SubscriptionConfigService) GetAvailableNodes(userUUID string) ([]*model.Node, error) {
	// 获取用户信息
	user, err := s.userService.GetUserByUUID(userUUID)
	if err != nil {
		return nil, fmt.Errorf("获取用户信息失败: %v", err)
	}

	// 检查用户状态
	if user.Status != "active" {
		return nil, fmt.Errorf("用户状态异常: %s", user.Status)
	}

	// 检查用户是否过期
	if user.ExpiredAt != nil && user.ExpiredAt.Before(now()) {
		return nil, fmt.Errorf("用户已过期")
	}

	// 获取用户当前活跃订阅
	activeSubscription, err := s.subscriptionService.GetUserActiveSubscription(user.ID)
	if err != nil {
		return nil, fmt.Errorf("获取用户订阅失败: %v", err)
	}

	// 基于权限组获取可用节点
	var nodes []*model.Node
	err = s.db.Joins("JOIN permission_group_nodes ON nodes.id = permission_group_nodes.node_id").
		Joins("JOIN products ON products.permission_group_id = permission_group_nodes.group_id").
		Where("products.id = ? AND nodes.status = ?", activeSubscription.ProductID, "online").
		Order("nodes.sort_order ASC, nodes.id ASC").
		Find(&nodes).Error

	if err != nil {
		return nil, fmt.Errorf("获取节点列表失败: %v", err)
	}

	return nodes, nil
}

// GenerateSubscription 生成订阅配置
func (s *SubscriptionConfigService) GenerateSubscription(userUUID string, clientType ClientType) (*SubscriptionResult, error) {
	// 获取可用节点
	nodes, err := s.GetAvailableNodes(userUUID)
	if err != nil {
		// 如果获取节点失败，返回空配置
		return &SubscriptionResult{
			Content:     "",
			ContentType: "text/plain",
			Filename:    "empty.txt",
		}, nil
	}

	// 检查用户状态
	user, err := s.userService.GetUserByUUID(userUUID)
	if err != nil || user.Status != "active" || (user.ExpiredAt != nil && user.ExpiredAt.Before(now())) {
		// 用户状态异常，返回空配置
		return &SubscriptionResult{
			Content:     "",
			ContentType: "text/plain",
			Filename:    "empty.txt",
		}, nil
	}

	// 获取配置生成器
	generator, err := GetClientGenerator(clientType)
	if err != nil {
		return nil, fmt.Errorf("不支持的客户端类型: %s", clientType)
	}

	// 生成配置
	return generator.GenerateSubscription(nodes, userUUID)
}

// GetClientGenerator 获取客户端配置生成器
func GetClientGenerator(clientType ClientType) (ClientConfigGenerator, error) {
	generator, exists := clientGenerators[clientType]
	if !exists {
		return nil, fmt.Errorf("unsupported client type: %s", clientType)
	}
	return generator, nil
}

// GetProtocolHandler 获取协议处理器
func GetProtocolHandler(protocol string) (ProtocolConfigGenerator, error) {
	handler, exists := protocolHandlers[protocol]
	if !exists {
		return nil, fmt.Errorf("unsupported protocol: %s", protocol)
	}
	return handler, nil
}

// 客户端生成器注册表
var clientGenerators = map[ClientType]ClientConfigGenerator{
	ClientClash:        &ClashConfigGenerator{},
	ClientClashMeta:    &ClashConfigGenerator{},
	ClientSingBox:      &SingBoxConfigGenerator{},
	ClientV2Ray:        &V2RayConfigGenerator{},
	ClientShadowrocket: &V2RayConfigGenerator{},
	ClientQuantumultX:  &V2RayConfigGenerator{},
	ClientSurge:        &V2RayConfigGenerator{},
	ClientGeneric:      &V2RayConfigGenerator{},
}

// 协议处理器注册表
var protocolHandlers = map[string]ProtocolConfigGenerator{
	"anytls": &AnyTLSConfigGenerator{},
}

// 辅助函数
func now() time.Time {
	return time.Now()
}

// getProxyNames 获取代理名称列表
func getProxyNames(proxies []map[string]interface{}) []string {
	var names []string
	for _, proxy := range proxies {
		if name, ok := proxy["name"].(string); ok {
			names = append(names, name)
		}
	}
	return names
}

// GenerateSubscriptionToken 生成订阅token
func (s *SubscriptionConfigService) GenerateSubscriptionToken(userID uint) (string, error) {
	user, err := s.userService.GetUserByID(userID)
	if err != nil {
		return "", err
	}

	// 使用用户UUID + 当前时间戳生成token
	data := fmt.Sprintf("%s_%d", user.UUID, time.Now().Unix())
	hash := md5.Sum([]byte(data))
	_ = hex.EncodeToString(hash[:])

	// 这里可以将token存储到数据库或缓存中
	// 目前直接返回用户UUID作为token
	return user.UUID, nil
}

// ValidateSubscriptionToken 验证订阅token
func (s *SubscriptionConfigService) ValidateSubscriptionToken(token string) (*model.User, error) {
	// 通过UUID查找用户
	user, err := s.userService.GetUserByUUID(token)
	if err != nil {
		return nil, fmt.Errorf("无效的订阅token")
	}

	// 检查用户状态
	if user.Status != "active" {
		return nil, fmt.Errorf("用户状态异常")
	}

	// 检查是否过期
	if user.ExpiredAt != nil && user.ExpiredAt.Before(now()) {
		return nil, fmt.Errorf("用户已过期")
	}

	return user, nil
}

// GenerateSubscriptionWithCache 生成带缓存的订阅配置
func (s *SubscriptionConfigService) GenerateSubscriptionWithCache(userUUID string, clientType ClientType) (*SubscriptionResult, error) {
	// 生成缓存key
	_ = fmt.Sprintf("subscription:%s:%s", userUUID, clientType)

	// 先检查用户状态，如果异常直接返回空配置
	user, err := s.userService.GetUserByUUID(userUUID)
	if err != nil || user.Status != "active" || (user.ExpiredAt != nil && user.ExpiredAt.Before(now())) {
		return &SubscriptionResult{
			Content:     "",
			ContentType: "text/plain",
			Filename:    "empty.txt",
		}, nil
	}

	// TODO: 实现Redis缓存逻辑
	// 目前直接生成配置
	return s.GenerateSubscription(userUUID, clientType)
}

package service

import (
	"anypanel/internal/model"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
)

type HelpService struct {
	db *gorm.DB
}

func NewHelpService(db *gorm.DB) *HelpService {
	return &HelpService{
		db: db,
	}
}

// HelpDocumentQueryRequest 帮助文档查询请求
type HelpDocumentQueryRequest struct {
	Page       int    `form:"page" binding:"min=1"`
	PageSize   int    `form:"page_size" binding:"min=1,max=100"`
	CategoryID *uint  `form:"category_id"`
	Search     string `form:"search"`
	Status     string `form:"status"`
	IsPublic   *bool  `form:"is_public"`
	Tags       string `form:"tags"`
	SortBy     string `form:"sort_by"`
	SortOrder  string `form:"sort_order"`
}

// HelpDocumentQueryResponse 帮助文档查询响应
type HelpDocumentQueryResponse struct {
	Total     int64              `json:"total"`
	Documents []*HelpDocumentDTO `json:"documents"`
}

// HelpDocumentDTO 帮助文档数据传输对象
type HelpDocumentDTO struct {
	ID          uint             `json:"id"`
	CategoryID  uint             `json:"category_id"`
	Title       string           `json:"title"`
	Content     string           `json:"content"`
	Summary     string           `json:"summary"`
	Tags        []string         `json:"tags"`
	SortOrder   int              `json:"sort_order"`
	Status      string           `json:"status"`
	ViewCount   int64            `json:"view_count"`
	IsPublic    bool             `json:"is_public"`
	CreatedBy   uint             `json:"created_by"`
	CreatorName string           `json:"creator_name"`
	CreatedAt   time.Time        `json:"created_at"`
	UpdatedAt   time.Time        `json:"updated_at"`
	Category    *HelpCategoryDTO `json:"category,omitempty"`
}

// HelpCategoryDTO 帮助文档分类数据传输对象
type HelpCategoryDTO struct {
	ID            uint   `json:"id"`
	Name          string `json:"name"`
	Description   string `json:"description"`
	Icon          string `json:"icon"`
	SortOrder     int    `json:"sort_order"`
	DocumentCount int64  `json:"document_count,omitempty"`
}

// HelpDocumentCreateRequest 帮助文档创建请求
type HelpDocumentCreateRequest struct {
	CategoryID uint     `json:"category_id" binding:"required"`
	Title      string   `json:"title" binding:"required,max=255"`
	Content    string   `json:"content" binding:"required"`
	Summary    string   `json:"summary" binding:"max=500"`
	Tags       []string `json:"tags"`
	SortOrder  int      `json:"sort_order"`
	Status     string   `json:"status" binding:"oneof=draft published archived"`
	IsPublic   bool     `json:"is_public"`
}

// HelpDocumentUpdateRequest 帮助文档更新请求
type HelpDocumentUpdateRequest struct {
	CategoryID *uint    `json:"category_id"`
	Title      *string  `json:"title" binding:"omitempty,max=255"`
	Content    *string  `json:"content"`
	Summary    *string  `json:"summary" binding:"omitempty,max=500"`
	Tags       []string `json:"tags"`
	SortOrder  *int     `json:"sort_order"`
	Status     *string  `json:"status" binding:"omitempty,oneof=draft published archived"`
	IsPublic   *bool    `json:"is_public"`
}

// HelpCategoryCreateRequest 帮助分类创建请求
type HelpCategoryCreateRequest struct {
	Name        string `json:"name" binding:"required,max=100"`
	Description string `json:"description"`
	Icon        string `json:"icon" binding:"max=100"`
	SortOrder   int    `json:"sort_order"`
	Status      string `json:"status" binding:"oneof=active inactive"`
}

// GetHelpDocuments 获取帮助文档列表
func (s *HelpService) GetHelpDocuments(req *HelpDocumentQueryRequest) (*HelpDocumentQueryResponse, error) {
	query := s.db.Model(&model.HelpDocument{})

	// 分类筛选
	if req.CategoryID != nil {
		query = query.Where("category_id = ?", *req.CategoryID)
	}

	// 状态筛选
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 公开状态筛选
	if req.IsPublic != nil {
		query = query.Where("is_public = ?", *req.IsPublic)
	}

	// 标签筛选
	if req.Tags != "" {
		tags := strings.Split(req.Tags, ",")
		for _, tag := range tags {
			tag = strings.TrimSpace(tag)
			if tag != "" {
				query = query.Where("tags LIKE ?", "%"+tag+"%")
			}
		}
	}

	// 搜索条件
	if req.Search != "" {
		searchPattern := "%" + req.Search + "%"
		query = query.Where("title LIKE ? OR content LIKE ? OR summary LIKE ?",
			searchPattern, searchPattern, searchPattern)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 排序
	sortBy := req.SortBy
	if sortBy == "" {
		sortBy = "sort_order"
	}
	sortOrder := req.SortOrder
	if sortOrder == "" {
		sortOrder = "asc"
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// 分页
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// 预加载关联信息
	var documents []model.HelpDocument
	if err := query.Preload("Category").Preload("Creator").Find(&documents).Error; err != nil {
		return nil, err
	}

	// 转换为DTO
	documentDTOs := make([]*HelpDocumentDTO, len(documents))
	for i, document := range documents {
		documentDTOs[i] = s.toHelpDocumentDTO(&document)
	}

	return &HelpDocumentQueryResponse{
		Total:     total,
		Documents: documentDTOs,
	}, nil
}

// GetPublicHelpDocuments 获取公开的帮助文档（用户端）
func (s *HelpService) GetPublicHelpDocuments(categoryID *uint, limit int) ([]*HelpDocumentDTO, error) {
	if limit <= 0 {
		limit = 20
	}

	query := s.db.Where("status = ? AND is_public = ?", "published", true)

	if categoryID != nil {
		query = query.Where("category_id = ?", *categoryID)
	}

	var documents []model.HelpDocument
	if err := query.Order("sort_order ASC, created_at DESC").
		Limit(limit).
		Preload("Category").
		Find(&documents).Error; err != nil {
		return nil, err
	}

	documentDTOs := make([]*HelpDocumentDTO, len(documents))
	for i, document := range documents {
		documentDTOs[i] = s.toHelpDocumentDTO(&document)
	}

	return documentDTOs, nil
}

// GetHelpDocumentByID 根据ID获取帮助文档
func (s *HelpService) GetHelpDocumentByID(id uint) (*HelpDocumentDTO, error) {
	var document model.HelpDocument
	if err := s.db.Preload("Category").Preload("Creator").First(&document, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("帮助文档不存在")
		}
		return nil, err
	}

	// 增加查看次数
	if err := s.db.Model(&document).UpdateColumn("view_count", gorm.Expr("view_count + ?", 1)).Error; err != nil {
		// 记录错误但不影响主流程
		// log.Printf("更新帮助文档查看次数失败: %v", err)
	}

	return s.toHelpDocumentDTO(&document), nil
}

// CreateHelpDocument 创建帮助文档
func (s *HelpService) CreateHelpDocument(createdBy uint, req *HelpDocumentCreateRequest) (*HelpDocumentDTO, error) {
	// 检查分类是否存在
	var category model.HelpCategory
	if err := s.db.First(&category, req.CategoryID).Error; err != nil {
		return nil, fmt.Errorf("分类不存在")
	}

	// 处理标签
	tagsStr := ""
	if len(req.Tags) > 0 {
		tagsStr = strings.Join(req.Tags, ",")
	}

	document := &model.HelpDocument{
		CategoryID: req.CategoryID,
		Title:      req.Title,
		Content:    req.Content,
		Summary:    req.Summary,
		Tags:       tagsStr,
		SortOrder:  req.SortOrder,
		Status:     req.Status,
		IsPublic:   req.IsPublic,
		CreatedBy:  createdBy,
	}

	if err := document.Validate(); err != nil {
		return nil, err
	}

	if err := s.db.Create(document).Error; err != nil {
		return nil, err
	}

	// 重新加载包含关联信息
	if err := s.db.Preload("Category").Preload("Creator").First(document, document.ID).Error; err != nil {
		return nil, err
	}

	return s.toHelpDocumentDTO(document), nil
}

// UpdateHelpDocument 更新帮助文档
func (s *HelpService) UpdateHelpDocument(id uint, req *HelpDocumentUpdateRequest) (*HelpDocumentDTO, error) {
	var document model.HelpDocument
	if err := s.db.First(&document, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("帮助文档不存在")
		}
		return nil, err
	}

	// 更新字段
	if req.CategoryID != nil {
		// 检查分类是否存在
		var category model.HelpCategory
		if err := s.db.First(&category, *req.CategoryID).Error; err != nil {
			return nil, fmt.Errorf("分类不存在")
		}
		document.CategoryID = *req.CategoryID
	}

	if req.Title != nil {
		document.Title = *req.Title
	}

	if req.Content != nil {
		document.Content = *req.Content
	}

	if req.Summary != nil {
		document.Summary = *req.Summary
	}

	if req.Tags != nil {
		document.Tags = strings.Join(req.Tags, ",")
	}

	if req.SortOrder != nil {
		document.SortOrder = *req.SortOrder
	}

	if req.Status != nil {
		document.Status = *req.Status
	}

	if req.IsPublic != nil {
		document.IsPublic = *req.IsPublic
	}

	if err := document.Validate(); err != nil {
		return nil, err
	}

	if err := s.db.Save(&document).Error; err != nil {
		return nil, err
	}

	// 重新加载包含关联信息
	if err := s.db.Preload("Category").Preload("Creator").First(&document, document.ID).Error; err != nil {
		return nil, err
	}

	return s.toHelpDocumentDTO(&document), nil
}

// DeleteHelpDocument 删除帮助文档
func (s *HelpService) DeleteHelpDocument(id uint) error {
	var document model.HelpDocument
	if err := s.db.First(&document, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("帮助文档不存在")
		}
		return err
	}

	return s.db.Delete(&document).Error
}

// GetHelpCategories 获取帮助分类列表
func (s *HelpService) GetHelpCategories(includeCount bool) ([]*HelpCategoryDTO, error) {
	var categories []model.HelpCategory
	if err := s.db.Where("status = ?", "active").
		Order("sort_order ASC").
		Find(&categories).Error; err != nil {
		return nil, err
	}

	categoryDTOs := make([]*HelpCategoryDTO, len(categories))
	for i, category := range categories {
		dto := &HelpCategoryDTO{
			ID:          category.ID,
			Name:        category.Name,
			Description: category.Description,
			Icon:        category.Icon,
			SortOrder:   category.SortOrder,
		}

		// 统计文档数量
		if includeCount {
			var count int64
			if err := s.db.Model(&model.HelpDocument{}).
				Where("category_id = ? AND status = ? AND is_public = ?",
					category.ID, "published", true).
				Count(&count).Error; err == nil {
				dto.DocumentCount = count
			}
		}

		categoryDTOs[i] = dto
	}

	return categoryDTOs, nil
}

// CreateHelpCategory 创建帮助分类
func (s *HelpService) CreateHelpCategory(req *HelpCategoryCreateRequest) (*HelpCategoryDTO, error) {
	category := &model.HelpCategory{
		Name:        req.Name,
		Description: req.Description,
		Icon:        req.Icon,
		SortOrder:   req.SortOrder,
		Status:      req.Status,
	}

	if err := category.Validate(); err != nil {
		return nil, err
	}

	if err := s.db.Create(category).Error; err != nil {
		return nil, err
	}

	return &HelpCategoryDTO{
		ID:          category.ID,
		Name:        category.Name,
		Description: category.Description,
		Icon:        category.Icon,
		SortOrder:   category.SortOrder,
	}, nil
}

// SearchHelpDocuments 搜索帮助文档
func (s *HelpService) SearchHelpDocuments(keyword string, limit int) ([]*HelpDocumentDTO, error) {
	if limit <= 0 {
		limit = 10
	}

	searchPattern := "%" + keyword + "%"
	var documents []model.HelpDocument

	if err := s.db.Where("status = ? AND is_public = ? AND (title LIKE ? OR content LIKE ? OR summary LIKE ?)",
		"published", true, searchPattern, searchPattern, searchPattern).
		Order("view_count DESC, created_at DESC").
		Limit(limit).
		Preload("Category").
		Find(&documents).Error; err != nil {
		return nil, err
	}

	documentDTOs := make([]*HelpDocumentDTO, len(documents))
	for i, document := range documents {
		documentDTOs[i] = s.toHelpDocumentDTO(&document)
	}

	return documentDTOs, nil
}

// GetPopularHelpDocuments 获取热门帮助文档
func (s *HelpService) GetPopularHelpDocuments(limit int) ([]*HelpDocumentDTO, error) {
	if limit <= 0 {
		limit = 10
	}

	var documents []model.HelpDocument
	if err := s.db.Where("status = ? AND is_public = ?", "published", true).
		Order("view_count DESC").
		Limit(limit).
		Preload("Category").
		Find(&documents).Error; err != nil {
		return nil, err
	}

	documentDTOs := make([]*HelpDocumentDTO, len(documents))
	for i, document := range documents {
		documentDTOs[i] = s.toHelpDocumentDTO(&document)
	}

	return documentDTOs, nil
}

// GetDocumentsByTag 根据标签获取文档
func (s *HelpService) GetDocumentsByTag(tag string, limit int) ([]*HelpDocumentDTO, error) {
	if limit <= 0 {
		limit = 10
	}

	var documents []model.HelpDocument
	if err := s.db.Where("status = ? AND is_public = ? AND tags LIKE ?",
		"published", true, "%"+tag+"%").
		Order("created_at DESC").
		Limit(limit).
		Preload("Category").
		Find(&documents).Error; err != nil {
		return nil, err
	}

	documentDTOs := make([]*HelpDocumentDTO, len(documents))
	for i, document := range documents {
		documentDTOs[i] = s.toHelpDocumentDTO(&document)
	}

	return documentDTOs, nil
}

// GetHelpStats 获取帮助系统统计信息
func (s *HelpService) GetHelpStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 文档总数
	var totalDocs int64
	if err := s.db.Model(&model.HelpDocument{}).Count(&totalDocs).Error; err != nil {
		return nil, err
	}
	stats["total_documents"] = totalDocs

	// 已发布文档数
	var publishedDocs int64
	if err := s.db.Model(&model.HelpDocument{}).
		Where("status = ?", "published").
		Count(&publishedDocs).Error; err != nil {
		return nil, err
	}
	stats["published_documents"] = publishedDocs

	// 分类总数
	var totalCategories int64
	if err := s.db.Model(&model.HelpCategory{}).
		Where("status = ?", "active").
		Count(&totalCategories).Error; err != nil {
		return nil, err
	}
	stats["total_categories"] = totalCategories

	// 总查看次数
	var totalViews int64
	if err := s.db.Model(&model.HelpDocument{}).
		Select("SUM(view_count) as total_views").
		Scan(&totalViews).Error; err != nil {
		return nil, err
	}
	stats["total_views"] = totalViews

	// 今日新增文档
	var todayDocs int64
	today := time.Now().Format("2006-01-02")
	if err := s.db.Model(&model.HelpDocument{}).
		Where("DATE(created_at) = ?", today).
		Count(&todayDocs).Error; err != nil {
		return nil, err
	}
	stats["today_documents"] = todayDocs

	return stats, nil
}

// 辅助函数
func (s *HelpService) toHelpDocumentDTO(document *model.HelpDocument) *HelpDocumentDTO {
	dto := &HelpDocumentDTO{
		ID:         document.ID,
		CategoryID: document.CategoryID,
		Title:      document.Title,
		Content:    document.Content,
		Summary:    document.Summary,
		SortOrder:  document.SortOrder,
		Status:     document.Status,
		ViewCount:  document.ViewCount,
		IsPublic:   document.IsPublic,
		CreatedBy:  document.CreatedBy,
		CreatedAt:  document.CreatedAt,
		UpdatedAt:  document.UpdatedAt,
	}

	// 处理标签
	if document.Tags != "" {
		dto.Tags = strings.Split(document.Tags, ",")
	} else {
		dto.Tags = []string{}
	}

	// 添加分类信息
	if document.Category.ID != 0 {
		dto.Category = &HelpCategoryDTO{
			ID:          document.Category.ID,
			Name:        document.Category.Name,
			Description: document.Category.Description,
			Icon:        document.Category.Icon,
			SortOrder:   document.Category.SortOrder,
		}
	}

	// 添加创建者信息
	if document.Creator.ID != 0 {
		dto.CreatorName = document.Creator.Username
	}

	return dto
}

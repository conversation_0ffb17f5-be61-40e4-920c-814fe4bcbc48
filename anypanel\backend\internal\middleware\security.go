package middleware

import (
	"anypanel/internal/model"
	"anypanel/internal/utils"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// PaymentSecurityMiddleware 支付安全中间件
func PaymentSecurityMiddleware(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否为支付相关请求
		if strings.Contains(c.Request.RequestURI, "/payment/") {
			// 验证请求频率
			clientIP := getClientIP(c)
			if isPaymentRateLimited(db, clientIP) {
				c.JSON(http.StatusTooManyRequests, gin.H{
					"error": "支付请求过于频繁，请稍后再试",
					"code":  "PAYMENT_RATE_LIMITED",
				})
				c.Abort()
				return
			}

			// 验证请求签名（对于支付回调）
			if strings.Contains(c.Request.RequestURI, "/notify/") {
				if !validatePaymentNotifySignature(c) {
					c.JSON(http.StatusForbidden, gin.H{
						"error": "无效的支付回调签名",
						"code":  "INVALID_SIGNATURE",
					})
					c.Abort()
					return
				}
			}
		}

		c.Next()
	}
}

// DuplicateSubmissionMiddleware 防重复提交中间件
func DuplicateSubmissionMiddleware(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 只对POST/PUT/DELETE请求进行检查
		if c.Request.Method == "GET" || c.Request.Method == "OPTIONS" {
			c.Next()
			return
		}

		// 获取请求标识
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			// 如果没有提供请求ID，跳过检查
			c.Next()
			return
		}

		// 检查是否为重复请求
		if isDuplicateRequest(db, requestID, getClientIP(c)) {
			c.JSON(http.StatusConflict, gin.H{
				"error": "重复的请求，请勿重复提交",
				"code":  "DUPLICATE_REQUEST",
			})
			c.Abort()
			return
		}

		// 记录请求
		recordRequest(db, requestID, getClientIP(c), c.Request.RequestURI)

		c.Next()
	}
}

// XSSProtectionMiddleware XSS防护中间件
func XSSProtectionMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 添加XSS防护响应头
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Content-Security-Policy", "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';")
		
		c.Next()
	}
}

// isPaymentRateLimited 检查支付请求是否被限流
func isPaymentRateLimited(db *gorm.DB, ipAddress string) bool {
	// 支付请求限制：每IP每分钟最多5次支付请求
	windowStart := time.Now().Add(-time.Minute)
	
	var count int64
	err := db.Model(&model.RateLimitRecord{}).
		Where("identifier = ? AND limit_type = 'payment' AND window_start > ?", 
			ipAddress, windowStart).
		Count(&count).Error

	if err != nil {
		return false
	}

	if count >= 5 {
		return true
	}

	// 记录这次请求
	record := &model.RateLimitRecord{
		Identifier:   ipAddress,
		LimitType:    "payment",
		Endpoint:     "/payment/",
		RequestCount: 1,
		WindowStart:  time.Now(),
		LastRequest:  time.Now(),
		IsBlocked:    false,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}
	db.Create(record)

	return false
}

// validatePaymentNotifySignature 验证支付回调签名
func validatePaymentNotifySignature(c *gin.Context) bool {
	// 获取签名
	signature := c.GetHeader("X-Signature")
	timestamp := c.GetHeader("X-Timestamp")
	
	if signature == "" || timestamp == "" {
		return false
	}

	// 检查时间戳是否在有效范围内（5分钟）
	ts, err := strconv.ParseInt(timestamp, 10, 64)
	if err != nil {
		return false
	}

	if time.Now().Unix()-ts > 300 { // 5分钟
		return false
	}

	// TODO: 实现具体的签名验证逻辑
	// 这里应该根据具体的支付接口文档实现签名验证
	
	return true
}

// isDuplicateRequest 检查是否为重复请求
func isDuplicateRequest(db *gorm.DB, requestID, ipAddress string) bool {
	// 在5分钟内检查相同的请求ID
	cutoff := time.Now().Add(-5 * time.Minute)
	
	var count int64
	err := db.Model(&model.RateLimitRecord{}).
		Where("identifier = ? AND limit_type = 'request_id' AND endpoint = ? AND window_start > ?", 
			ipAddress, requestID, cutoff).
		Count(&count).Error

	if err != nil {
		return false
	}

	return count > 0
}

// recordRequest 记录请求
func recordRequest(db *gorm.DB, requestID, ipAddress, endpoint string) {
	record := &model.RateLimitRecord{
		Identifier:   ipAddress,
		LimitType:    "request_id",
		Endpoint:     requestID,
		RequestCount: 1,
		WindowStart:  time.Now(),
		LastRequest:  time.Now(),
		IsBlocked:    false,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}
	db.Create(record)
}

// CSRFTokenMiddleware CSRF令牌中间件
func CSRFTokenMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 对于状态改变的请求，检查CSRF令牌
		if c.Request.Method == "POST" || c.Request.Method == "PUT" || c.Request.Method == "DELETE" {
			csrfToken := c.GetHeader("X-CSRF-Token")
			if csrfToken == "" {
				csrfToken = c.PostForm("_token")
			}

			// 验证CSRF令牌
			if !validateCSRFToken(csrfToken) {
				c.JSON(http.StatusForbidden, gin.H{
					"error": "无效的CSRF令牌",
					"code":  "INVALID_CSRF_TOKEN",
				})
				c.Abort()
				return
			}
		}

		c.Next()
	}
}

// validateCSRFToken 验证CSRF令牌
func validateCSRFToken(token string) bool {
	if token == "" {
		return false
	}

	// TODO: 实现CSRF令牌验证逻辑
	// 这里应该验证令牌的有效性，比如检查令牌是否由服务器生成、是否过期等
	
	return true
}

// APIKeyMiddleware API密钥验证中间件
func APIKeyMiddleware(validAPIKeys []string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取API密钥
		apiKey := c.GetHeader("X-API-Key")
		if apiKey == "" {
			apiKey = c.Query("api_key")
		}

		if apiKey == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "缺少API密钥",
				"code":  "MISSING_API_KEY",
			})
			c.Abort()
			return
		}

		// 验证API密钥
		valid := false
		for _, validKey := range validAPIKeys {
			if utils.SecureCompare(apiKey, validKey) {
				valid = true
				break
			}
		}

		if !valid {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "无效的API密钥",
				"code":  "INVALID_API_KEY",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequestSignatureMiddleware 请求签名验证中间件
func RequestSignatureMiddleware(secretKey string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取签名相关头部
		signature := c.GetHeader("X-Signature")
		timestamp := c.GetHeader("X-Timestamp")
		nonce := c.GetHeader("X-Nonce")

		if signature == "" || timestamp == "" || nonce == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "缺少签名信息",
				"code":  "MISSING_SIGNATURE",
			})
			c.Abort()
			return
		}

		// 检查时间戳
		ts, err := strconv.ParseInt(timestamp, 10, 64)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "无效的时间戳",
				"code":  "INVALID_TIMESTAMP",
			})
			c.Abort()
			return
		}

		// 检查时间戳是否在有效范围内（5分钟）
		if time.Now().Unix()-ts > 300 {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "请求已过期",
				"code":  "REQUEST_EXPIRED",
			})
			c.Abort()
			return
		}

		// 验证签名
		if !validateRequestSignature(c, secretKey, signature, timestamp, nonce) {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "无效的请求签名",
				"code":  "INVALID_SIGNATURE",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// validateRequestSignature 验证请求签名
func validateRequestSignature(c *gin.Context, secretKey, signature, timestamp, nonce string) bool {
	// 构建签名字符串
	method := c.Request.Method
	path := c.Request.URL.Path
	query := c.Request.URL.RawQuery
	
	signatureString := fmt.Sprintf("%s\n%s\n%s\n%s\n%s", method, path, query, timestamp, nonce)
	
	// 计算HMAC-SHA256签名
	h := hmac.New(sha256.New, []byte(secretKey))
	h.Write([]byte(signatureString))
	expectedSignature := hex.EncodeToString(h.Sum(nil))
	
	// 安全比较签名
	return utils.SecureCompare(signature, expectedSignature)
}

// SecurityEventLogger 安全事件记录中间件
func SecurityEventLogger(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 记录可疑活动
		if isSuspiciousRequest(c) {
			logSecurityEvent(db, c, "suspicious_request", "检测到可疑请求", "medium")
		}

		c.Next()

		// 记录失败的请求
		if c.Writer.Status() >= 400 {
			severity := "low"
			if c.Writer.Status() >= 500 {
				severity = "high"
			}
			logSecurityEvent(db, c, "failed_request", 
				fmt.Sprintf("请求失败，状态码: %d", c.Writer.Status()), severity)
		}
	}
}

// isSuspiciousRequest 检查是否为可疑请求
func isSuspiciousRequest(c *gin.Context) bool {
	userAgent := c.GetHeader("User-Agent")
	
	// 检查常见的恶意User-Agent
	suspiciousAgents := []string{
		"sqlmap", "nmap", "nikto", "dirb", "gobuster",
		"masscan", "zap", "burp", "w3af",
	}
	
	userAgentLower := strings.ToLower(userAgent)
	for _, agent := range suspiciousAgents {
		if strings.Contains(userAgentLower, agent) {
			return true
		}
	}

	// 检查可疑的请求路径
	path := strings.ToLower(c.Request.URL.Path)
	suspiciousPaths := []string{
		"admin", "phpmyadmin", "wp-admin", "wp-login",
		"config", "backup", ".env", "database",
	}
	
	for _, suspPath := range suspiciousPaths {
		if strings.Contains(path, suspPath) {
			return true
		}
	}

	return false
}

// logSecurityEvent 记录安全事件
func logSecurityEvent(db *gorm.DB, c *gin.Context, eventType, description, severity string) {
	event := &model.SecurityEvent{
		EventType:   eventType,
		IPAddress:   getClientIP(c),
		UserAgent:   c.GetHeader("User-Agent"),
		Description: description,
		Severity:    severity,
		Source:      "middleware",
		CreatedAt:   time.Now(),
	}

	// 如果是认证用户，记录用户ID
	if userID, exists := c.Get("user_id"); exists {
		if uid, ok := userID.(uint); ok {
			event.UserID = uid
		}
	}

	db.Create(event)
}
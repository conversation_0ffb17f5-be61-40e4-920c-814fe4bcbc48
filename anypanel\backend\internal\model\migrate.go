package model

import (
	"fmt"

	"gorm.io/gorm"
)

// AutoMigrate 自动迁移数据库表
func AutoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		&User{},
		&Node{},
		&PermissionGroup{},
		&Product{},
		&UserSubscription{},
		&Payment{},
		&Order{},
		&PaymentLog{}, // PaymentLog 必须在 Order 之后创建
		&TrafficLog{},
		&OnlineUser{},
		&LoginLog{},
		&FAQCategory{},
		&FAQ{},
		&Ticket{},
		&TicketReply{},
		&Announcement{},
		&UserFeedback{},
		&HelpCategory{},
		&HelpDocument{},
		// 安全相关模型
		&SecurityEvent{},
		&IPBanRecord{},
		&AuditLog{},
		&RateLimitRecord{},
		// 监控相关模型
		&MetricRecord{},
		&AlertRule{},
		&SystemLog{},
		// 系统配置模型
		&SystemConfig{},
	)
}

// CreateIndexes 创建数据库索引
func CreateIndexes(db *gorm.DB) error {
	// 安全创建索引的辅助函数
	createIndexSafe := func(indexName, tableName, columns string, unique bool) {
		// 检查索引是否存在
		var count int64
		checkSQL := "SELECT count(*) FROM information_schema.statistics WHERE table_schema = DATABASE() AND table_name = ? AND index_name = ?"
		db.Raw(checkSQL, tableName, indexName).Scan(&count)
		
		if count == 0 {
			// 索引不存在，创建新索引
			var createSQL string
			if unique {
				createSQL = fmt.Sprintf("CREATE UNIQUE INDEX %s ON %s(%s)", indexName, tableName, columns)
			} else {
				createSQL = fmt.Sprintf("CREATE INDEX %s ON %s(%s)", indexName, tableName, columns)
			}
			if err := db.Exec(createSQL).Error; err != nil {
				// 记录错误但不中断流程
				fmt.Printf("创建索引失败 %s: %v\n", indexName, err)
			}
		}
	}

	// 用户相关索引 (兼容MySQL 5.7+)
	createIndexSafe("idx_users_status", "users", "status", false)
	createIndexSafe("idx_users_expired_at", "users", "expired_at", false)

	// 节点相关索引
	createIndexSafe("idx_nodes_protocol", "nodes", "protocol", false)
	createIndexSafe("idx_nodes_status", "nodes", "status", false)

	// 订单相关索引
	createIndexSafe("idx_orders_user_status", "orders", "user_id, status", false)

	// 订阅相关索引
	createIndexSafe("idx_user_subscriptions_user_status", "user_subscriptions", "user_id, status", false)
	createIndexSafe("idx_user_subscriptions_expired_at", "user_subscriptions", "expired_at", false)

	// 流量统计相关索引
	createIndexSafe("idx_traffic_logs_user_time", "traffic_logs", "user_id, recorded_at", false)
	createIndexSafe("idx_traffic_logs_node_time", "traffic_logs", "node_id, recorded_at", false)

	// 在线用户相关索引
	createIndexSafe("idx_online_users_unique", "online_users", "user_id, node_id, ip_address", true)

	// 支付方式相关索引
	createIndexSafe("idx_payments_method", "payments", "method", false)
	createIndexSafe("idx_payments_enable", "payments", "enable", false)

	// 支付日志相关索引
	createIndexSafe("idx_payment_logs_order_id", "payment_logs", "order_id", false)
	createIndexSafe("idx_payment_logs_method", "payment_logs", "method", false)
	createIndexSafe("idx_payment_logs_status", "payment_logs", "status", false)
	createIndexSafe("idx_payment_logs_created_at", "payment_logs", "created_at", false)

	// 登录日志相关索引
	createIndexSafe("idx_login_logs_user_id", "login_logs", "user_id", false)
	createIndexSafe("idx_login_logs_login_at", "login_logs", "login_at", false)
	createIndexSafe("idx_login_logs_status", "login_logs", "status", false)
	createIndexSafe("idx_login_logs_ip_address", "login_logs", "ip_address", false)

	// FAQ相关索引
	createIndexSafe("idx_faqs_category_id", "faqs", "category_id", false)
	createIndexSafe("idx_faqs_status", "faqs", "status", false)
	createIndexSafe("idx_faq_categories_status", "faq_categories", "status", false)

	// 工单相关索引
	createIndexSafe("idx_tickets_user_id", "tickets", "user_id", false)
	createIndexSafe("idx_tickets_status", "tickets", "status", false)
	createIndexSafe("idx_tickets_priority", "tickets", "priority", false)
	createIndexSafe("idx_tickets_assigned_to", "tickets", "assigned_to", false)
	createIndexSafe("idx_tickets_ticket_no", "tickets", "ticket_no", false)
	createIndexSafe("idx_ticket_replies_ticket_id", "ticket_replies", "ticket_id", false)
	createIndexSafe("idx_ticket_replies_user_id", "ticket_replies", "user_id", false)

	// 公告相关索引
	createIndexSafe("idx_announcements_status", "announcements", "status", false)
	createIndexSafe("idx_announcements_type", "announcements", "type", false)
	createIndexSafe("idx_announcements_priority", "announcements", "priority", false)
	createIndexSafe("idx_announcements_published_at", "announcements", "published_at", false)
	createIndexSafe("idx_announcements_is_sticky", "announcements", "is_sticky", false)

	// 用户反馈相关索引
	createIndexSafe("idx_user_feedbacks_user_id", "user_feedbacks", "user_id", false)
	createIndexSafe("idx_user_feedbacks_type", "user_feedbacks", "type", false)
	createIndexSafe("idx_user_feedbacks_status", "user_feedbacks", "status", false)

	// 帮助文档相关索引
	createIndexSafe("idx_help_documents_category_id", "help_documents", "category_id", false)
	createIndexSafe("idx_help_documents_status", "help_documents", "status", false)
	createIndexSafe("idx_help_documents_is_public", "help_documents", "is_public", false)
	createIndexSafe("idx_help_categories_status", "help_categories", "status", false)

	// 安全事件相关索引
	createIndexSafe("idx_security_events_event_type", "security_events", "event_type", false)
	createIndexSafe("idx_security_events_ip_time", "security_events", "ip_address, created_at", false)
	createIndexSafe("idx_security_events_severity", "security_events", "severity", false)

	// IP封禁记录相关索引
	createIndexSafe("idx_ip_ban_records_active", "ip_ban_records", "is_active", false)
	createIndexSafe("idx_ip_ban_records_expires", "ip_ban_records", "expires_at", false)

	// 审计日志相关索引
	createIndexSafe("idx_audit_logs_user_action", "audit_logs", "user_id, action", false)
	createIndexSafe("idx_audit_logs_resource", "audit_logs", "resource, resource_id", false)
	createIndexSafe("idx_audit_logs_time", "audit_logs", "created_at", false)

	// 速率限制记录相关索引
	createIndexSafe("idx_rate_limit_records_identifier", "rate_limit_records", "identifier, limit_type", false)
	createIndexSafe("idx_rate_limit_records_window", "rate_limit_records", "window_start", false)

	// 监控指标相关索引
	createIndexSafe("idx_metric_records_name_time", "metric_records", "name, timestamp", false)
	createIndexSafe("idx_metric_records_timestamp", "metric_records", "timestamp", false)

	// 告警规则相关索引
	createIndexSafe("idx_alert_rules_active", "alert_rules", "is_active", false)
	createIndexSafe("idx_alert_rules_metric", "alert_rules", "metric_name", false)

	// 系统日志相关索引
	createIndexSafe("idx_system_logs_level_time", "system_logs", "level, created_at", false)
	createIndexSafe("idx_system_logs_component", "system_logs", "component", false)
	createIndexSafe("idx_system_logs_trace_id", "system_logs", "trace_id", false)

	return nil
}

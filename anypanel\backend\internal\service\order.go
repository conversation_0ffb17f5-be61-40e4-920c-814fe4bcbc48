package service

import (
	"anypanel/internal/model"
	"anypanel/internal/types"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type OrderService struct {
	db             *gorm.DB
	productService *ProductService
	userService    *UserService
}

func NewOrderService(db *gorm.DB, productService *ProductService, userService *UserService) *OrderService {
	return &OrderService{
		db:             db,
		productService: productService,
		userService:    userService,
	}
}

// OrderQueryRequest 订单查询请求
type OrderQueryRequest struct {
	Page          int    `form:"page" binding:"min=1"`
	PageSize      int    `form:"page_size" binding:"min=1,max=100"`
	Search        string `form:"search"`
	UserID        uint   `form:"user_id"`
	ProductID     uint   `form:"product_id"`
	Status        string `form:"status"`
	PaymentMethod string `form:"payment_method"`
	SortBy        string `form:"sort_by"`
	SortOrder     string `form:"sort_order"`
}

// OrderQueryResponse 订单查询响应
type OrderQueryResponse struct {
	Total  int64       `json:"total"`
	Orders []*OrderDTO `json:"orders"`
}

// OrderCreateRequest 订单创建请求
type OrderCreateRequest struct {
	UserID    uint    `json:"user_id" binding:"required"`
	ProductID uint    `json:"product_id" binding:"required"`
	Amount    float64 `json:"amount" binding:"required,min=0"`
}

// OrderUpdateRequest 订单更新请求
type OrderUpdateRequest struct {
	Status        *string `json:"status"`
	PaymentMethod *string `json:"payment_method"`
	PaymentID     *string `json:"payment_id"`
}

// OrderDTO 订单数据传输对象
type OrderDTO struct {
	ID            uint             `json:"id"`
	OrderID       string           `json:"order_id"`
	UserID        uint             `json:"user_id"`
	User          types.UserDTO    `json:"user"`
	ProductID     uint             `json:"product_id"`
	Product       types.ProductDTO `json:"product"`
	Amount        float64          `json:"amount"`
	Status        string           `json:"status"`
	PaymentMethod string           `json:"payment_method"`
	PaymentID     string           `json:"payment_id"`
	PaidAt        *string          `json:"paid_at"`
	CreatedAt     string           `json:"created_at"`
	UpdatedAt     string           `json:"updated_at"`
}

// GetOrderByID 根据ID获取订单
func (s *OrderService) GetOrderByID(orderID uint) (*model.Order, error) {
	var order model.Order
	if err := s.db.Preload("User").Preload("Product").Preload("Product.PermissionGroup").First(&order, orderID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("订单不存在")
		}
		return nil, err
	}
	return &order, nil
}

// GetOrderByOrderID 根据订单ID获取订单
func (s *OrderService) GetOrderByOrderID(orderID string) (*model.Order, error) {
	var order model.Order
	if err := s.db.Preload("User").Preload("Product").Preload("Product.PermissionGroup").Where("order_id = ?", orderID).First(&order).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("订单不存在")
		}
		return nil, err
	}
	return &order, nil
}

// QueryOrders 查询订单列表
func (s *OrderService) QueryOrders(req *OrderQueryRequest) (*OrderQueryResponse, error) {
	query := s.db.Model(&model.Order{})

	// 搜索条件
	if req.Search != "" {
		searchPattern := "%" + req.Search + "%"
		query = query.Where("order_id LIKE ? OR payment_id LIKE ?", searchPattern, searchPattern)
	}

	// 用户筛选
	if req.UserID > 0 {
		query = query.Where("user_id = ?", req.UserID)
	}

	// 商品筛选
	if req.ProductID > 0 {
		query = query.Where("product_id = ?", req.ProductID)
	}

	// 状态筛选
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 支付方式筛选
	if req.PaymentMethod != "" {
		query = query.Where("payment_method = ?", req.PaymentMethod)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 排序
	sortBy := req.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}
	sortOrder := req.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// 分页
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// 查询订单
	var orders []model.Order
	if err := query.Preload("User").Preload("Product").Preload("Product.PermissionGroup").Find(&orders).Error; err != nil {
		return nil, err
	}

	// 转换为DTO
	orderDTOs := make([]*OrderDTO, len(orders))
	for i, order := range orders {
		orderDTOs[i] = s.toOrderDTO(&order)
	}

	return &OrderQueryResponse{
		Total:  total,
		Orders: orderDTOs,
	}, nil
}

// CreateOrder 创建订单
func (s *OrderService) CreateOrder(req *OrderCreateRequest) (*OrderDTO, error) {
	// 验证用户是否存在
	_, err := s.userService.GetUserByID(req.UserID)
	if err != nil {
		return nil, err
	}

	// 验证商品是否存在
	product, err := s.productService.GetProductByID(req.ProductID)
	if err != nil {
		return nil, err
	}

	// 验证商品状态
	if product.Status != "active" {
		return nil, errors.New("商品已下架")
	}

	// 生成订单ID
	orderID := uuid.New().String()

	// 创建订单
	order := &model.Order{
		OrderID:   orderID,
		UserID:    req.UserID,
		ProductID: req.ProductID,
		Amount:    req.Amount,
		Status:    "pending",
	}

	if err := order.Validate(); err != nil {
		return nil, err
	}

	if err := s.db.Create(order).Error; err != nil {
		return nil, err
	}

	// 重新加载以包含关联数据
	if err := s.db.Preload("User").Preload("Product").Preload("Product.PermissionGroup").First(order, order.ID).Error; err != nil {
		return nil, err
	}

	return s.toOrderDTO(order), nil
}

// UpdateOrder 更新订单
func (s *OrderService) UpdateOrder(orderID uint, req *OrderUpdateRequest) (*OrderDTO, error) {
	order, err := s.GetOrderByID(orderID)
	if err != nil {
		return nil, err
	}

	// 更新字段
	if req.Status != nil {
		// 如果状态改为已支付，记录支付时间
		if *req.Status == "paid" && order.Status != "paid" {
			now := time.Now()
			order.PaidAt = &now
		}
		order.Status = *req.Status
	}

	if req.PaymentMethod != nil {
		order.PaymentMethod = *req.PaymentMethod
	}

	if req.PaymentID != nil {
		order.PaymentID = *req.PaymentID
	}

	if err := order.Validate(); err != nil {
		return nil, err
	}

	if err := s.db.Save(order).Error; err != nil {
		return nil, err
	}

	// 重新加载以包含关联数据
	if err := s.db.Preload("User").Preload("Product").Preload("Product.PermissionGroup").First(order, order.ID).Error; err != nil {
		return nil, err
	}

	return s.toOrderDTO(order), nil
}

// DeleteOrder 删除订单
func (s *OrderService) DeleteOrder(orderID uint) error {
	order, err := s.GetOrderByID(orderID)
	if err != nil {
		return err
	}

	// 只能删除未支付或已取消的订单
	if order.Status == "paid" {
		return errors.New("已支付的订单无法删除")
	}

	if err := s.db.Delete(order).Error; err != nil {
		return err
	}

	return nil
}

// UpdateOrderStatus 更新订单状态
func (s *OrderService) UpdateOrderStatus(orderID uint, status string) error {
	order, err := s.GetOrderByID(orderID)
	if err != nil {
		return err
	}

	// 如果状态改为已支付，记录支付时间
	if status == "paid" && order.Status != "paid" {
		now := time.Now()
		order.PaidAt = &now
	}
	order.Status = status

	if err := order.Validate(); err != nil {
		return err
	}

	return s.db.Save(order).Error
}

// ProcessPayment 处理支付
func (s *OrderService) ProcessPayment(orderID string, paymentMethod string, paymentID string) (*OrderDTO, error) {
	order, err := s.GetOrderByOrderID(orderID)
	if err != nil {
		return nil, err
	}

	// 检查订单状态
	if order.Status != "pending" {
		return nil, errors.New("订单状态不正确")
	}

	// 更新订单状态
	now := time.Now()
	order.Status = "paid"
	order.PaymentMethod = paymentMethod
	order.PaymentID = paymentID
	order.PaidAt = &now

	if err := s.db.Save(order).Error; err != nil {
		return nil, err
	}

	// 重新加载以包含关联数据
	if err := s.db.Preload("User").Preload("Product").Preload("Product.PermissionGroup").First(order, order.ID).Error; err != nil {
		return nil, err
	}

	return s.toOrderDTO(order), nil
}

// GetOrdersByUser 根据用户获取订单
func (s *OrderService) GetOrdersByUser(userID uint) ([]*model.Order, error) {
	var orders []model.Order
	if err := s.db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Find(&orders).Error; err != nil {
		return nil, err
	}

	orderPtrs := make([]*model.Order, len(orders))
	for i := range orders {
		orderPtrs[i] = &orders[i]
	}

	return orderPtrs, nil
}

// GetOrderStats 获取订单统计信息
func (s *OrderService) GetOrderStats() (map[string]interface{}, error) {
	var totalOrders int64
	var pendingOrders int64
	var paidOrders int64
	var cancelledOrders int64
	var refundedOrders int64
	var totalRevenue float64

	// 获取订单总数
	if err := s.db.Model(&model.Order{}).Count(&totalOrders).Error; err != nil {
		return nil, err
	}

	// 获取待支付订单数
	if err := s.db.Model(&model.Order{}).Where("status = ?", "pending").Count(&pendingOrders).Error; err != nil {
		return nil, err
	}

	// 获取已支付订单数
	if err := s.db.Model(&model.Order{}).Where("status = ?", "paid").Count(&paidOrders).Error; err != nil {
		return nil, err
	}

	// 获取已取消订单数
	if err := s.db.Model(&model.Order{}).Where("status = ?", "cancelled").Count(&cancelledOrders).Error; err != nil {
		return nil, err
	}

	// 获取已退款订单数
	if err := s.db.Model(&model.Order{}).Where("status = ?", "refunded").Count(&refundedOrders).Error; err != nil {
		return nil, err
	}

	// 获取总收入
	if err := s.db.Model(&model.Order{}).Where("status = ?", "paid").Select("COALESCE(SUM(amount), 0)").Scan(&totalRevenue).Error; err != nil {
		return nil, err
	}

	stats := map[string]interface{}{
		"total_orders":     totalOrders,
		"pending_orders":   pendingOrders,
		"paid_orders":      paidOrders,
		"cancelled_orders": cancelledOrders,
		"refunded_orders":  refundedOrders,
		"total_revenue":    totalRevenue,
	}

	return stats, nil
}

// GetTodayRevenueStats 获取今日收入统计（参考成熟项目做法）
func (s *OrderService) GetTodayRevenueStats() (float64, error) {
	var todayRevenue float64
	if err := s.db.Model(&model.Order{}).
		Where("status = ? AND DATE(created_at) = CURDATE()", "paid").
		Select("COALESCE(SUM(amount), 0)").Scan(&todayRevenue).Error; err != nil {
		return 0, err
	}
	return todayRevenue, nil
}

// GetMonthlyRevenueStats 获取本月收入统计（参考成熟项目做法）
func (s *OrderService) GetMonthlyRevenueStats() (float64, error) {
	var monthlyRevenue float64
	if err := s.db.Model(&model.Order{}).
		Where("status = ? AND MONTH(created_at) = MONTH(NOW()) AND YEAR(created_at) = YEAR(NOW())", "paid").
		Select("COALESCE(SUM(amount), 0)").Scan(&monthlyRevenue).Error; err != nil {
		return 0, err
	}
	return monthlyRevenue, nil
}

// GetLastMonthRevenueStats 获取上月收入统计（用于计算增长率）
func (s *OrderService) GetLastMonthRevenueStats() (float64, error) {
	var lastMonthRevenue float64
	if err := s.db.Model(&model.Order{}).
		Where("status = ? AND MONTH(created_at) = MONTH(DATE_SUB(NOW(), INTERVAL 1 MONTH)) AND YEAR(created_at) = YEAR(DATE_SUB(NOW(), INTERVAL 1 MONTH))", "paid").
		Select("COALESCE(SUM(amount), 0)").Scan(&lastMonthRevenue).Error; err != nil {
		return 0, err
	}
	return lastMonthRevenue, nil
}

// GetTotalRevenueStats 获取总收入统计（所有已支付订单）
func (s *OrderService) GetTotalRevenueStats() (float64, error) {
	var totalRevenue float64
	if err := s.db.Model(&model.Order{}).
		Where("status = ?", "paid").
		Select("COALESCE(SUM(amount), 0)").Scan(&totalRevenue).Error; err != nil {
		return 0, err
	}
	return totalRevenue, nil
}

// GetUserOrderStats 获取用户订单统计
func (s *OrderService) GetUserOrderStats(userID uint) (map[string]interface{}, error) {
	var totalOrders int64
	var paidOrders int64
	var totalAmount float64

	// 获取用户订单总数
	if err := s.db.Model(&model.Order{}).Where("user_id = ?", userID).Count(&totalOrders).Error; err != nil {
		return nil, err
	}

	// 获取用户已支付订单数
	if err := s.db.Model(&model.Order{}).Where("user_id = ? AND status = ?", userID, "paid").Count(&paidOrders).Error; err != nil {
		return nil, err
	}

	// 获取用户总消费金额
	if err := s.db.Model(&model.Order{}).Where("user_id = ? AND status = ?", userID, "paid").Select("COALESCE(SUM(amount), 0)").Scan(&totalAmount).Error; err != nil {
		return nil, err
	}

	stats := map[string]interface{}{
		"total_orders": totalOrders,
		"paid_orders":  paidOrders,
		"total_amount": totalAmount,
	}

	return stats, nil
}

// 辅助函数
func (s *OrderService) toOrderDTO(order *model.Order) *OrderDTO {
	paidAt := ""
	if order.PaidAt != nil {
		paidAt = order.PaidAt.Format("2006-01-02 15:04:05")
	}

	return &OrderDTO{
		ID:      order.ID,
		OrderID: order.OrderID,
		UserID:  order.UserID,
		User: types.UserDTO{
			ID:           order.User.ID,
			Username:     order.User.Username,
			Email:        order.User.Email,
			Role:         order.User.Role,
			Status:       order.User.Status,
			TrafficLimit: order.User.TrafficLimit,
			TrafficUsed:  order.User.TrafficUsed,
			DeviceLimit:  order.User.DeviceLimit,
			ExpiredAt: func() string {
				if order.User.ExpiredAt != nil {
					return order.User.ExpiredAt.Format("2006-01-02 15:04:05")
				}
				return ""
			}(),
			CreatedAt: order.User.CreatedAt.Format("2006-01-02 15:04:05"),
		},
		ProductID: order.ProductID,
		Product: types.ProductDTO{
			ID:           order.Product.ID,
			Name:         order.Product.Name,
			Description:  order.Product.Description,
			Price:        order.Product.Price,
			TrafficLimit: order.Product.TrafficLimit,
			DurationDays: order.Product.DurationDays,
			DeviceLimit:  order.Product.DeviceLimit,
			SpeedLimit:   order.Product.SpeedLimit,
			GroupID:      order.Product.PermissionGroupID,
			GroupName:    order.Product.PermissionGroup.Name,
			Status:       order.Product.Status,
			SortOrder:    order.Product.SortOrder,
			CreatedAt:    order.Product.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:    order.Product.UpdatedAt.Format("2006-01-02 15:04:05"),
		},
		Amount:        order.Amount,
		Status:        order.Status,
		PaymentMethod: order.PaymentMethod,
		PaymentID:     order.PaymentID,
		PaidAt:        &paidAt,
		CreatedAt:     order.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:     order.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
}

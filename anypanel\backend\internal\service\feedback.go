package service

import (
	"anypanel/internal/model"
	"fmt"
	"time"

	"gorm.io/gorm"
)

type FeedbackService struct {
	db *gorm.DB
}

func NewFeedbackService(db *gorm.DB) *FeedbackService {
	return &FeedbackService{
		db: db,
	}
}

// FeedbackQueryRequest 反馈查询请求
type FeedbackQueryRequest struct {
	Page      int    `form:"page" binding:"min=1"`
	PageSize  int    `form:"page_size" binding:"min=1,max=100"`
	UserID    *uint  `form:"user_id"`
	Type      string `form:"type"`
	Status    string `form:"status"`
	Search    string `form:"search"`
	Rating    *int   `form:"rating"`
	SortBy    string `form:"sort_by"`
	SortOrder string `form:"sort_order"`
}

// FeedbackQueryResponse 反馈查询响应
type FeedbackQueryResponse struct {
	Total     int64          `json:"total"`
	Feedbacks []*FeedbackDTO `json:"feedbacks"`
}

// FeedbackDTO 反馈数据传输对象
type FeedbackDTO struct {
	ID          uint      `json:"id"`
	UserID      uint      `json:"user_id"`
	Username    string    `json:"username"`
	Type        string    `json:"type"`
	Subject     string    `json:"subject"`
	Content     string    `json:"content"`
	Rating      *int      `json:"rating"`
	ContactInfo string    `json:"contact_info"`
	Status      string    `json:"status"`
	AdminNotes  string    `json:"admin_notes"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// FeedbackCreateRequest 反馈创建请求
type FeedbackCreateRequest struct {
	Type        string `json:"type" binding:"oneof=bug feature suggestion complaint praise"`
	Subject     string `json:"subject" binding:"required,max=255"`
	Content     string `json:"content" binding:"required"`
	Rating      *int   `json:"rating" binding:"omitempty,min=1,max=5"`
	ContactInfo string `json:"contact_info" binding:"max=255"`
}

// FeedbackUpdateRequest 反馈更新请求（管理员用）
type FeedbackUpdateRequest struct {
	Status     *string `json:"status" binding:"omitempty,oneof=pending reviewed resolved closed"`
	AdminNotes *string `json:"admin_notes"`
}

// GetFeedbacks 获取反馈列表
func (s *FeedbackService) GetFeedbacks(req *FeedbackQueryRequest) (*FeedbackQueryResponse, error) {
	query := s.db.Model(&model.UserFeedback{})

	// 用户筛选
	if req.UserID != nil {
		query = query.Where("user_id = ?", *req.UserID)
	}

	// 类型筛选
	if req.Type != "" {
		query = query.Where("type = ?", req.Type)
	}

	// 状态筛选
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 评分筛选
	if req.Rating != nil {
		query = query.Where("rating = ?", *req.Rating)
	}

	// 搜索条件
	if req.Search != "" {
		searchPattern := "%" + req.Search + "%"
		query = query.Where("subject LIKE ? OR content LIKE ?", searchPattern, searchPattern)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 排序
	sortBy := req.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}
	sortOrder := req.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// 分页
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// 预加载用户信息
	var feedbacks []model.UserFeedback
	if err := query.Preload("User").Find(&feedbacks).Error; err != nil {
		return nil, err
	}

	// 转换为DTO
	feedbackDTOs := make([]*FeedbackDTO, len(feedbacks))
	for i, feedback := range feedbacks {
		feedbackDTOs[i] = s.toFeedbackDTO(&feedback)
	}

	return &FeedbackQueryResponse{
		Total:     total,
		Feedbacks: feedbackDTOs,
	}, nil
}

// GetFeedbackByID 根据ID获取反馈
func (s *FeedbackService) GetFeedbackByID(id uint) (*FeedbackDTO, error) {
	var feedback model.UserFeedback
	if err := s.db.Preload("User").First(&feedback, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("反馈不存在")
		}
		return nil, err
	}

	return s.toFeedbackDTO(&feedback), nil
}

// GetUserFeedbacks 获取用户的反馈列表
func (s *FeedbackService) GetUserFeedbacks(userID uint, req *FeedbackQueryRequest) (*FeedbackQueryResponse, error) {
	req.UserID = &userID
	return s.GetFeedbacks(req)
}

// CreateFeedback 创建反馈
func (s *FeedbackService) CreateFeedback(userID uint, req *FeedbackCreateRequest) (*FeedbackDTO, error) {
	feedback := &model.UserFeedback{
		UserID:      userID,
		Type:        req.Type,
		Subject:     req.Subject,
		Content:     req.Content,
		Rating:      req.Rating,
		ContactInfo: req.ContactInfo,
		Status:      "pending",
	}

	if err := feedback.Validate(); err != nil {
		return nil, err
	}

	if err := s.db.Create(feedback).Error; err != nil {
		return nil, err
	}

	// 重新加载包含用户信息
	if err := s.db.Preload("User").First(feedback, feedback.ID).Error; err != nil {
		return nil, err
	}

	return s.toFeedbackDTO(feedback), nil
}

// UpdateFeedback 更新反馈（管理员用）
func (s *FeedbackService) UpdateFeedback(id uint, req *FeedbackUpdateRequest) (*FeedbackDTO, error) {
	var feedback model.UserFeedback
	if err := s.db.First(&feedback, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("反馈不存在")
		}
		return nil, err
	}

	// 更新字段
	if req.Status != nil {
		feedback.Status = *req.Status
	}

	if req.AdminNotes != nil {
		feedback.AdminNotes = *req.AdminNotes
	}

	if err := feedback.Validate(); err != nil {
		return nil, err
	}

	if err := s.db.Save(&feedback).Error; err != nil {
		return nil, err
	}

	// 重新加载包含用户信息
	if err := s.db.Preload("User").First(&feedback, feedback.ID).Error; err != nil {
		return nil, err
	}

	return s.toFeedbackDTO(&feedback), nil
}

// DeleteFeedback 删除反馈
func (s *FeedbackService) DeleteFeedback(id uint) error {
	var feedback model.UserFeedback
	if err := s.db.First(&feedback, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("反馈不存在")
		}
		return err
	}

	return s.db.Delete(&feedback).Error
}

// GetFeedbackStats 获取反馈统计信息
func (s *FeedbackService) GetFeedbackStats(userID *uint) (map[string]interface{}, error) {
	query := s.db.Model(&model.UserFeedback{})

	// 如果指定用户ID，只统计该用户的反馈
	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}

	stats := make(map[string]interface{})

	// 总反馈数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}
	stats["total"] = total

	// 按状态统计
	var statusStats []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}
	if err := query.Select("status, COUNT(*) as count").Group("status").Scan(&statusStats).Error; err != nil {
		return nil, err
	}
	stats["by_status"] = statusStats

	// 按类型统计
	var typeStats []struct {
		Type  string `json:"type"`
		Count int64  `json:"count"`
	}
	if err := query.Select("type, COUNT(*) as count").Group("type").Scan(&typeStats).Error; err != nil {
		return nil, err
	}
	stats["by_type"] = typeStats

	// 按评分统计
	var ratingStats []struct {
		Rating int   `json:"rating"`
		Count  int64 `json:"count"`
	}
	if err := query.Select("rating, COUNT(*) as count").
		Where("rating IS NOT NULL").
		Group("rating").
		Scan(&ratingStats).Error; err != nil {
		return nil, err
	}
	stats["by_rating"] = ratingStats

	// 平均评分
	var avgRating float64
	if err := query.Select("AVG(rating) as avg_rating").
		Where("rating IS NOT NULL").
		Scan(&avgRating).Error; err != nil {
		return nil, err
	}
	stats["avg_rating"] = avgRating

	// 今日新增
	var todayCount int64
	today := time.Now().Format("2006-01-02")
	if err := query.Where("DATE(created_at) = ?", today).Count(&todayCount).Error; err != nil {
		return nil, err
	}
	stats["today_count"] = todayCount

	return stats, nil
}

// GetRecentFeedbacks 获取最近的反馈
func (s *FeedbackService) GetRecentFeedbacks(limit int) ([]*FeedbackDTO, error) {
	if limit <= 0 {
		limit = 10
	}

	var feedbacks []model.UserFeedback
	if err := s.db.Order("created_at DESC").
		Limit(limit).
		Preload("User").
		Find(&feedbacks).Error; err != nil {
		return nil, err
	}

	feedbackDTOs := make([]*FeedbackDTO, len(feedbacks))
	for i, feedback := range feedbacks {
		feedbackDTOs[i] = s.toFeedbackDTO(&feedback)
	}

	return feedbackDTOs, nil
}

// GetHighRatingFeedbacks 获取高评分反馈
func (s *FeedbackService) GetHighRatingFeedbacks(minRating int, limit int) ([]*FeedbackDTO, error) {
	if limit <= 0 {
		limit = 10
	}
	if minRating <= 0 {
		minRating = 4
	}

	var feedbacks []model.UserFeedback
	if err := s.db.Where("rating >= ? AND type = ?", minRating, "praise").
		Order("rating DESC, created_at DESC").
		Limit(limit).
		Preload("User").
		Find(&feedbacks).Error; err != nil {
		return nil, err
	}

	feedbackDTOs := make([]*FeedbackDTO, len(feedbacks))
	for i, feedback := range feedbacks {
		feedbackDTOs[i] = s.toFeedbackDTO(&feedback)
	}

	return feedbackDTOs, nil
}

// GetBugReports 获取Bug报告
func (s *FeedbackService) GetBugReports(status string, limit int) ([]*FeedbackDTO, error) {
	if limit <= 0 {
		limit = 10
	}

	query := s.db.Where("type = ?", "bug")

	if status != "" {
		query = query.Where("status = ?", status)
	}

	var feedbacks []model.UserFeedback
	if err := query.Order("created_at DESC").
		Limit(limit).
		Preload("User").
		Find(&feedbacks).Error; err != nil {
		return nil, err
	}

	feedbackDTOs := make([]*FeedbackDTO, len(feedbacks))
	for i, feedback := range feedbacks {
		feedbackDTOs[i] = s.toFeedbackDTO(&feedback)
	}

	return feedbackDTOs, nil
}

// GetFeatureRequests 获取功能请求
func (s *FeedbackService) GetFeatureRequests(status string, limit int) ([]*FeedbackDTO, error) {
	if limit <= 0 {
		limit = 10
	}

	query := s.db.Where("type = ?", "feature")

	if status != "" {
		query = query.Where("status = ?", status)
	}

	var feedbacks []model.UserFeedback
	if err := query.Order("created_at DESC").
		Limit(limit).
		Preload("User").
		Find(&feedbacks).Error; err != nil {
		return nil, err
	}

	feedbackDTOs := make([]*FeedbackDTO, len(feedbacks))
	for i, feedback := range feedbacks {
		feedbackDTOs[i] = s.toFeedbackDTO(&feedback)
	}

	return feedbackDTOs, nil
}

// MarkFeedbackReviewed 标记反馈为已审核
func (s *FeedbackService) MarkFeedbackReviewed(id uint, adminNotes string) error {
	var feedback model.UserFeedback
	if err := s.db.First(&feedback, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("反馈不存在")
		}
		return err
	}

	return s.db.Model(&feedback).Updates(map[string]interface{}{
		"status":      "reviewed",
		"admin_notes": adminNotes,
	}).Error
}

// ResolveFeedback 解决反馈
func (s *FeedbackService) ResolveFeedback(id uint, adminNotes string) error {
	var feedback model.UserFeedback
	if err := s.db.First(&feedback, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("反馈不存在")
		}
		return err
	}

	return s.db.Model(&feedback).Updates(map[string]interface{}{
		"status":      "resolved",
		"admin_notes": adminNotes,
	}).Error
}

// 辅助函数
func (s *FeedbackService) toFeedbackDTO(feedback *model.UserFeedback) *FeedbackDTO {
	dto := &FeedbackDTO{
		ID:          feedback.ID,
		UserID:      feedback.UserID,
		Type:        feedback.Type,
		Subject:     feedback.Subject,
		Content:     feedback.Content,
		Rating:      feedback.Rating,
		ContactInfo: feedback.ContactInfo,
		Status:      feedback.Status,
		AdminNotes:  feedback.AdminNotes,
		CreatedAt:   feedback.CreatedAt,
		UpdatedAt:   feedback.UpdatedAt,
	}

	// 添加用户信息
	if feedback.User.ID != 0 {
		dto.Username = feedback.User.Username
	}

	return dto
}

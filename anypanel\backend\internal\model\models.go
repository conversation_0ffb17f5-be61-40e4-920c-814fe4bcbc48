package model

import (
	"fmt"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// User 用户模型
type User struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	UUID         string         `json:"uuid" gorm:"uniqueIndex;size:36"`
	Username     string         `json:"username" gorm:"uniqueIndex;size:50"`
	Email        string         `json:"email" gorm:"uniqueIndex;size:100"`
	PasswordHash string         `json:"-" gorm:"size:255"`
	Role         string         `json:"role" gorm:"type:enum('admin','user');default:'user'"`
	Status       string         `json:"status" gorm:"type:enum('active','inactive','expired');default:'active'"`
	TrafficLimit int64          `json:"traffic_limit" gorm:"default:0"`
	TrafficUsed  int64          `json:"traffic_used" gorm:"default:0"`
	DeviceLimit  int            `json:"device_limit" gorm:"default:1"`
	SpeedLimit   int            `json:"speed_limit" gorm:"default:0"`
	ExpiredAt    *time.Time     `json:"expired_at"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Nodes             []Node             `json:"nodes" gorm:"many2many:user_nodes;"`
	UserSubscriptions []UserSubscription `json:"user_subscriptions" gorm:"foreignKey:UserID"`
	Orders            []Order            `json:"orders" gorm:"foreignKey:UserID"`
}

// Node 节点模型
type Node struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Name        string         `json:"name" gorm:"size:100"`
	Protocol    string         `json:"protocol" gorm:"size:20;default:'anytls'"`
	Host        string         `json:"host" gorm:"size:255"`
	Port        int            `json:"port"`
	Password    string         `json:"password" gorm:"size:255"`
	Config      datatypes.JSON `json:"config"` // 协议特定配置
	ServerName  string         `json:"server_name" gorm:"size:255"`
	Status      string         `json:"status" gorm:"type:enum('online','offline','maintenance');default:'offline'"`
	SortOrder   int            `json:"sort_order" gorm:"default:0"`
	TrafficRate float64        `json:"traffic_rate" gorm:"type:decimal(10,2);default:1.0"`
	MaxUsers    int            `json:"max_users" gorm:"default:0"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Users []User `json:"users" gorm:"many2many:user_nodes;"`
}

// PermissionGroup 权限组模型
type PermissionGroup struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Name        string         `json:"name" gorm:"uniqueIndex;size:100"`
	Description string         `json:"description" gorm:"type:text"`
	SortOrder   int            `json:"sort_order" gorm:"default:0"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Nodes    []Node    `json:"nodes" gorm:"many2many:permission_group_nodes;"`
	Products []Product `json:"products" gorm:"foreignKey:PermissionGroupID"`
}

// Product 订阅商品模型
type Product struct {
	ID                uint           `json:"id" gorm:"primaryKey"`
	Name              string         `json:"name" gorm:"size:100"`
	Description       string         `json:"description" gorm:"type:text"`
	Price             float64        `json:"price" gorm:"type:decimal(10,2)"`
	TrafficLimit      int64          `json:"traffic_limit"`
	DurationDays      int            `json:"duration_days"`
	DeviceLimit       int            `json:"device_limit" gorm:"default:1"`
	SpeedLimit        int            `json:"speed_limit" gorm:"default:0"`
	PermissionGroupID uint           `json:"permission_group_id"`
	Status            string         `json:"status" gorm:"type:enum('active','inactive');default:'active'"`
	SortOrder         int            `json:"sort_order" gorm:"default:0"`
	CreatedAt         time.Time      `json:"created_at"`
	UpdatedAt         time.Time      `json:"updated_at"`
	DeletedAt         gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	PermissionGroup   PermissionGroup    `json:"permission_group" gorm:"foreignKey:PermissionGroupID"`
	UserSubscriptions []UserSubscription `json:"user_subscriptions" gorm:"foreignKey:ProductID"`
	Orders            []Order            `json:"orders" gorm:"foreignKey:ProductID"`
}

// UserSubscription 用户订阅模型
type UserSubscription struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	UserID    uint           `json:"user_id"`
	ProductID uint           `json:"product_id"`
	OrderID   string         `json:"order_id" gorm:"size:50"`
	Status    string         `json:"status" gorm:"type:enum('active','expired','cancelled');default:'active'"`
	StartedAt time.Time      `json:"started_at"`
	ExpiredAt time.Time      `json:"expired_at"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	User    User    `json:"user" gorm:"foreignKey:UserID"`
	Product Product `json:"product" gorm:"foreignKey:ProductID"`
}

// Order 订单模型
type Order struct {
	ID            uint           `json:"id" gorm:"primaryKey"`
	OrderID       string         `json:"order_id" gorm:"uniqueIndex;size:50"`
	UserID        uint           `json:"user_id"`
	ProductID     uint           `json:"product_id"`
	Amount        float64        `json:"amount" gorm:"type:decimal(10,2)"`
	Status        string         `json:"status" gorm:"type:enum('pending','paid','cancelled','refunded');default:'pending'"`
	PaymentMethod string         `json:"payment_method" gorm:"size:50"`
	PaymentID     string         `json:"payment_id" gorm:"size:100"`
	PaidAt        *time.Time     `json:"paid_at"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	User    User    `json:"user" gorm:"foreignKey:UserID"`
	Product Product `json:"product" gorm:"foreignKey:ProductID"`
}

// TrafficLog 流量统计模型
type TrafficLog struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	UserID     uint      `json:"user_id"`
	NodeID     uint      `json:"node_id"`
	Upload     int64     `json:"upload" gorm:"default:0"`
	Download   int64     `json:"download" gorm:"default:0"`
	RecordedAt time.Time `json:"recorded_at"`

	// 关联关系
	User User `json:"user" gorm:"foreignKey:UserID"`
	Node Node `json:"node" gorm:"foreignKey:NodeID"`
}

// OnlineUser 在线用户模型
type OnlineUser struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	UserID      uint      `json:"user_id"`
	NodeID      uint      `json:"node_id"`
	IPAddress   string    `json:"ip_address" gorm:"size:45"`
	ConnectedAt time.Time `json:"connected_at"`
	LastSeen    time.Time `json:"last_seen"`

	// 关联关系
	User User `json:"user" gorm:"foreignKey:UserID"`
	Node Node `json:"node" gorm:"foreignKey:NodeID"`
}

// Payment 支付方式模型
type Payment struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	Name         string         `json:"name" gorm:"size:100"`
	Method       string         `json:"method" gorm:"uniqueIndex;size:50"` // alipay_f2f, wechat_pay_native, etc.
	Config       datatypes.JSON `json:"config"`                            // 支付配置信息
	Enable       bool           `json:"enable" gorm:"default:true"`
	SortOrder    int            `json:"sort_order" gorm:"default:0"`
	NotifyDomain string         `json:"notify_domain" gorm:"size:255"`
	UUID         string         `json:"uuid" gorm:"uniqueIndex;size:36"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`
}

// PaymentLog 支付日志模型
type PaymentLog struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	OrderID      string         `json:"order_id" gorm:"size:50"`
	PaymentID    string         `json:"payment_id" gorm:"size:100"` // 第三方支付平台交易号
	Method       string         `json:"method" gorm:"size:50"`      // 支付方式
	Amount       float64        `json:"amount" gorm:"type:decimal(10,2)"`
	Status       string         `json:"status" gorm:"size:20"` // success, failed, pending
	CallbackData datatypes.JSON `json:"callback_data"`         // 回调数据
	ErrorMessage string         `json:"error_message" gorm:"size:500"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`

	// 注意：暂时移除关联定义以避免循环引用问题
	// Order Order `json:"order" gorm:"foreignKey:OrderID;references:OrderID"`
}

// LoginLog 登录日志模型
type LoginLog struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	UserID     uint      `json:"user_id"`
	IPAddress  string    `json:"ip_address" gorm:"size:45"`
	UserAgent  string    `json:"user_agent" gorm:"type:text"`
	Location   string    `json:"location" gorm:"size:255"`
	Status     string    `json:"status" gorm:"type:enum('success','failed');default:'success'"`
	Method     string    `json:"method" gorm:"size:50"`       // password, oauth, etc.
	FailReason string    `json:"fail_reason" gorm:"size:255"` // 失败原因
	LoginAt    time.Time `json:"login_at"`

	// 关联关系
	User User `json:"user" gorm:"foreignKey:UserID"`
}

// FAQ 常见问题模型
type FAQ struct {
	ID         uint           `json:"id" gorm:"primaryKey"`
	CategoryID uint           `json:"category_id"`
	Question   string         `json:"question" gorm:"size:500"`
	Answer     string         `json:"answer" gorm:"type:text"`
	SortOrder  int            `json:"sort_order" gorm:"default:0"`
	Status     string         `json:"status" gorm:"type:enum('active','inactive');default:'active'"`
	ViewCount  int64          `json:"view_count" gorm:"default:0"`
	IsHelpful  int64          `json:"is_helpful" gorm:"default:0"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Category FAQCategory `json:"category" gorm:"foreignKey:CategoryID"`
}

// FAQCategory FAQ分类模型
type FAQCategory struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Name        string         `json:"name" gorm:"size:100"`
	Description string         `json:"description" gorm:"type:text"`
	SortOrder   int            `json:"sort_order" gorm:"default:0"`
	Status      string         `json:"status" gorm:"type:enum('active','inactive');default:'active'"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	FAQs []FAQ `json:"faqs" gorm:"foreignKey:CategoryID"`
}

// Ticket 工单模型
type Ticket struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	TicketNo    string         `json:"ticket_no" gorm:"uniqueIndex;size:50"`
	UserID      uint           `json:"user_id"`
	Subject     string         `json:"subject" gorm:"size:255"`
	Content     string         `json:"content" gorm:"type:text"`
	Priority    string         `json:"priority" gorm:"type:enum('low','medium','high','urgent');default:'medium'"`
	Status      string         `json:"status" gorm:"type:enum('open','in_progress','pending','resolved','closed');default:'open'"`
	Category    string         `json:"category" gorm:"size:100"`
	AssignedTo  *uint          `json:"assigned_to"`
	LastReplyAt *time.Time     `json:"last_reply_at"`
	ResolvedAt  *time.Time     `json:"resolved_at"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	User     User          `json:"user" gorm:"foreignKey:UserID"`
	Assignee *User         `json:"assignee" gorm:"foreignKey:AssignedTo"`
	Replies  []TicketReply `json:"replies" gorm:"foreignKey:TicketID"`
}

// TicketReply 工单回复模型
type TicketReply struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	TicketID  uint      `json:"ticket_id"`
	UserID    uint      `json:"user_id"`
	Content   string    `json:"content" gorm:"type:text"`
	IsStaff   bool      `json:"is_staff" gorm:"default:false"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// 关联关系
	Ticket Ticket `json:"ticket" gorm:"foreignKey:TicketID"`
	User   User   `json:"user" gorm:"foreignKey:UserID"`
}

// Announcement 系统公告模型
type Announcement struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Title       string         `json:"title" gorm:"size:255"`
	Content     string         `json:"content" gorm:"type:text"`
	Type        string         `json:"type" gorm:"type:enum('info','warning','success','error');default:'info'"`
	Priority    string         `json:"priority" gorm:"type:enum('low','medium','high');default:'medium'"`
	IsSticky    bool           `json:"is_sticky" gorm:"default:false"`
	ShowOnLogin bool           `json:"show_on_login" gorm:"default:false"`
	Status      string         `json:"status" gorm:"type:enum('draft','published','archived');default:'draft'"`
	PublishedAt *time.Time     `json:"published_at"`
	ExpiresAt   *time.Time     `json:"expires_at"`
	ViewCount   int64          `json:"view_count" gorm:"default:0"`
	CreatedBy   uint           `json:"created_by"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Creator User `json:"creator" gorm:"foreignKey:CreatedBy"`
}

// UserFeedback 用户反馈模型
type UserFeedback struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	UserID      uint           `json:"user_id"`
	Type        string         `json:"type" gorm:"type:enum('bug','feature','suggestion','complaint','praise');default:'suggestion'"`
	Subject     string         `json:"subject" gorm:"size:255"`
	Content     string         `json:"content" gorm:"type:text"`
	Rating      *int           `json:"rating"` // 1-5 stars
	ContactInfo string         `json:"contact_info" gorm:"size:255"`
	Status      string         `json:"status" gorm:"type:enum('pending','reviewed','resolved','closed');default:'pending'"`
	AdminNotes  string         `json:"admin_notes" gorm:"type:text"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	User User `json:"user" gorm:"foreignKey:UserID"`
}

// HelpDocument 帮助文档模型
type HelpDocument struct {
	ID         uint           `json:"id" gorm:"primaryKey"`
	CategoryID uint           `json:"category_id"`
	Title      string         `json:"title" gorm:"size:255"`
	Content    string         `json:"content" gorm:"type:longtext"`
	Summary    string         `json:"summary" gorm:"type:text"`
	Tags       string         `json:"tags" gorm:"size:500"`
	SortOrder  int            `json:"sort_order" gorm:"default:0"`
	Status     string         `json:"status" gorm:"type:enum('draft','published','archived');default:'draft'"`
	ViewCount  int64          `json:"view_count" gorm:"default:0"`
	IsPublic   bool           `json:"is_public" gorm:"default:true"`
	CreatedBy  uint           `json:"created_by"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Category HelpCategory `json:"category" gorm:"foreignKey:CategoryID"`
	Creator  User         `json:"creator" gorm:"foreignKey:CreatedBy"`
}

// HelpCategory 帮助文档分类模型
type HelpCategory struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Name        string         `json:"name" gorm:"size:100"`
	Description string         `json:"description" gorm:"type:text"`
	Icon        string         `json:"icon" gorm:"size:100"`
	SortOrder   int            `json:"sort_order" gorm:"default:0"`
	Status      string         `json:"status" gorm:"type:enum('active','inactive');default:'active'"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Documents []HelpDocument `json:"documents" gorm:"foreignKey:CategoryID"`
}

// Validate 验证用户模型
func (u *User) Validate() error {
	if u.Username == "" {
		return fmt.Errorf("用户名不能为空")
	}
	if u.Email == "" {
		return fmt.Errorf("邮箱不能为空")
	}
	if u.PasswordHash == "" {
		return fmt.Errorf("密码哈希不能为空")
	}
	if u.Role != "admin" && u.Role != "user" {
		return fmt.Errorf("无效的用户角色")
	}
	if u.DeviceLimit < 0 {
		return fmt.Errorf("设备限制不能为负数")
	}
	if u.SpeedLimit < 0 {
		return fmt.Errorf("速度限制不能为负数")
	}
	return nil
}

// ToSafeJSON 返回安全的用户JSON数据（不包含敏感信息）
func (u *User) ToSafeJSON() map[string]interface{} {
	return map[string]interface{}{
		"id":            u.ID,
		"uuid":          u.UUID,
		"username":      u.Username,
		"email":         u.Email,
		"role":          u.Role,
		"status":        u.Status,
		"traffic_limit": u.TrafficLimit,
		"traffic_used":  u.TrafficUsed,
		"device_limit":  u.DeviceLimit,
		"speed_limit":   u.SpeedLimit,
		"expired_at":    u.ExpiredAt,
		"created_at":    u.CreatedAt,
		"updated_at":    u.UpdatedAt,
	}
}

// Validate 验证节点模型
func (n *Node) Validate() error {
	if n.Name == "" {
		return fmt.Errorf("节点名称不能为空")
	}
	if n.Host == "" {
		return fmt.Errorf("节点地址不能为空")
	}
	if n.Port <= 0 || n.Port > 65535 {
		return fmt.Errorf("无效的端口号")
	}
	if n.Password == "" {
		return fmt.Errorf("节点密码不能为空")
	}
	if n.Protocol == "" {
		return fmt.Errorf("节点协议不能为空")
	}
	if n.Status != "online" && n.Status != "offline" && n.Status != "maintenance" {
		return fmt.Errorf("无效的节点状态")
	}
	if n.MaxUsers < 0 {
		return fmt.Errorf("最大用户数不能为负数")
	}
	if n.TrafficRate <= 0 {
		return fmt.Errorf("流量倍率必须大于0")
	}
	return nil
}

// GetAvailableTraffic 获取用户可用流量
func (u *User) GetAvailableTraffic() int64 {
	if u.TrafficLimit == 0 {
		return 0 // 无限流量
	}
	return u.TrafficLimit - u.TrafficUsed
}

// IsExpired 检查用户是否过期
func (u *User) IsExpired() bool {
	if u.ExpiredAt == nil {
		return false
	}
	return time.Now().After(*u.ExpiredAt)
}

// IsActive 检查用户是否活跃
func (u *User) IsActive() bool {
	return u.Status == "active" && !u.IsExpired()
}

// CanConnect 检查用户是否可以连接
func (u *User) CanConnect() bool {
	if !u.IsActive() {
		return false
	}

	// 检查流量限制
	if u.TrafficLimit > 0 && u.TrafficUsed >= u.TrafficLimit {
		return false
	}

	// 检查设备限制（需要在线查询）
	// 这里需要在实际使用时查询在线用户表

	return true
}

// GetSubscriptionStatus 获取用户订阅状态
func (u *User) GetSubscriptionStatus() string {
	if u.IsExpired() {
		return "expired"
	}
	if u.Status == "inactive" {
		return "inactive"
	}
	if u.Status == "active" {
		if u.TrafficLimit > 0 && u.TrafficUsed >= u.TrafficLimit {
			return "traffic_exceeded"
		}
		return "active"
	}
	return "unknown"
}

// Validate 验证权限组模型
func (pg *PermissionGroup) Validate() error {
	if pg.Name == "" {
		return fmt.Errorf("权限组名称不能为空")
	}
	if len(pg.Name) > 100 {
		return fmt.Errorf("权限组名称不能超过100个字符")
	}
	return nil
}

// Validate 验证商品模型
func (p *Product) Validate() error {
	if p.Name == "" {
		return fmt.Errorf("商品名称不能为空")
	}
	if p.Price < 0 {
		return fmt.Errorf("商品价格不能为负数")
	}
	if p.DurationDays <= 0 {
		return fmt.Errorf("商品有效期必须大于0天")
	}
	if p.DeviceLimit < 0 {
		return fmt.Errorf("设备限制不能为负数")
	}
	if p.SpeedLimit < 0 {
		return fmt.Errorf("速度限制不能为负数")
	}
	if p.PermissionGroupID == 0 {
		return fmt.Errorf("必须指定权限组")
	}
	if p.Status != "active" && p.Status != "inactive" {
		return fmt.Errorf("无效的商品状态")
	}
	return nil
}

// Validate 验证订单模型
func (o *Order) Validate() error {
	if o.OrderID == "" {
		return fmt.Errorf("订单ID不能为空")
	}
	if o.UserID == 0 {
		return fmt.Errorf("用户ID不能为空")
	}
	if o.ProductID == 0 {
		return fmt.Errorf("商品ID不能为空")
	}
	if o.Amount < 0 {
		return fmt.Errorf("订单金额不能为负数")
	}
	if o.Status != "pending" && o.Status != "paid" && o.Status != "cancelled" && o.Status != "refunded" {
		return fmt.Errorf("无效的订单状态")
	}
	return nil
}

// Validate 验证用户订阅模型
func (us *UserSubscription) Validate() error {
	if us.UserID == 0 {
		return fmt.Errorf("用户ID不能为空")
	}
	if us.ProductID == 0 {
		return fmt.Errorf("商品ID不能为空")
	}
	if us.Status != "active" && us.Status != "expired" && us.Status != "cancelled" {
		return fmt.Errorf("无效的订阅状态")
	}
	if us.ExpiredAt.IsZero() {
		return fmt.Errorf("过期时间不能为空")
	}
	return nil
}

// Validate 验证流量日志模型
func (tl *TrafficLog) Validate() error {
	if tl.UserID == 0 {
		return fmt.Errorf("用户ID不能为空")
	}
	if tl.NodeID == 0 {
		return fmt.Errorf("节点ID不能为空")
	}
	if tl.Upload < 0 {
		return fmt.Errorf("上传流量不能为负数")
	}
	if tl.Download < 0 {
		return fmt.Errorf("下载流量不能为负数")
	}
	if tl.RecordedAt.IsZero() {
		return fmt.Errorf("记录时间不能为空")
	}
	return nil
}

// Validate 验证在线用户模型
func (ou *OnlineUser) Validate() error {
	if ou.UserID == 0 {
		return fmt.Errorf("用户ID不能为空")
	}
	if ou.NodeID == 0 {
		return fmt.Errorf("节点ID不能为空")
	}
	if ou.IPAddress == "" {
		return fmt.Errorf("IP地址不能为空")
	}
	if ou.ConnectedAt.IsZero() {
		return fmt.Errorf("连接时间不能为空")
	}
	if ou.LastSeen.IsZero() {
		return fmt.Errorf("最后活跃时间不能为空")
	}
	return nil
}

// IsExpired 检查订阅是否过期
func (us *UserSubscription) IsExpired() bool {
	return time.Now().After(us.ExpiredAt)
}

// IsActive 检查订阅是否活跃
func (us *UserSubscription) IsActive() bool {
	return us.Status == "active" && !us.IsExpired()
}

// GetRemainingDays 获取订阅剩余天数
func (us *UserSubscription) GetRemainingDays() int {
	if us.IsExpired() {
		return 0
	}
	duration := time.Until(us.ExpiredAt)
	return int(duration.Hours() / 24)
}

// IsOnline 检查用户是否在线
func (ou *OnlineUser) IsOnline() bool {
	// 如果超过5分钟没有活跃，认为离线
	return time.Since(ou.LastSeen) < 5*time.Minute
}

// GetSessionDuration 获取会话持续时间
func (ou *OnlineUser) GetSessionDuration() time.Duration {
	return ou.LastSeen.Sub(ou.ConnectedAt)
}

// GetTotalTraffic 获取总流量
func (tl *TrafficLog) GetTotalTraffic() int64 {
	return tl.Upload + tl.Download
}

// GetActiveSubscription 获取用户当前有效订阅
func (u *User) GetActiveSubscription(db *gorm.DB) (*UserSubscription, error) {
	var subscription UserSubscription
	err := db.Where("user_id = ? AND status = 'active' AND expired_at > ?",
		u.ID, time.Now()).First(&subscription).Error
	if err != nil {
		return nil, err
	}
	return &subscription, nil
}

// GetAccessibleNodes 获取用户可访问的节点（基于权限组）
func (u *User) GetAccessibleNodes(db *gorm.DB) ([]Node, error) {
	subscription, err := u.GetActiveSubscription(db)
	if err != nil {
		return nil, err
	}

	var nodes []Node
	err = db.Joins("JOIN permission_group_nodes ON nodes.id = permission_group_nodes.node_id").
		Joins("JOIN products ON products.permission_group_id = permission_group_nodes.group_id").
		Where("products.id = ? AND nodes.status = 'online'", subscription.ProductID).
		Find(&nodes).Error

	return nodes, err
}

// Validate 验证支付方式模型
func (p *Payment) Validate() error {
	if p.Name == "" {
		return fmt.Errorf("支付方式名称不能为空")
	}
	if p.Method == "" {
		return fmt.Errorf("支付方式标识不能为空")
	}
	if p.UUID == "" {
		return fmt.Errorf("支付方式UUID不能为空")
	}
	return nil
}

// Validate 验证支付日志模型
func (pl *PaymentLog) Validate() error {
	if pl.OrderID == "" {
		return fmt.Errorf("订单ID不能为空")
	}
	if pl.Method == "" {
		return fmt.Errorf("支付方式不能为空")
	}
	if pl.Amount < 0 {
		return fmt.Errorf("支付金额不能为负数")
	}
	if pl.Status != "success" && pl.Status != "failed" && pl.Status != "pending" {
		return fmt.Errorf("无效的支付状态")
	}
	return nil
}

// Validate 验证登录日志模型
func (ll *LoginLog) Validate() error {
	if ll.UserID == 0 {
		return fmt.Errorf("用户ID不能为空")
	}
	if ll.IPAddress == "" {
		return fmt.Errorf("IP地址不能为空")
	}
	if ll.Status != "success" && ll.Status != "failed" {
		return fmt.Errorf("无效的登录状态")
	}
	if ll.LoginAt.IsZero() {
		return fmt.Errorf("登录时间不能为空")
	}
	return nil
}

// Validate 验证FAQ模型
func (f *FAQ) Validate() error {
	if f.Question == "" {
		return fmt.Errorf("问题不能为空")
	}
	if f.Answer == "" {
		return fmt.Errorf("答案不能为空")
	}
	if f.CategoryID == 0 {
		return fmt.Errorf("分类ID不能为空")
	}
	if f.Status != "active" && f.Status != "inactive" {
		return fmt.Errorf("无效的状态")
	}
	return nil
}

// Validate 验证FAQ分类模型
func (fc *FAQCategory) Validate() error {
	if fc.Name == "" {
		return fmt.Errorf("分类名称不能为空")
	}
	if fc.Status != "active" && fc.Status != "inactive" {
		return fmt.Errorf("无效的状态")
	}
	return nil
}

// Validate 验证工单模型
func (t *Ticket) Validate() error {
	if t.UserID == 0 {
		return fmt.Errorf("用户ID不能为空")
	}
	if t.Subject == "" {
		return fmt.Errorf("主题不能为空")
	}
	if t.Content == "" {
		return fmt.Errorf("内容不能为空")
	}
	validPriorities := map[string]bool{"low": true, "medium": true, "high": true, "urgent": true}
	if !validPriorities[t.Priority] {
		return fmt.Errorf("无效的优先级")
	}
	validStatuses := map[string]bool{"open": true, "in_progress": true, "pending": true, "resolved": true, "closed": true}
	if !validStatuses[t.Status] {
		return fmt.Errorf("无效的状态")
	}
	return nil
}

// Validate 验证工单回复模型
func (tr *TicketReply) Validate() error {
	if tr.TicketID == 0 {
		return fmt.Errorf("工单ID不能为空")
	}
	if tr.UserID == 0 {
		return fmt.Errorf("用户ID不能为空")
	}
	if tr.Content == "" {
		return fmt.Errorf("回复内容不能为空")
	}
	return nil
}

// Validate 验证公告模型
func (a *Announcement) Validate() error {
	if a.Title == "" {
		return fmt.Errorf("标题不能为空")
	}
	if a.Content == "" {
		return fmt.Errorf("内容不能为空")
	}
	if a.CreatedBy == 0 {
		return fmt.Errorf("创建者ID不能为空")
	}
	validTypes := map[string]bool{"info": true, "warning": true, "success": true, "error": true}
	if !validTypes[a.Type] {
		return fmt.Errorf("无效的公告类型")
	}
	validPriorities := map[string]bool{"low": true, "medium": true, "high": true}
	if !validPriorities[a.Priority] {
		return fmt.Errorf("无效的优先级")
	}
	validStatuses := map[string]bool{"draft": true, "published": true, "archived": true}
	if !validStatuses[a.Status] {
		return fmt.Errorf("无效的状态")
	}
	return nil
}

// Validate 验证用户反馈模型
func (uf *UserFeedback) Validate() error {
	if uf.UserID == 0 {
		return fmt.Errorf("用户ID不能为空")
	}
	if uf.Subject == "" {
		return fmt.Errorf("主题不能为空")
	}
	if uf.Content == "" {
		return fmt.Errorf("内容不能为空")
	}
	validTypes := map[string]bool{"bug": true, "feature": true, "suggestion": true, "complaint": true, "praise": true}
	if !validTypes[uf.Type] {
		return fmt.Errorf("无效的反馈类型")
	}
	if uf.Rating != nil && (*uf.Rating < 1 || *uf.Rating > 5) {
		return fmt.Errorf("评分必须在1-5之间")
	}
	validStatuses := map[string]bool{"pending": true, "reviewed": true, "resolved": true, "closed": true}
	if !validStatuses[uf.Status] {
		return fmt.Errorf("无效的状态")
	}
	return nil
}

// Validate 验证帮助文档模型
func (hd *HelpDocument) Validate() error {
	if hd.Title == "" {
		return fmt.Errorf("标题不能为空")
	}
	if hd.Content == "" {
		return fmt.Errorf("内容不能为空")
	}
	if hd.CategoryID == 0 {
		return fmt.Errorf("分类ID不能为空")
	}
	if hd.CreatedBy == 0 {
		return fmt.Errorf("创建者ID不能为空")
	}
	validStatuses := map[string]bool{"draft": true, "published": true, "archived": true}
	if !validStatuses[hd.Status] {
		return fmt.Errorf("无效的状态")
	}
	return nil
}

// Validate 验证帮助文档分类模型
func (hc *HelpCategory) Validate() error {
	if hc.Name == "" {
		return fmt.Errorf("分类名称不能为空")
	}
	if hc.Status != "active" && hc.Status != "inactive" {
		return fmt.Errorf("无效的状态")
	}
	return nil
}

// SecurityEvent 安全事件模型 - 记录安全相关事件
type SecurityEvent struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	UserID      uint           `json:"user_id"`                   // 可选，如果是系统事件可以为空
	EventType   string         `json:"event_type" gorm:"size:50"` // login_fail, rate_limit, suspicious_activity, etc.
	IPAddress   string         `json:"ip_address" gorm:"size:45"`
	UserAgent   string         `json:"user_agent" gorm:"type:text"`
	Description string         `json:"description" gorm:"type:text"`
	Severity    string         `json:"severity" gorm:"type:enum('low','medium','high','critical');default:'low'"`
	Source      string         `json:"source" gorm:"size:100"` // api, admin, system, etc.
	EventData   datatypes.JSON `json:"event_data"`             // 事件相关的额外数据
	CreatedAt   time.Time      `json:"created_at"`

	// 关联关系
	User User `json:"user" gorm:"foreignKey:UserID"`
}

// IPBanRecord IP封禁记录模型
type IPBanRecord struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	IPAddress string         `json:"ip_address" gorm:"uniqueIndex;size:45"`
	Reason    string         `json:"reason" gorm:"size:255"`
	BanType   string         `json:"ban_type" gorm:"type:enum('temporary','permanent');default:'temporary'"`
	ExpiresAt *time.Time     `json:"expires_at"` // 临时封禁的过期时间
	IsActive  bool           `json:"is_active" gorm:"default:true"`
	CreatedBy uint           `json:"created_by"` // 创建封禁的管理员ID
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Creator User `json:"creator" gorm:"foreignKey:CreatedBy"`
}

// AuditLog 审计日志模型 - 记录重要操作
type AuditLog struct {
	ID         uint           `json:"id" gorm:"primaryKey"`
	UserID     uint           `json:"user_id"`
	Action     string         `json:"action" gorm:"size:100"`     // create_user, delete_order, modify_node, etc.
	Resource   string         `json:"resource" gorm:"size:100"`   // user, order, node, etc.
	ResourceID string         `json:"resource_id" gorm:"size:50"` // 资源ID
	OldValues  datatypes.JSON `json:"old_values"`                 // 操作前的值
	NewValues  datatypes.JSON `json:"new_values"`                 // 操作后的值
	IPAddress  string         `json:"ip_address" gorm:"size:45"`
	UserAgent  string         `json:"user_agent" gorm:"type:text"`
	Status     string         `json:"status" gorm:"type:enum('success','failed');default:'success'"`
	ErrorMsg   string         `json:"error_msg" gorm:"size:500"`
	CreatedAt  time.Time      `json:"created_at"`

	// 关联关系
	User User `json:"user" gorm:"foreignKey:UserID"`
}

// RateLimitRecord 速率限制记录模型
type RateLimitRecord struct {
	ID           uint      `json:"id" gorm:"primaryKey"`
	Identifier   string    `json:"identifier" gorm:"size:100"` // IP或用户ID
	LimitType    string    `json:"limit_type" gorm:"size:50"`  // ip, user, api_endpoint
	Endpoint     string    `json:"endpoint" gorm:"size:255"`   // API端点
	RequestCount int64     `json:"request_count" gorm:"default:1"`
	WindowStart  time.Time `json:"window_start"`
	LastRequest  time.Time `json:"last_request"`
	IsBlocked    bool      `json:"is_blocked" gorm:"default:false"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// MetricRecord 指标记录模型
type MetricRecord struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	Name      string    `json:"name" gorm:"size:100"`
	Value     float64   `json:"value"`
	Labels    string    `json:"labels" gorm:"type:text"` // JSON格式的标签
	Timestamp time.Time `json:"timestamp"`
	CreatedAt time.Time `json:"created_at"`
}

// AlertRule 告警规则模型
type AlertRule struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	Name        string         `json:"name" gorm:"uniqueIndex;size:100"`
	MetricName  string         `json:"metric_name" gorm:"size:100"`
	Operator    string         `json:"operator" gorm:"size:10"` // >, <, >=, <=, ==, !=
	Threshold   float64        `json:"threshold"`
	Duration    int            `json:"duration"` // 持续时间（秒）
	Severity    string         `json:"severity" gorm:"type:enum('critical','warning','info');default:'warning'"`
	Description string         `json:"description" gorm:"type:text"`
	IsActive    bool           `json:"is_active" gorm:"default:true"`
	CreatedBy   uint           `json:"created_by"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联关系
	Creator User `json:"creator" gorm:"foreignKey:CreatedBy"`
}

// SystemLog 系统日志模型
type SystemLog struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	Level     string         `json:"level" gorm:"type:enum('debug','info','warn','error','fatal');default:'info'"`
	Component string         `json:"component" gorm:"size:100"`
	Message   string         `json:"message" gorm:"type:text"`
	Context   datatypes.JSON `json:"context"` // 上下文数据
	TraceID   string         `json:"trace_id" gorm:"size:100"`
	UserID    uint           `json:"user_id"`
	IPAddress string         `json:"ip_address" gorm:"size:45"`
	UserAgent string         `json:"user_agent" gorm:"type:text"`
	CreatedAt time.Time      `json:"created_at"`

	// 关联关系
	User User `json:"user" gorm:"foreignKey:UserID"`
}

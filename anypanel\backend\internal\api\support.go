package api

import (
	"anypanel/internal/middleware"
	"anypanel/internal/service"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SupportHandler 支持系统处理器
type SupportHandler struct {
	faqService          *service.FAQService
	ticketService       *service.TicketService
	announcementService *service.AnnouncementService
	feedbackService     *service.FeedbackService
	helpService         *service.HelpService
}

func NewSupportHandler(db *gorm.DB) *SupportHandler {
	return &SupportHandler{
		faqService:          service.NewFAQService(db),
		ticketService:       service.NewTicketService(db),
		announcementService: service.NewAnnouncementService(db),
		feedbackService:     service.NewFeedbackService(db),
		helpService:         service.NewHelpService(db),
	}
}

// ============= FAQ相关接口 =============

// GetFAQs 获取FAQ列表
func (h *SupportHandler) GetFAQs(c *gin.Context) {
	var req service.FAQQueryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"message": err.Error(),
		})
		return
	}

	resp, err := h.faqService.GetFAQs(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取FAQ失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    resp,
	})
}

// GetFAQByID 获取FAQ详情
func (h *SupportHandler) GetFAQByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的FAQ ID",
			"message": err.Error(),
		})
		return
	}

	faq, err := h.faqService.GetFAQByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "获取FAQ失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    faq,
	})
}

// GetFAQCategories 获取FAQ分类
func (h *SupportHandler) GetFAQCategories(c *gin.Context) {
	categories, err := h.faqService.GetFAQCategories()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取FAQ分类失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    categories,
	})
}

// SearchFAQs 搜索FAQ
func (h *SupportHandler) SearchFAQs(c *gin.Context) {
	keyword := c.Query("keyword")
	if keyword == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "搜索关键词不能为空",
		})
		return
	}

	limitStr := c.DefaultQuery("limit", "10")
	limit, _ := strconv.Atoi(limitStr)

	faqs, err := h.faqService.SearchFAQs(keyword, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "搜索FAQ失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "搜索成功",
		"data":    faqs,
	})
}

// MarkFAQHelpful 标记FAQ为有用
func (h *SupportHandler) MarkFAQHelpful(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的FAQ ID",
			"message": err.Error(),
		})
		return
	}

	if err := h.faqService.MarkFAQHelpful(uint(id)); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "标记失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "标记成功",
	})
}

// ============= 工单相关接口 =============

// GetUserTickets 获取用户工单列表
func (h *SupportHandler) GetUserTickets(c *gin.Context) {
	user, exists := middleware.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "用户未认证",
			"message": "请先登录",
		})
		return
	}

	var req service.TicketQueryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"message": err.Error(),
		})
		return
	}

	resp, err := h.ticketService.GetUserTickets(user.ID, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取工单失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    resp,
	})
}

// GetTicketByID 获取工单详情
func (h *SupportHandler) GetTicketByID(c *gin.Context) {
	user, exists := middleware.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "用户未认证",
			"message": "请先登录",
		})
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的工单ID",
			"message": err.Error(),
		})
		return
	}

	ticket, err := h.ticketService.GetTicketByID(uint(id), true)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "获取工单失败",
			"message": err.Error(),
		})
		return
	}

	// 检查权限（用户只能查看自己的工单，除非是管理员）
	if user.Role != "admin" && ticket.UserID != user.ID {
		c.JSON(http.StatusForbidden, gin.H{
			"error": "没有权限访问此工单",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    ticket,
	})
}

// CreateTicket 创建工单
func (h *SupportHandler) CreateTicket(c *gin.Context) {
	user, exists := middleware.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "用户未认证",
			"message": "请先登录",
		})
		return
	}

	var req service.TicketCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"message": err.Error(),
		})
		return
	}

	ticket, err := h.ticketService.CreateTicket(user.ID, &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "创建工单失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "创建成功",
		"data":    ticket,
	})
}

// ReplyTicket 回复工单
func (h *SupportHandler) ReplyTicket(c *gin.Context) {
	user, exists := middleware.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "用户未认证",
			"message": "请先登录",
		})
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的工单ID",
			"message": err.Error(),
		})
		return
	}

	var req service.TicketReplyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"message": err.Error(),
		})
		return
	}

	isStaff := user.Role == "admin"
	reply, err := h.ticketService.ReplyTicket(uint(id), user.ID, &req, isStaff)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "回复工单失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "回复成功",
		"data":    reply,
	})
}

// CloseTicket 关闭工单
func (h *SupportHandler) CloseTicket(c *gin.Context) {
	user, exists := middleware.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "用户未认证",
			"message": "请先登录",
		})
		return
	}

	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的工单ID",
			"message": err.Error(),
		})
		return
	}

	isStaff := user.Role == "admin"
	if err := h.ticketService.CloseTicket(uint(id), user.ID, isStaff); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "关闭工单失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "工单已关闭",
	})
}

// ============= 公告相关接口 =============

// GetPublicAnnouncements 获取公开公告
func (h *SupportHandler) GetPublicAnnouncements(c *gin.Context) {
	limitStr := c.DefaultQuery("limit", "10")
	limit, _ := strconv.Atoi(limitStr)

	announcements, err := h.announcementService.GetPublicAnnouncements(limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取公告失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    announcements,
	})
}

// GetLoginAnnouncements 获取登录页公告
func (h *SupportHandler) GetLoginAnnouncements(c *gin.Context) {
	announcements, err := h.announcementService.GetLoginAnnouncements()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取公告失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    announcements,
	})
}

// GetAnnouncementByID 获取公告详情
func (h *SupportHandler) GetAnnouncementByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的公告ID",
			"message": err.Error(),
		})
		return
	}

	announcement, err := h.announcementService.GetAnnouncementByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "获取公告失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    announcement,
	})
}

// ============= 反馈相关接口 =============

// GetUserFeedbacks 获取用户反馈列表
func (h *SupportHandler) GetUserFeedbacks(c *gin.Context) {
	user, exists := middleware.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "用户未认证",
			"message": "请先登录",
		})
		return
	}

	var req service.FeedbackQueryRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"message": err.Error(),
		})
		return
	}

	resp, err := h.feedbackService.GetUserFeedbacks(user.ID, &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取反馈失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    resp,
	})
}

// CreateFeedback 创建反馈
func (h *SupportHandler) CreateFeedback(c *gin.Context) {
	user, exists := middleware.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "用户未认证",
			"message": "请先登录",
		})
		return
	}

	var req service.FeedbackCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "请求参数错误",
			"message": err.Error(),
		})
		return
	}

	feedback, err := h.feedbackService.CreateFeedback(user.ID, &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "提交反馈失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"message": "反馈提交成功",
		"data":    feedback,
	})
}

// ============= 帮助文档相关接口 =============

// GetHelpCategories 获取帮助分类
func (h *SupportHandler) GetHelpCategories(c *gin.Context) {
	includeCount := c.DefaultQuery("include_count", "true") == "true"

	categories, err := h.helpService.GetHelpCategories(includeCount)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取帮助分类失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    categories,
	})
}

// GetPublicHelpDocuments 获取公开帮助文档
func (h *SupportHandler) GetPublicHelpDocuments(c *gin.Context) {
	var categoryID *uint
	if categoryIDStr := c.Query("category_id"); categoryIDStr != "" {
		if id, err := strconv.ParseUint(categoryIDStr, 10, 32); err == nil {
			categoryIDUint := uint(id)
			categoryID = &categoryIDUint
		}
	}

	limitStr := c.DefaultQuery("limit", "20")
	limit, _ := strconv.Atoi(limitStr)

	documents, err := h.helpService.GetPublicHelpDocuments(categoryID, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取帮助文档失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    documents,
	})
}

// GetHelpDocumentByID 获取帮助文档详情
func (h *SupportHandler) GetHelpDocumentByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "无效的文档ID",
			"message": err.Error(),
		})
		return
	}

	document, err := h.helpService.GetHelpDocumentByID(uint(id))
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "获取帮助文档失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    document,
	})
}

// SearchHelpDocuments 搜索帮助文档
func (h *SupportHandler) SearchHelpDocuments(c *gin.Context) {
	keyword := c.Query("keyword")
	if keyword == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "搜索关键词不能为空",
		})
		return
	}

	limitStr := c.DefaultQuery("limit", "10")
	limit, _ := strconv.Atoi(limitStr)

	documents, err := h.helpService.SearchHelpDocuments(keyword, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "搜索帮助文档失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "搜索成功",
		"data":    documents,
	})
}

// GetPopularHelpDocuments 获取热门帮助文档
func (h *SupportHandler) GetPopularHelpDocuments(c *gin.Context) {
	limitStr := c.DefaultQuery("limit", "10")
	limit, _ := strconv.Atoi(limitStr)

	documents, err := h.helpService.GetPopularHelpDocuments(limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "获取热门文档失败",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取成功",
		"data":    documents,
	})
}

// ============= 路由设置 =============

// SetupSupportRoutes 设置支持系统路由
func SetupSupportRoutes(router *gin.RouterGroup, db *gorm.DB) {
	supportHandler := NewSupportHandler(db)

	support := router.Group("/support")
	{
		// 公开接口
		support.GET("/announcements", supportHandler.GetPublicAnnouncements)
		support.GET("/announcements/login", supportHandler.GetLoginAnnouncements)
		support.GET("/announcements/:id", supportHandler.GetAnnouncementByID)

		support.GET("/faq/categories", supportHandler.GetFAQCategories)
		support.GET("/faq", supportHandler.GetFAQs)
		support.GET("/faq/:id", supportHandler.GetFAQByID)
		support.GET("/faq/search", supportHandler.SearchFAQs)
		support.POST("/faq/:id/helpful", supportHandler.MarkFAQHelpful)

		support.GET("/help/categories", supportHandler.GetHelpCategories)
		support.GET("/help/documents", supportHandler.GetPublicHelpDocuments)
		support.GET("/help/documents/:id", supportHandler.GetHelpDocumentByID)
		support.GET("/help/search", supportHandler.SearchHelpDocuments)
		support.GET("/help/popular", supportHandler.GetPopularHelpDocuments)

		// 需要认证的接口
		support.Use(middleware.AuthMiddlewareCompat(db))
		{
			// 工单相关
			support.GET("/tickets", supportHandler.GetUserTickets)
			support.GET("/tickets/:id", supportHandler.GetTicketByID)
			support.POST("/tickets", supportHandler.CreateTicket)
			support.POST("/tickets/:id/reply", supportHandler.ReplyTicket)
			support.POST("/tickets/:id/close", supportHandler.CloseTicket)

			// 反馈相关
			support.GET("/feedback", supportHandler.GetUserFeedbacks)
			support.POST("/feedback", supportHandler.CreateFeedback)
		}
	}
}

package service

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"io"
	mathRand "math/rand"
	"time"

	"anypanel/internal/model"

	"gorm.io/gorm"
)

// SystemConfigService 系统配置服务
type SystemConfigService struct {
	db            *gorm.DB
	encryptionKey string
}

// NewSystemConfigService 创建系统配置服务
func NewSystemConfigService(db *gorm.DB, encryptionKey string) *SystemConfigService {
	return &SystemConfigService{
		db:            db,
		encryptionKey: encryptionKey,
	}
}

// GetConfigByKey 根据key获取配置
func (s *SystemConfigService) GetConfigByKey(key string) (*model.SystemConfig, error) {
	var config model.SystemConfig
	err := s.db.Where("config_key = ?", key).First(&config).Error
	if err != nil {
		return nil, err
	}

	// 如果是加密配置，解密
	if config.IsEncrypted {
		decryptedValue, err := s.decrypt(string(config.ConfigValue))
		if err != nil {
			return nil, fmt.Errorf("解密配置失败: %w", err)
		}
		config.ConfigValue = []byte(decryptedValue)
	}

	return &config, nil
}

// GetConfigsByCategory 根据分类获取配置列表
func (s *SystemConfigService) GetConfigsByCategory(category string) ([]model.SystemConfig, error) {
	var configs []model.SystemConfig
	err := s.db.Where("category = ?", category).Order("sort_order ASC").Find(&configs).Error
	if err != nil {
		return nil, err
	}

	// 解密加密的配置
	for i := range configs {
		if configs[i].IsEncrypted {
			decryptedValue, err := s.decrypt(string(configs[i].ConfigValue))
			if err != nil {
				return nil, fmt.Errorf("解密配置失败: %w", err)
			}
			configs[i].ConfigValue = []byte(decryptedValue)
		}
	}

	return configs, nil
}

// GetPublicConfigs 获取公开的配置（可供前端读取）
func (s *SystemConfigService) GetPublicConfigs() ([]model.SystemConfig, error) {
	var configs []model.SystemConfig
	err := s.db.Where("is_public = ?", true).Order("category ASC, sort_order ASC").Find(&configs).Error
	return configs, err
}

// CreateOrUpdateConfig 创建或更新配置
func (s *SystemConfigService) CreateOrUpdateConfig(config *model.SystemConfig) error {
	// 如果是敏感配置，加密存储
	if config.IsEncrypted {
		// 将原始值转换为字符串进行加密
		originalValue := string(config.ConfigValue)

		// 加密原始值（JSON字符串）
		encryptedValue, err := s.encrypt(originalValue)
		if err != nil {
			return fmt.Errorf("加密配置失败: %w", err)
		}

		// 将加密后的值作为JSON字符串存储
		if err := config.SetValue(encryptedValue); err != nil {
			return fmt.Errorf("设置加密配置值失败: %w", err)
		}
	}

	// 查找是否已存在
	var existing model.SystemConfig
	err := s.db.Where("config_key = ?", config.ConfigKey).First(&existing).Error
	if err == gorm.ErrRecordNotFound {
		// 创建新配置
		return s.db.Create(config).Error
	} else if err != nil {
		return err
	} else {
		// 更新现有配置
		config.ID = existing.ID
		return s.db.Save(config).Error
	}
}

// DeleteConfig 删除配置
func (s *SystemConfigService) DeleteConfig(key string) error {
	return s.db.Where("config_key = ?", key).Delete(&model.SystemConfig{}).Error
}

// GetAllConfigs 获取所有配置（管理员使用）
func (s *SystemConfigService) GetAllConfigs() ([]model.SystemConfig, error) {
	var configs []model.SystemConfig
	err := s.db.Order("category ASC, sort_order ASC").Find(&configs).Error
	if err != nil {
		return nil, err
	}

	// 解密加密的配置
	for i := range configs {
		if configs[i].IsEncrypted {
			decryptedValue, err := s.decrypt(string(configs[i].ConfigValue))
			if err != nil {
				return nil, fmt.Errorf("解密配置失败: %w", err)
			}
			configs[i].ConfigValue = []byte(decryptedValue)
		}
	}

	return configs, nil
}

// InitDefaultConfigs 初始化默认配置
func (s *SystemConfigService) InitDefaultConfigs() error {
	defaultConfigs := s.getDefaultConfigs()

	for _, config := range defaultConfigs {
		// 检查是否已存在
		var existing model.SystemConfig
		err := s.db.Where("config_key = ?", config.ConfigKey).First(&existing).Error
		if err == gorm.ErrRecordNotFound {
			// 不存在则创建
			if err := s.CreateOrUpdateConfig(&config); err != nil {
				return fmt.Errorf("创建默认配置失败 %s: %w", config.ConfigKey, err)
			}
		}
	}

	return nil
}

// encrypt 加密数据
func (s *SystemConfigService) encrypt(plaintext string) (string, error) {
	if s.encryptionKey == "" {
		return plaintext, nil
	}

	key := []byte(s.encryptionKey)
	if len(key) < 32 {
		// 如果密钥长度不足32位，填充到32位
		for len(key) < 32 {
			key = append(key, 0)
		}
	}
	key = key[:32] // 截取前32位

	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}

	ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// decrypt 解密数据
func (s *SystemConfigService) decrypt(ciphertext string) (string, error) {
	if s.encryptionKey == "" {
		return ciphertext, nil
	}

	key := []byte(s.encryptionKey)
	if len(key) < 32 {
		// 如果密钥长度不足32位，填充到32位
		for len(key) < 32 {
			key = append(key, 0)
		}
	}
	key = key[:32] // 截取前32位

	data, err := base64.StdEncoding.DecodeString(ciphertext)
	if err != nil {
		return "", err
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return "", fmt.Errorf("ciphertext too short")
	}

	nonce, ciphertext_bytes := data[:nonceSize], data[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext_bytes, nil)
	if err != nil {
		return "", err
	}

	return string(plaintext), nil
}

// getDefaultConfigs 获取默认配置
func (s *SystemConfigService) getDefaultConfigs() []model.SystemConfig {
	configs := []model.SystemConfig{
		{
			ConfigKey:   "v2bx.server_token",
			ConfigName:  "V2bX服务器令牌",
			ConfigType:  "string",
			Description: "V2bX后端服务器访问令牌",
			Category:    "v2bx",
			IsEncrypted: true,
			SortOrder:   1,
		},
		{
			ConfigKey:   "log.level",
			ConfigName:  "日志级别",
			ConfigType:  "string",
			Description: "系统日志记录级别",
			Category:    "log",
			SortOrder:   1,
		},
		{
			ConfigKey:   "log.file",
			ConfigName:  "日志文件路径",
			ConfigType:  "string",
			Description: "日志文件存储路径",
			Category:    "log",
			SortOrder:   2,
		},
		{
			ConfigKey:   "log.max_size",
			ConfigName:  "日志文件最大大小(MB)",
			ConfigType:  "number",
			Description: "单个日志文件最大大小",
			Category:    "log",
			SortOrder:   3,
		},
		{
			ConfigKey:   "log.max_backups",
			ConfigName:  "日志备份文件数量",
			ConfigType:  "number",
			Description: "保留的日志备份文件数量",
			Category:    "log",
			SortOrder:   4,
		},
		{
			ConfigKey:   "log.max_age",
			ConfigName:  "日志保留天数",
			ConfigType:  "number",
			Description: "日志文件保留天数",
			Category:    "log",
			SortOrder:   5,
		},
		{
			ConfigKey:   "cors.allowed_origins",
			ConfigName:  "CORS允许的源",
			ConfigType:  "object",
			Description: "跨域请求允许的源地址列表",
			Category:    "cors",
			IsPublic:    true,
			SortOrder:   1,
		},
		{
			ConfigKey:   "cors.allowed_methods",
			ConfigName:  "CORS允许的方法",
			ConfigType:  "object",
			Description: "跨域请求允许的HTTP方法",
			Category:    "cors",
			IsPublic:    true,
			SortOrder:   2,
		},
		{
			ConfigKey:   "cors.allowed_headers",
			ConfigName:  "CORS允许的头部",
			ConfigType:  "object",
			Description: "跨域请求允许的HTTP头部",
			Category:    "cors",
			IsPublic:    true,
			SortOrder:   3,
		},
		{
			ConfigKey:   "rate_limit.enabled",
			ConfigName:  "启用限流",
			ConfigType:  "boolean",
			Description: "是否启用API限流功能",
			Category:    "rate_limit",
			SortOrder:   1,
		},
		{
			ConfigKey:   "rate_limit.requests_per_minute",
			ConfigName:  "每分钟请求限制",
			ConfigType:  "number",
			Description: "每分钟允许的最大请求数",
			Category:    "rate_limit",
			SortOrder:   2,
		},
		{
			ConfigKey:   "rate_limit.burst",
			ConfigName:  "突发请求限制",
			ConfigType:  "number",
			Description: "允许的突发请求数量",
			Category:    "rate_limit",
			SortOrder:   3,
		},
		{
			ConfigKey:   "upload.max_size",
			ConfigName:  "最大上传大小",
			ConfigType:  "number",
			Description: "文件上传最大大小(字节)",
			Category:    "upload",
			SortOrder:   1,
		},
		{
			ConfigKey:   "upload.allowed_types",
			ConfigName:  "允许的文件类型",
			ConfigType:  "object",
			Description: "允许上传的文件类型列表",
			Category:    "upload",
			SortOrder:   2,
		},
		{
			ConfigKey:   "payment.timeout",
			ConfigName:  "支付超时时间",
			ConfigType:  "number",
			Description: "支付订单超时时间(秒)",
			Category:    "payment",
			SortOrder:   1,
		},
		{
			ConfigKey:   "payment.alipay",
			ConfigName:  "支付宝配置",
			ConfigType:  "object",
			Description: "支付宝支付相关配置",
			Category:    "payment",
			IsEncrypted: true,
			SortOrder:   2,
		},
		{
			ConfigKey:   "payment.wechat",
			ConfigName:  "微信支付配置",
			ConfigType:  "object",
			Description: "微信支付相关配置",
			Category:    "payment",
			IsEncrypted: true,
			SortOrder:   3,
		},
		{
			ConfigKey:   "security.rate_limit",
			ConfigName:  "安全限流配置",
			ConfigType:  "object",
			Description: "登录等安全相关的限流配置",
			Category:    "security",
			SortOrder:   1,
		},
		{
			ConfigKey:   "security.ip_whitelist",
			ConfigName:  "IP白名单",
			ConfigType:  "object",
			Description: "允许访问的IP地址列表",
			Category:    "security",
			SortOrder:   2,
		},
		{
			ConfigKey:   "security.ip_blacklist",
			ConfigName:  "IP黑名单",
			ConfigType:  "object",
			Description: "禁止访问的IP地址列表",
			Category:    "security",
			SortOrder:   3,
		},
		{
			ConfigKey:   "security.max_login_attempts",
			ConfigName:  "最大登录尝试次数",
			ConfigType:  "number",
			Description: "账户锁定前的最大登录失败次数",
			Category:    "security",
			SortOrder:   4,
		},
		{
			ConfigKey:   "security.lockout_duration",
			ConfigName:  "账户锁定时长",
			ConfigType:  "number",
			Description: "账户锁定持续时间(秒)",
			Category:    "security",
			SortOrder:   5,
		},
		{
			ConfigKey:   "monitoring.enabled",
			ConfigName:  "启用监控",
			ConfigType:  "boolean",
			Description: "是否启用系统监控功能",
			Category:    "monitoring",
			SortOrder:   1,
		},
		{
			ConfigKey:   "monitoring.metrics_retention_days",
			ConfigName:  "指标保留天数",
			ConfigType:  "number",
			Description: "监控指标数据保留天数",
			Category:    "monitoring",
			SortOrder:   2,
		},
		{
			ConfigKey:   "monitoring.logs_retention_days",
			ConfigName:  "日志保留天数",
			ConfigType:  "number",
			Description: "监控日志保留天数",
			Category:    "monitoring",
			SortOrder:   3,
		},
		{
			ConfigKey:   "monitoring.health_check_interval",
			ConfigName:  "健康检查间隔",
			ConfigType:  "number",
			Description: "系统健康检查间隔时间(秒)",
			Category:    "monitoring",
			SortOrder:   4,
		},
		{
			ConfigKey:   "email.enabled",
			ConfigName:  "启用邮件",
			ConfigType:  "boolean",
			Description: "是否启用邮件发送功能",
			Category:    "email",
			SortOrder:   1,
		},
		{
			ConfigKey:   "email.host",
			ConfigName:  "SMTP服务器",
			ConfigType:  "string",
			Description: "邮件SMTP服务器地址",
			Category:    "email",
			SortOrder:   2,
		},
		{
			ConfigKey:   "email.port",
			ConfigName:  "SMTP端口",
			ConfigType:  "number",
			Description: "邮件SMTP服务器端口",
			Category:    "email",
			SortOrder:   3,
		},
		{
			ConfigKey:   "email.username",
			ConfigName:  "邮箱用户名",
			ConfigType:  "string",
			Description: "邮件发送账户用户名",
			Category:    "email",
			IsEncrypted: true,
			SortOrder:   4,
		},
		{
			ConfigKey:   "email.password",
			ConfigName:  "邮箱密码",
			ConfigType:  "string",
			Description: "邮件发送账户密码",
			Category:    "email",
			IsEncrypted: true,
			SortOrder:   5,
		},
		{
			ConfigKey:   "email.from",
			ConfigName:  "发送人邮箱",
			ConfigType:  "string",
			Description: "邮件发送人地址",
			Category:    "email",
			SortOrder:   6,
		},
		{
			ConfigKey:   "email.tls",
			ConfigName:  "启用TLS",
			ConfigType:  "boolean",
			Description: "是否启用TLS加密",
			Category:    "email",
			SortOrder:   7,
		},
		{
			ConfigKey:   "sms.enabled",
			ConfigName:  "启用短信",
			ConfigType:  "boolean",
			Description: "是否启用短信发送功能",
			Category:    "sms",
			SortOrder:   1,
		},
		{
			ConfigKey:   "sms.provider",
			ConfigName:  "短信服务商",
			ConfigType:  "string",
			Description: "短信服务提供商",
			Category:    "sms",
			SortOrder:   2,
		},
		{
			ConfigKey:   "sms.app_id",
			ConfigName:  "应用ID",
			ConfigType:  "string",
			Description: "短信服务应用ID",
			Category:    "sms",
			IsEncrypted: true,
			SortOrder:   3,
		},
		{
			ConfigKey:   "sms.app_secret",
			ConfigName:  "应用密钥",
			ConfigType:  "string",
			Description: "短信服务应用密钥",
			Category:    "sms",
			IsEncrypted: true,
			SortOrder:   4,
		},
		{
			ConfigKey:   "sms.sign_name",
			ConfigName:  "短信签名",
			ConfigType:  "string",
			Description: "短信发送签名",
			Category:    "sms",
			SortOrder:   5,
		},
	}

	// 为配置设置默认值
	for i := range configs {
		config := &configs[i]
		switch config.ConfigKey {
		case "v2bx.server_token":
			// 生成随机token
			if err := config.SetValue(s.generateRandomToken(32)); err != nil {
				// 如果生成失败，使用固定默认值
				config.SetValue("default-v2bx-token-" + s.generateRandomToken(16))
			}
		case "log.level":
			config.SetValue("info")
		case "log.file":
			config.SetValue("logs/anypanel.log")
		case "log.max_size":
			config.SetValue(100)
		case "log.max_backups":
			config.SetValue(7)
		case "log.max_age":
			config.SetValue(30)
		case "cors.allowed_origins":
			config.SetValue([]string{"*"})
		case "cors.allowed_methods":
			config.SetValue([]string{"GET", "POST", "PUT", "DELETE", "OPTIONS"})
		case "cors.allowed_headers":
			config.SetValue([]string{"*"})
		case "cors.allow_credentials":
			config.SetValue(true)
		case "rate_limit.enabled":
			config.SetValue(true)
		case "rate_limit.requests_per_minute":
			config.SetValue(60)
		case "rate_limit.burst":
			config.SetValue(10)
		case "upload.max_size":
			config.SetValue(10 * 1024 * 1024) // 10MB
		case "upload.allowed_types":
			config.SetValue([]string{"image/jpeg", "image/png", "image/gif", "text/plain"})
		case "upload.path":
			config.SetValue("uploads")
		case "payment.enabled":
			config.SetValue(false)
		case "payment.currency":
			config.SetValue("CNY")
		case "payment.callback_secret":
			config.SetValue(s.generateRandomToken(32))
		case "security.session_timeout":
			config.SetValue(3600)
		case "security.max_login_attempts":
			config.SetValue(5)
		case "security.lockout_duration":
			config.SetValue(300)
		case "security.password_min_length":
			config.SetValue(8)
		case "security.enable_2fa":
			config.SetValue(false)
		case "monitoring.enabled":
			config.SetValue(true)
		case "monitoring.retention_days":
			config.SetValue(30)
		case "monitoring.alert_thresholds":
			config.SetValue(map[string]interface{}{
				"cpu_usage":    80.0,
				"memory_usage": 85.0,
				"disk_usage":   90.0,
			})
		case "email.smtp_host":
			config.SetValue("")
		case "email.smtp_port":
			config.SetValue(587)
		case "email.smtp_username":
			config.SetValue("")
		case "email.smtp_password":
			config.SetValue("")
		case "email.smtp_encryption":
			config.SetValue("tls")
		case "email.from_address":
			config.SetValue("")
		case "email.from_name":
			config.SetValue("AnyPanel")
		case "sms.provider":
			config.SetValue("")
		case "sms.app_id":
			config.SetValue("")
		case "sms.app_key":
			config.SetValue("")
		case "sms.app_secret":
			config.SetValue("")
		case "sms.sign_name":
			config.SetValue("")
		default:
			// 对于没有指定默认值的配置，根据类型设置空值
			switch config.ConfigType {
			case "string":
				config.SetValue("")
			case "number":
				config.SetValue(0)
			case "boolean":
				config.SetValue(false)
			default:
				config.SetValue(map[string]interface{}{})
			}
		}
	}

	return configs
}

// generateRandomToken 生成随机token
func (s *SystemConfigService) generateRandomToken(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	bytes := make([]byte, length)
	source := mathRand.NewSource(time.Now().UnixNano())
	rng := mathRand.New(source)
	for i := range bytes {
		bytes[i] = charset[rng.Intn(len(charset))]
	}
	return string(bytes)
}

package api

import (
	"anypanel/internal/middleware"
	"anypanel/internal/service"
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SubscriptionAPI 订阅API处理器
type SubscriptionAPI struct {
	subscriptionConfigService *service.SubscriptionConfigService
	userService               *service.UserService
	subscriptionService       *service.SubscriptionService
}

// NewSubscriptionAPI 创建订阅API处理器
func NewSubscriptionAPI(db *gorm.DB) *SubscriptionAPI {
	nodeService := service.NewNodeService(db)
	userService := service.NewUserService(db)
	productService := service.NewProductService(db)
	subscriptionService := service.NewSubscriptionService(db, productService, userService, service.NewOrderService(db, productService, userService))
	subscriptionConfigService := service.NewSubscriptionConfigService(db, nodeService, userService, productService, subscriptionService)

	return &SubscriptionAPI{
		subscriptionConfigService: subscriptionConfigService,
		userService:               userService,
		subscriptionService:       subscriptionService,
	}
}

// SetupSubscriptionRoutes 设置订阅路由
func SetupSubscriptionRoutes(router *gin.RouterGroup, db *gorm.DB) {
	api := NewSubscriptionAPI(db)

	// 订阅相关路由
	subscription := router.Group("/subscription")
	{
		// 需要认证的路由
		subscription.Use(middleware.AuthMiddlewareCompat(db))
		{
			// 获取用户当前订阅信息
			subscription.GET("/info", api.GetUserSubscriptionInfo)

			// 获取用户订阅历史
			subscription.GET("/history", api.GetUserSubscriptionHistory)

			// 多客户端订阅配置下载
			subscription.GET("/clash", api.GetClashSubscription)
			subscription.GET("/clash-meta", api.GetClashMetaSubscription)
			subscription.GET("/sing-box", api.GetSingBoxSubscription)
			subscription.GET("/v2ray", api.GetV2RaySubscription)
			subscription.GET("/shadowrocket", api.GetShadowrocketSubscription)
			subscription.GET("/quantumult-x", api.GetQuantumultXSubscription)
			subscription.GET("/surge", api.GetSurgeSubscription)

			// 订阅链接管理
			subscription.GET("/urls", api.GetSubscriptionUrls)
			subscription.PUT("/regenerate-token", api.RegenerateSubscriptionToken)
		}
	}

	// 公开订阅路由（通过token访问）
	public := router.Group("/public")
	{
		// 通过token获取订阅（支持不同客户端类型）
		public.GET("/subscription/:token", api.GetPublicSubscription)
		public.GET("/subscription/:token/clash", api.GetPublicClashSubscription)
		public.GET("/subscription/:token/sing-box", api.GetPublicSingBoxSubscription)
		public.GET("/subscription/:token/v2ray", api.GetPublicV2RaySubscription)
	}
}

// GetUserSubscriptionInfo 获取用户订阅信息
func (api *SubscriptionAPI) GetUserSubscriptionInfo(c *gin.Context) {
	user, exists := middleware.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "用户未认证",
		})
		return
	}

	// 获取用户订阅统计
	stats, err := api.subscriptionService.GetUserSubscriptionStats(user.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取订阅信息失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取订阅信息成功",
		"data":    stats,
	})
}

// GetUserSubscriptionHistory 获取用户订阅历史
func (api *SubscriptionAPI) GetUserSubscriptionHistory(c *gin.Context) {
	user, exists := middleware.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "用户未认证",
		})
		return
	}

	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	req := &service.SubscriptionQueryRequest{
		Page:     page,
		PageSize: pageSize,
		UserID:   user.ID,
	}

	// 查询订阅历史
	response, err := api.subscriptionService.QuerySubscriptions(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取订阅历史失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取订阅历史成功",
		"data":    response,
	})
}

// GetClashSubscription 获取Clash订阅配置
func (api *SubscriptionAPI) GetClashSubscription(c *gin.Context) {
	api.getClientSubscription(c, service.ClientClash)
}

// GetClashMetaSubscription 获取Clash Meta订阅配置
func (api *SubscriptionAPI) GetClashMetaSubscription(c *gin.Context) {
	api.getClientSubscription(c, service.ClientClashMeta)
}

// GetSingBoxSubscription 获取Sing-box订阅配置
func (api *SubscriptionAPI) GetSingBoxSubscription(c *gin.Context) {
	api.getClientSubscription(c, service.ClientSingBox)
}

// GetV2RaySubscription 获取V2Ray订阅配置
func (api *SubscriptionAPI) GetV2RaySubscription(c *gin.Context) {
	api.getClientSubscription(c, service.ClientV2Ray)
}

// GetShadowrocketSubscription 获取Shadowrocket订阅配置
func (api *SubscriptionAPI) GetShadowrocketSubscription(c *gin.Context) {
	api.getClientSubscription(c, service.ClientShadowrocket)
}

// GetQuantumultXSubscription 获取Quantumult X订阅配置
func (api *SubscriptionAPI) GetQuantumultXSubscription(c *gin.Context) {
	api.getClientSubscription(c, service.ClientQuantumultX)
}

// GetSurgeSubscription 获取Surge订阅配置
func (api *SubscriptionAPI) GetSurgeSubscription(c *gin.Context) {
	api.getClientSubscription(c, service.ClientSurge)
}

// getClientSubscription 获取客户端订阅配置的通用方法
func (api *SubscriptionAPI) getClientSubscription(c *gin.Context, clientType service.ClientType) {
	user, exists := middleware.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "用户未认证",
		})
		return
	}

	// 生成订阅配置
	result, err := api.subscriptionConfigService.GenerateSubscription(user.UUID, clientType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "生成订阅配置失败",
			"error":   err.Error(),
		})
		return
	}

	// 设置响应头
	c.Header("Content-Type", result.ContentType)
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", result.Filename))

	// 如果内容为空，返回404
	if result.Content == "" {
		c.String(http.StatusNotFound, "No available nodes")
		return
	}

	c.String(http.StatusOK, result.Content)
}

// GetPublicSubscription 获取公开订阅（通用格式）
func (api *SubscriptionAPI) GetPublicSubscription(c *gin.Context) {
	token := c.Param("token")
	clientType := service.ClientGeneric // 默认使用通用格式

	// 支持通过查询参数指定客户端类型
	if client := c.Query("client"); client != "" {
		ct := service.ClientType(client)
		clientType = ct
	}

	api.getPublicSubscriptionWithToken(c, token, clientType)
}

// GetPublicClashSubscription 获取公开Clash订阅
func (api *SubscriptionAPI) GetPublicClashSubscription(c *gin.Context) {
	token := c.Param("token")
	api.getPublicSubscriptionWithToken(c, token, service.ClientClash)
}

// GetPublicSingBoxSubscription 获取公开Sing-box订阅
func (api *SubscriptionAPI) GetPublicSingBoxSubscription(c *gin.Context) {
	token := c.Param("token")
	api.getPublicSubscriptionWithToken(c, token, service.ClientSingBox)
}

// GetPublicV2RaySubscription 获取公开V2Ray订阅
func (api *SubscriptionAPI) GetPublicV2RaySubscription(c *gin.Context) {
	token := c.Param("token")
	api.getPublicSubscriptionWithToken(c, token, service.ClientV2Ray)
}

// getPublicSubscriptionWithToken 通过token获取订阅的通用方法
func (api *SubscriptionAPI) getPublicSubscriptionWithToken(c *gin.Context, token string, clientType service.ClientType) {
	// 记录访问日志
	clientIP := c.ClientIP()
	userAgent := c.GetHeader("User-Agent")

	// 验证token并获取用户信息
	user, err := api.subscriptionConfigService.ValidateSubscriptionToken(token)
	if err != nil {
		// 记录无效访问
		fmt.Printf("[订阅访问] 无效token: %s, IP: %s, UA: %s, 错误: %v\n", token, clientIP, userAgent, err)
		c.String(http.StatusNotFound, "Invalid subscription token")
		return
	}

	// 记录成功访问
	fmt.Printf("[订阅访问] 用户: %s, 客户端: %s, IP: %s, UA: %s\n", user.UUID, clientType, clientIP, userAgent)

	// 生成订阅配置（使用缓存版本）
	result, err := api.subscriptionConfigService.GenerateSubscriptionWithCache(user.UUID, clientType)
	if err != nil {
		fmt.Printf("[订阅生成失败] 用户: %s, 客户端: %s, 错误: %v\n", user.UUID, clientType, err)
		c.String(http.StatusInternalServerError, "Failed to generate subscription")
		return
	}

	// 设置响应头
	c.Header("Content-Type", result.ContentType)
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", result.Filename))
	c.Header("Subscription-Userinfo", fmt.Sprintf("upload=0; download=%d; total=%d; expire=%d",
		user.TrafficUsed, user.TrafficLimit, user.ExpiredAt.Unix()))

	// 如果内容为空，返回404（用户无权限或过期）
	if result.Content == "" {
		c.String(http.StatusNotFound, "No available nodes")
		return
	}

	c.String(http.StatusOK, result.Content)
}

// GetSubscriptionUrls 获取用户的订阅链接
func (api *SubscriptionAPI) GetSubscriptionUrls(c *gin.Context) {
	user, exists := middleware.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "用户未认证",
		})
		return
	}

	// 生成订阅token
	token, err := api.subscriptionConfigService.GenerateSubscriptionToken(user.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "生成订阅token失败",
			"error":   err.Error(),
		})
		return
	}

	// 构建订阅链接
	baseURL := c.Request.Host
	scheme := "http"
	if c.Request.TLS != nil {
		scheme = "https"
	}

	urls := map[string]interface{}{
		"base_url": fmt.Sprintf("%s://%s", scheme, baseURL),
		"token":    token,
		"urls": map[string]string{
			"clash":        fmt.Sprintf("%s://%s/api/v1/public/subscription/%s/clash", scheme, baseURL, token),
			"sing-box":     fmt.Sprintf("%s://%s/api/v1/public/subscription/%s/sing-box", scheme, baseURL, token),
			"v2ray":        fmt.Sprintf("%s://%s/api/v1/public/subscription/%s/v2ray", scheme, baseURL, token),
			"shadowrocket": fmt.Sprintf("%s://%s/api/v1/public/subscription/%s/v2ray", scheme, baseURL, token),
			"quantumult-x": fmt.Sprintf("%s://%s/api/v1/public/subscription/%s/v2ray", scheme, baseURL, token),
			"surge":        fmt.Sprintf("%s://%s/api/v1/public/subscription/%s/v2ray", scheme, baseURL, token),
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "获取订阅链接成功",
		"data":    urls,
	})
}

// RegenerateSubscriptionToken 重新生成订阅token
func (api *SubscriptionAPI) RegenerateSubscriptionToken(c *gin.Context) {
	user, exists := middleware.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"message": "用户未认证",
		})
		return
	}

	// 重新生成token（目前直接返回UUID，后续可以实现更复杂的token生成逻辑）
	token, err := api.subscriptionConfigService.GenerateSubscriptionToken(user.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "重新生成订阅token失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "重新生成订阅token成功",
		"data": map[string]interface{}{
			"token": token,
		},
	})
}

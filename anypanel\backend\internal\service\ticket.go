package service

import (
	"anypanel/internal/model"
	"fmt"
	"math/rand"
	"time"

	"gorm.io/gorm"
)

type TicketService struct {
	db *gorm.DB
}

func NewTicketService(db *gorm.DB) *TicketService {
	return &TicketService{
		db: db,
	}
}

// TicketQueryRequest 工单查询请求
type TicketQueryRequest struct {
	Page       int    `form:"page" binding:"min=1"`
	PageSize   int    `form:"page_size" binding:"min=1,max=100"`
	UserID     *uint  `form:"user_id"`
	Status     string `form:"status"`
	Priority   string `form:"priority"`
	Category   string `form:"category"`
	AssignedTo *uint  `form:"assigned_to"`
	Search     string `form:"search"`
	SortBy     string `form:"sort_by"`
	SortOrder  string `form:"sort_order"`
}

// TicketQueryResponse 工单查询响应
type TicketQueryResponse struct {
	Total   int64        `json:"total"`
	Tickets []*TicketDTO `json:"tickets"`
}

// TicketDTO 工单数据传输对象
type TicketDTO struct {
	ID           uint              `json:"id"`
	TicketNo     string            `json:"ticket_no"`
	UserID       uint              `json:"user_id"`
	Username     string            `json:"username"`
	Subject      string            `json:"subject"`
	Content      string            `json:"content"`
	Priority     string            `json:"priority"`
	Status       string            `json:"status"`
	Category     string            `json:"category"`
	AssignedTo   *uint             `json:"assigned_to"`
	AssigneeName *string           `json:"assignee_name"`
	LastReplyAt  *time.Time        `json:"last_reply_at"`
	ResolvedAt   *time.Time        `json:"resolved_at"`
	CreatedAt    time.Time         `json:"created_at"`
	UpdatedAt    time.Time         `json:"updated_at"`
	Replies      []*TicketReplyDTO `json:"replies,omitempty"`
}

// TicketReplyDTO 工单回复数据传输对象
type TicketReplyDTO struct {
	ID        uint      `json:"id"`
	TicketID  uint      `json:"ticket_id"`
	UserID    uint      `json:"user_id"`
	Username  string    `json:"username"`
	Content   string    `json:"content"`
	IsStaff   bool      `json:"is_staff"`
	CreatedAt time.Time `json:"created_at"`
}

// TicketCreateRequest 工单创建请求
type TicketCreateRequest struct {
	Subject  string `json:"subject" binding:"required,max=255"`
	Content  string `json:"content" binding:"required"`
	Priority string `json:"priority" binding:"oneof=low medium high urgent"`
	Category string `json:"category" binding:"required,max=100"`
}

// TicketReplyRequest 工单回复请求
type TicketReplyRequest struct {
	Content string `json:"content" binding:"required"`
}

// TicketUpdateRequest 工单更新请求（管理员用）
type TicketUpdateRequest struct {
	Status     *string `json:"status" binding:"omitempty,oneof=open in_progress pending resolved closed"`
	Priority   *string `json:"priority" binding:"omitempty,oneof=low medium high urgent"`
	AssignedTo *uint   `json:"assigned_to"`
}

// GetTickets 获取工单列表
func (s *TicketService) GetTickets(req *TicketQueryRequest) (*TicketQueryResponse, error) {
	query := s.db.Model(&model.Ticket{})

	// 用户筛选
	if req.UserID != nil {
		query = query.Where("user_id = ?", *req.UserID)
	}

	// 状态筛选
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 优先级筛选
	if req.Priority != "" {
		query = query.Where("priority = ?", req.Priority)
	}

	// 分类筛选
	if req.Category != "" {
		query = query.Where("category = ?", req.Category)
	}

	// 指派人筛选
	if req.AssignedTo != nil {
		query = query.Where("assigned_to = ?", *req.AssignedTo)
	}

	// 搜索条件
	if req.Search != "" {
		searchPattern := "%" + req.Search + "%"
		query = query.Where("ticket_no LIKE ? OR subject LIKE ? OR content LIKE ?",
			searchPattern, searchPattern, searchPattern)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}

	// 排序
	sortBy := req.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}
	sortOrder := req.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// 分页
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// 预加载用户和指派人信息
	var tickets []model.Ticket
	if err := query.Preload("User").Preload("Assignee").Find(&tickets).Error; err != nil {
		return nil, err
	}

	// 转换为DTO
	ticketDTOs := make([]*TicketDTO, len(tickets))
	for i, ticket := range tickets {
		ticketDTOs[i] = s.toTicketDTO(&ticket, false)
	}

	return &TicketQueryResponse{
		Total:   total,
		Tickets: ticketDTOs,
	}, nil
}

// GetTicketByID 根据ID获取工单详情
func (s *TicketService) GetTicketByID(id uint, includeReplies bool) (*TicketDTO, error) {
	var ticket model.Ticket
	query := s.db.Preload("User").Preload("Assignee")

	if includeReplies {
		query = query.Preload("Replies.User")
	}

	if err := query.First(&ticket, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("工单不存在")
		}
		return nil, err
	}

	return s.toTicketDTO(&ticket, includeReplies), nil
}

// GetUserTickets 获取用户的工单列表
func (s *TicketService) GetUserTickets(userID uint, req *TicketQueryRequest) (*TicketQueryResponse, error) {
	req.UserID = &userID
	return s.GetTickets(req)
}

// CreateTicket 创建工单
func (s *TicketService) CreateTicket(userID uint, req *TicketCreateRequest) (*TicketDTO, error) {
	// 生成工单号
	ticketNo := s.generateTicketNo()

	ticket := &model.Ticket{
		TicketNo: ticketNo,
		UserID:   userID,
		Subject:  req.Subject,
		Content:  req.Content,
		Priority: req.Priority,
		Category: req.Category,
		Status:   "open",
	}

	if err := ticket.Validate(); err != nil {
		return nil, err
	}

	if err := s.db.Create(ticket).Error; err != nil {
		return nil, err
	}

	// 重新加载包含用户信息
	if err := s.db.Preload("User").First(ticket, ticket.ID).Error; err != nil {
		return nil, err
	}

	return s.toTicketDTO(ticket, false), nil
}

// ReplyTicket 回复工单
func (s *TicketService) ReplyTicket(ticketID, userID uint, req *TicketReplyRequest, isStaff bool) (*TicketReplyDTO, error) {
	// 检查工单是否存在
	var ticket model.Ticket
	if err := s.db.First(&ticket, ticketID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("工单不存在")
		}
		return nil, err
	}

	// 检查权限（用户只能回复自己的工单，或者是管理员）
	if !isStaff && ticket.UserID != userID {
		return nil, fmt.Errorf("没有权限回复此工单")
	}

	// 创建回复
	reply := &model.TicketReply{
		TicketID: ticketID,
		UserID:   userID,
		Content:  req.Content,
		IsStaff:  isStaff,
	}

	if err := reply.Validate(); err != nil {
		return nil, err
	}

	if err := s.db.Create(reply).Error; err != nil {
		return nil, err
	}

	// 更新工单的最后回复时间
	now := time.Now()
	if err := s.db.Model(&ticket).Updates(map[string]interface{}{
		"last_reply_at": &now,
		"status":        "pending", // 有新回复时，状态改为待处理
	}).Error; err != nil {
		// 记录错误但不影响主流程
		// log.Printf("更新工单最后回复时间失败: %v", err)
	}

	// 重新加载包含用户信息
	if err := s.db.Preload("User").First(reply, reply.ID).Error; err != nil {
		return nil, err
	}

	return s.toTicketReplyDTO(reply), nil
}

// UpdateTicket 更新工单（管理员用）
func (s *TicketService) UpdateTicket(id uint, req *TicketUpdateRequest) (*TicketDTO, error) {
	var ticket model.Ticket
	if err := s.db.First(&ticket, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("工单不存在")
		}
		return nil, err
	}

	// 更新字段
	updates := make(map[string]interface{})

	if req.Status != nil {
		updates["status"] = *req.Status
		// 如果状态改为已解决，记录解决时间
		if *req.Status == "resolved" && ticket.ResolvedAt == nil {
			now := time.Now()
			updates["resolved_at"] = &now
		}
	}

	if req.Priority != nil {
		updates["priority"] = *req.Priority
	}

	if req.AssignedTo != nil {
		updates["assigned_to"] = *req.AssignedTo
	}

	if len(updates) > 0 {
		if err := s.db.Model(&ticket).Updates(updates).Error; err != nil {
			return nil, err
		}
	}

	// 重新加载包含用户和指派人信息
	if err := s.db.Preload("User").Preload("Assignee").First(&ticket, ticket.ID).Error; err != nil {
		return nil, err
	}

	return s.toTicketDTO(&ticket, false), nil
}

// CloseTicket 关闭工单
func (s *TicketService) CloseTicket(id uint, userID uint, isStaff bool) error {
	var ticket model.Ticket
	if err := s.db.First(&ticket, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("工单不存在")
		}
		return err
	}

	// 检查权限（用户只能关闭自己的工单，或者是管理员）
	if !isStaff && ticket.UserID != userID {
		return fmt.Errorf("没有权限关闭此工单")
	}

	return s.db.Model(&ticket).Updates(map[string]interface{}{
		"status":      "closed",
		"resolved_at": time.Now(),
	}).Error
}

// GetTicketStats 获取工单统计信息
func (s *TicketService) GetTicketStats(userID *uint) (map[string]interface{}, error) {
	query := s.db.Model(&model.Ticket{})

	// 如果指定用户ID，只统计该用户的工单
	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}

	stats := make(map[string]interface{})

	// 总工单数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}
	stats["total"] = total

	// 按状态统计
	var statusStats []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}
	if err := query.Select("status, COUNT(*) as count").Group("status").Scan(&statusStats).Error; err != nil {
		return nil, err
	}
	stats["by_status"] = statusStats

	// 按优先级统计
	var priorityStats []struct {
		Priority string `json:"priority"`
		Count    int64  `json:"count"`
	}
	if err := query.Select("priority, COUNT(*) as count").Group("priority").Scan(&priorityStats).Error; err != nil {
		return nil, err
	}
	stats["by_priority"] = priorityStats

	// 按分类统计
	var categoryStats []struct {
		Category string `json:"category"`
		Count    int64  `json:"count"`
	}
	if err := query.Select("category, COUNT(*) as count").Group("category").Scan(&categoryStats).Error; err != nil {
		return nil, err
	}
	stats["by_category"] = categoryStats

	return stats, nil
}

// generateTicketNo 生成工单号
func (s *TicketService) generateTicketNo() string {
	// 格式: TK + 年月日 + 4位随机数
	now := time.Now()
	dateStr := now.Format("20060102")
	randNum := rand.Intn(9999)
	return fmt.Sprintf("TK%s%04d", dateStr, randNum)
}

// 辅助函数
func (s *TicketService) toTicketDTO(ticket *model.Ticket, includeReplies bool) *TicketDTO {
	dto := &TicketDTO{
		ID:          ticket.ID,
		TicketNo:    ticket.TicketNo,
		UserID:      ticket.UserID,
		Subject:     ticket.Subject,
		Content:     ticket.Content,
		Priority:    ticket.Priority,
		Status:      ticket.Status,
		Category:    ticket.Category,
		AssignedTo:  ticket.AssignedTo,
		LastReplyAt: ticket.LastReplyAt,
		ResolvedAt:  ticket.ResolvedAt,
		CreatedAt:   ticket.CreatedAt,
		UpdatedAt:   ticket.UpdatedAt,
	}

	// 添加用户信息
	if ticket.User.ID != 0 {
		dto.Username = ticket.User.Username
	}

	// 添加指派人信息
	if ticket.Assignee != nil && ticket.Assignee.ID != 0 {
		dto.AssigneeName = &ticket.Assignee.Username
	}

	// 添加回复信息
	if includeReplies {
		replies := make([]*TicketReplyDTO, len(ticket.Replies))
		for i, reply := range ticket.Replies {
			replies[i] = s.toTicketReplyDTO(&reply)
		}
		dto.Replies = replies
	}

	return dto
}

func (s *TicketService) toTicketReplyDTO(reply *model.TicketReply) *TicketReplyDTO {
	dto := &TicketReplyDTO{
		ID:        reply.ID,
		TicketID:  reply.TicketID,
		UserID:    reply.UserID,
		Content:   reply.Content,
		IsStaff:   reply.IsStaff,
		CreatedAt: reply.CreatedAt,
	}

	// 添加用户信息
	if reply.User.ID != 0 {
		dto.Username = reply.User.Username
	}

	return dto
}

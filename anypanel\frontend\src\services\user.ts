import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import apiClient, { PaginatedResponse, PaginationParams } from './api';

// 用户数据类型
export interface User {
  id: number;
  username: string;
  email: string;
  role: 'admin' | 'user';
  status: 'active' | 'inactive' | 'expired';
  traffic_limit: number; // 字节
  traffic_used: number; // 字节
  device_limit: number;
  speed_limit: number; // Mbps，0表示不限速
  expired_at: string | null;
  created_at: string;
}

// 用户创建表单类型
export interface UserForm {
  username: string;
  email: string;
  password: string;
  role: 'admin' | 'user';
  status: 'active' | 'inactive' | 'expired';
  traffic_limit: number;
  device_limit: number;
  speed_limit: number;
  expired_at?: string;
}

// 用户更新表单类型（部分字段可选）
export interface UserUpdateForm {
  username?: string;
  email?: string;
  role?: 'admin' | 'user';
  status?: 'active' | 'inactive' | 'expired';
  traffic_limit?: number;
  device_limit?: number;
  speed_limit?: number;
  expired_at?: string;
}

// 用户查询参数
export interface UserQueryParams extends PaginationParams {
  search?: string;
  role?: string;
  status?: string;
  sort_by?: string;
  sort_order?: string;
}

// 用户统计数据
export interface UserStats {
  total_users: number;
  active_users: number;
  inactive_users: number;
  expired_users: number;
  admin_users: number;
  total_traffic_used: number;
  total_traffic_limit: number;
  avg_traffic_usage: number;
  new_users_today: number;
  new_users_this_month: number;
}

// 用户流量统计
export interface UserTrafficStats {
  daily_usage: Array<{
    date: string;
    upload: number;
    download: number;
    total: number;
  }>;
  total_usage: {
    upload: number;
    download: number;
    total: number;
  };
  usage_percentage: number;
  remaining_traffic: number;
}

// 用户订阅信息
export interface UserSubscription {
  id: number;
  user_id: number;
  product_id: number;
  product_name: string;
  status: 'active' | 'inactive' | 'expired';
  started_at: string;
  expired_at: string;
  is_expired: boolean;
  days_left: number;
  traffic_used: number;
  traffic_limit: number;
}

class UserService {
  // 获取用户列表
  async getUsers(params: UserQueryParams): Promise<PaginatedResponse<User>> {
    // 转换分页参数以匹配后端期望的格式
    const backendParams = {
      ...params,
      page: params.current || 1,
      page_size: params.pageSize || 20,
    };
    // 删除前端的分页参数，避免冲突
    delete backendParams.current;
    delete backendParams.pageSize;

    const response = await apiClient.get('/user/users/admin', backendParams);

    // 转换后端响应格式为前端期望的分页格式
    return {
      list: response.users || [],  // 后端返回的是 "users" 字段
      total: response.total || 0,
      current: params.current || 1,
      pageSize: params.pageSize || 20
    };
  }

  // 获取所有用户（不分页）
  async getAllUsers(): Promise<User[]> {
    return apiClient.get('/user/users/admin/all');
  }

  // 获取单个用户详情
  async getUser(id: number): Promise<User> {
    return apiClient.get(`/user/users/admin/${id}`);
  }

  // 创建用户
  async createUser(data: UserForm): Promise<User> {
    return apiClient.post('/user/users/admin', data);
  }

  // 更新用户
  async updateUser(id: number, data: UserUpdateForm): Promise<User> {
    return apiClient.put(`/user/users/admin/${id}`, data);
  }

  // 删除用户
  async deleteUser(id: number): Promise<void> {
    return apiClient.delete(`/user/users/admin/${id}`);
  }

  // 更新用户状态
  async updateUserStatus(id: number, status: 'active' | 'inactive' | 'expired'): Promise<User> {
    return apiClient.put(`/user/users/admin/${id}/status`, { status });
  }

  // 重置用户密码
  async resetUserPassword(id: number, newPassword: string): Promise<void> {
    return apiClient.post(`/user/users/admin/${id}/reset-password`, { password: newPassword });
  }

  // 获取用户流量统计
  async getUserTrafficStats(id: number): Promise<UserTrafficStats> {
    return apiClient.get(`/user/users/admin/${id}/traffic-stats`);
  }

  // 获取活跃用户
  async getActiveUsers(): Promise<User[]> {
    return apiClient.get('/user/users/admin/active');
  }

  // 获取过期用户
  async getExpiredUsers(): Promise<User[]> {
    return apiClient.get('/user/users/admin/expired');
  }

  // 批量更新用户
  async batchUpdateUsers(userIds: number[], data: UserUpdateForm): Promise<void> {
    return apiClient.post('/user/users/admin/batch', {
      user_ids: userIds,
      ...data,
    });
  }

  // 批量删除用户（需要循环调用单个删除API）
  async batchDeleteUsers(ids: number[]): Promise<void> {
    await Promise.all(ids.map(id => this.deleteUser(id)));
  }

  // 获取用户统计数据（可能需要从管理员统计API获取）
  async getUserStats(): Promise<UserStats> {
    return apiClient.get('/admin/system/user-stats');
  }

  // 格式化流量显示
  formatTraffic(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(unitIndex > 0 ? 2 : 0)} ${units[unitIndex]}`;
  }

  // 计算流量使用百分比
  getTrafficUsagePercentage(used: number, limit: number): number {
    if (limit === 0) return 0;
    return Math.min((used / limit) * 100, 100);
  }

  // 格式化速度显示
  formatSpeed(speedMbps: number): string {
    if (speedMbps === 0) {
      return '不限速';
    }
    return `${speedMbps} Mbps`;
  }

  // 获取状态显示文本
  getStatusText(status: string): string {
    const statusMap: Record<string, string> = {
      active: '正常',
      inactive: '禁用',
      expired: '过期',
    };
    return statusMap[status] || status;
  }

  // 获取状态颜色
  getStatusColor(status: string): string {
    const colorMap: Record<string, string> = {
      active: 'success',
      inactive: 'warning',
      expired: 'error',
    };
    return colorMap[status] || 'default';
  }

  // 获取角色显示文本
  getRoleText(role: string): string {
    const roleMap: Record<string, string> = {
      admin: '管理员',
      user: '普通用户',
    };
    return roleMap[role] || role;
  }

  // 获取角色颜色
  getRoleColor(role: string): string {
    const colorMap: Record<string, string> = {
      admin: 'red',
      user: 'blue',
    };
    return colorMap[role] || 'default';
  }

  // 判断用户是否即将过期
  isUserExpiringSoon(expiredAt: string | null): boolean {
    if (!expiredAt) return false;
    const expireDate = new Date(expiredAt);
    const now = new Date();
    const daysLeft = Math.ceil((expireDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return daysLeft <= 7 && daysLeft > 0;
  }

  // 获取用户剩余天数
  getUserDaysLeft(expiredAt: string | null): number {
    if (!expiredAt) return -1;
    const expireDate = new Date(expiredAt);
    const now = new Date();
    return Math.ceil((expireDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
  }

  // 格式化过期时间显示
  formatExpiredAt(expiredAt: string | null): string {
    if (!expiredAt) return '永不过期';
    const daysLeft = this.getUserDaysLeft(expiredAt);
    if (daysLeft < 0) return '已过期';
    if (daysLeft === 0) return '今天过期';
    return `${daysLeft}天后过期`;
  }
}

export const userService = new UserService();

// React Query Hooks
export const useUsers = (params: UserQueryParams) => {
  return useQuery({
    queryKey: ['users', params],
    queryFn: () => userService.getUsers(params),
    keepPreviousData: true,
  });
};

export const useUser = (id: number) => {
  return useQuery({
    queryKey: ['user', id],
    queryFn: () => userService.getUser(id),
    enabled: !!id,
  });
};

export const useUserStats = () => {
  return useQuery({
    queryKey: ['user-stats'],
    queryFn: () => userService.getUserStats(),
    refetchInterval: 5 * 60 * 1000, // 5分钟刷新
  });
};

export const useUserTrafficStats = (id: number) => {
  return useQuery({
    queryKey: ['user-traffic-stats', id],
    queryFn: () => userService.getUserTrafficStats(id),
    enabled: !!id,
    refetchInterval: 2 * 60 * 1000, // 2分钟刷新
  });
};

export const useActiveUsers = () => {
  return useQuery({
    queryKey: ['active-users'],
    queryFn: () => userService.getActiveUsers(),
    staleTime: 2 * 60 * 1000, // 2分钟
  });
};

export const useExpiredUsers = () => {
  return useQuery({
    queryKey: ['expired-users'],
    queryFn: () => userService.getExpiredUsers(),
    staleTime: 2 * 60 * 1000, // 2分钟
  });
};

// Mutations
export const useCreateUser = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: UserForm) => userService.createUser(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['user-stats'] });
      message.success('用户创建成功');
    },
    onError: () => {
      message.error('用户创建失败');
    },
  });
};

export const useUpdateUser = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UserUpdateForm }) => 
      userService.updateUser(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['user'] });
      queryClient.invalidateQueries({ queryKey: ['user-stats'] });
      message.success('用户更新成功');
    },
    onError: () => {
      message.error('用户更新失败');
    },
  });
};

export const useDeleteUser = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: number) => userService.deleteUser(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['user-stats'] });
      message.success('用户删除成功');
    },
    onError: () => {
      message.error('用户删除失败');
    },
  });
};

export const useBatchDeleteUsers = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (ids: number[]) => userService.batchDeleteUsers(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['user-stats'] });
      message.success('批量删除成功');
    },
    onError: () => {
      message.error('批量删除失败');
    },
  });
};

export const useUpdateUserStatus = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, status }: { id: number; status: 'active' | 'inactive' | 'expired' }) => 
      userService.updateUserStatus(id, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['user'] });
      message.success('用户状态更新成功');
    },
    onError: () => {
      message.error('用户状态更新失败');
    },
  });
};

export const useResetUserPassword = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, password }: { id: number; password: string }) => 
      userService.resetUserPassword(id, password),
    onSuccess: () => {
      message.success('密码重置成功');
    },
    onError: () => {
      message.error('密码重置失败');
    },
  });
};

export const useBatchUpdateUsers = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ ids, data }: { ids: number[]; data: UserUpdateForm }) => 
      userService.batchUpdateUsers(ids, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['user-stats'] });
      message.success('批量更新成功');
    },
    onError: () => {
      message.error('批量更新失败');
    },
  });
};

// 获取所有用户（不分页，用于下拉选择）
export const useAllUsers = () => {
  return useQuery({
    queryKey: ['all-users'],
    queryFn: () => userService.getAllUsers(),
    staleTime: 5 * 60 * 1000, // 5分钟
  });
};

export default userService;
package service

import (
	"anypanel/internal/model"
	"fmt"
	"log"
	"time"

	"github.com/robfig/cron/v3"
	"gorm.io/gorm"
)

// TaskScheduler 定时任务调度器
type TaskScheduler struct {
	db                  *gorm.DB
	userService         *UserService
	nodeService         *NodeService
	trafficService      *TrafficService
	onlineUserService   *OnlineUserService
	subscriptionService *SubscriptionService
	cron                *cron.Cron
	isRunning           bool
}

// NewTaskScheduler 创建新的定时任务调度器
func NewTaskScheduler(db *gorm.DB) *TaskScheduler {
	userService := NewUserService(db)
	nodeService := NewNodeService(db)
	productService := NewProductService(db)
	orderService := NewOrderService(db, productService, userService)
	trafficService := NewTrafficService(db, nil, nil, nil)
	onlineUserService := NewOnlineUserService(db, nil, nil, nil)
	subscriptionService := NewSubscriptionService(db, productService, userService, orderService)

	return &TaskScheduler{
		db:                  db,
		userService:         userService,
		nodeService:         nodeService,
		trafficService:      trafficService,
		onlineUserService:   onlineUserService,
		subscriptionService: subscriptionService,
		cron:                cron.New(cron.WithSeconds()), // 支持秒级定时任务
		isRunning:           false,
	}
}

// Start 启动定时任务调度器
func (s *TaskScheduler) Start() error {
	if s.isRunning {
		return nil
	}

	// 注册定时任务
	if err := s.registerTasks(); err != nil {
		return err
	}

	s.cron.Start()
	s.isRunning = true
	log.Println("定时任务调度器已启动")

	return nil
}

// Stop 停止定时任务调度器
func (s *TaskScheduler) Stop() {
	if !s.isRunning {
		return
	}

	ctx := s.cron.Stop()
	<-ctx.Done()
	s.isRunning = false
	log.Println("定时任务调度器已停止")
}

// registerTasks 注册所有定时任务
func (s *TaskScheduler) registerTasks() error {
	// 每分钟清理过期在线用户
	if _, err := s.cron.AddFunc("@every 1m", s.cleanupExpiredOnlineUsers); err != nil {
		return err
	}

	// 每5分钟检查流量超限用户
	if _, err := s.cron.AddFunc("@every 5m", s.checkTrafficLimits); err != nil {
		return err
	}

	// 每小时聚合流量数据
	if _, err := s.cron.AddFunc("@every 1h", s.aggregateTrafficData); err != nil {
		return err
	}

	// 每天凌晨2点清理过期流量记录
	if _, err := s.cron.AddFunc("0 0 2 * * *", s.cleanupExpiredTrafficLogs); err != nil {
		return err
	}

	// 每天凌晨3点检查过期订阅
	if _, err := s.cron.AddFunc("0 0 3 * * *", s.checkExpiredSubscriptions); err != nil {
		return err
	}

	// 每30分钟执行节点健康检查
	if _, err := s.cron.AddFunc("@every 30m", s.performNodeHealthChecks); err != nil {
		return err
	}

	// 每天凌晨1点生成统计报表
	if _, err := s.cron.AddFunc("0 0 1 * * *", s.generateDailyReports); err != nil {
		return err
	}

	log.Println("定时任务注册完成")
	return nil
}

// cleanupExpiredOnlineUsers 清理过期在线用户
func (s *TaskScheduler) cleanupExpiredOnlineUsers() {
	log.Println("开始执行清理过期在线用户任务")

	// 使用增强版的清理功能
	if err := s.onlineUserService.AutoCleanupExpiredOnlineUsers(); err != nil {
		log.Printf("清理过期在线用户失败: %v", err)
		return
	}
}

// checkTrafficLimits 检查流量超限用户
func (s *TaskScheduler) checkTrafficLimits() {
	log.Println("开始执行流量超限检查任务")

	// 使用增强版的流量超限处理功能
	if err := s.trafficService.AutoHandleTrafficLimitExceeded(); err != nil {
		log.Printf("流量超限检查失败: %v", err)
		return
	}
}

// aggregateTrafficData 聚合流量数据
func (s *TaskScheduler) aggregateTrafficData() {
	log.Println("开始执行流量数据聚合任务")

	// 聚合过去1小时的流量数据
	endTime := time.Now()
	startTime := endTime.Add(-1 * time.Hour)

	// 使用增强版的流量聚合功能
	if err := s.trafficService.AggregateTrafficData(startTime, endTime); err != nil {
		log.Printf("聚合流量数据失败: %v", err)
		return
	}
}

// cleanupExpiredTrafficLogs 清理过期流量记录
func (s *TaskScheduler) cleanupExpiredTrafficLogs() {
	log.Println("开始执行清理过期流量记录任务")

	// 使用增强版的流量清理功能（保留90天）
	if err := s.trafficService.CleanupExpiredTrafficLogs(90); err != nil {
		log.Printf("清理过期流量记录失败: %v", err)
		return
	}
}

// checkExpiredSubscriptions 检查过期订阅
func (s *TaskScheduler) checkExpiredSubscriptions() {
	log.Println("开始执行过期订阅检查任务")

	// 使用订阅服务的检查方法
	if err := s.subscriptionService.CheckExpiredSubscriptions(); err != nil {
		log.Printf("过期订阅检查失败: %v", err)
		return
	}
}

// performNodeHealthChecks 执行节点健康检查
func (s *TaskScheduler) performNodeHealthChecks() {
	log.Println("开始执行节点健康检查任务")

	nodes, err := s.nodeService.GetAllNodes()
	if err != nil {
		log.Printf("获取节点列表失败: %v", err)
		return
	}

	var healthyNodes int
	var unhealthyNodes int

	for _, node := range nodes {
		result, err := s.nodeService.HealthCheck(node.ID)
		if err != nil {
			log.Printf("节点 %d 健康检查失败: %v", node.ID, err)
			unhealthyNodes++
			continue
		}

		if result.IsHealthy {
			healthyNodes++
		} else {
			unhealthyNodes++
		}

		// 如果节点状态与实际健康状态不符，更新状态
		expectedStatus := "online"
		if !result.IsHealthy {
			expectedStatus = "offline"
		}

		if node.Status != expectedStatus {
			s.nodeService.UpdateNodeStatus(node.ID, expectedStatus)
		}
	}

	log.Printf("节点健康检查完成，健康节点: %d，不健康节点: %d", healthyNodes, unhealthyNodes)
}

// generateDailyReports 生成每日统计报表
func (s *TaskScheduler) generateDailyReports() {
	log.Println("开始执行每日报表生成任务")

	// 这里可以生成各种统计报表
	// 例如：用户增长报表、流量统计报表、收入报表等

	// 生成用户统计
	userStats, err := s.generateUserStats()
	if err != nil {
		log.Printf("生成用户统计失败: %v", err)
	} else {
		log.Printf("用户统计生成完成: %+v", userStats)
	}

	// 生成流量统计
	trafficStats, err := s.generateTrafficStats()
	if err != nil {
		log.Printf("生成流量统计失败: %v", err)
	} else {
		log.Printf("流量统计生成完成: %+v", trafficStats)
	}

	log.Println("每日报表生成任务完成")
}

// 辅助方法

// userHasActiveSubscription 检查用户是否有活跃订阅
func (s *TaskScheduler) userHasActiveSubscription(userID uint) bool {
	var count int64
	if err := s.db.Model(&model.UserSubscription{}).
		Where("user_id = ? AND status = ? AND expired_at > ?", userID, "active", time.Now()).
		Count(&count).Error; err != nil {
		return false
	}
	return count > 0
}

// logTrafficLimitExceeded 记录流量超限日志
func (s *TaskScheduler) logTrafficLimitExceeded(user *model.User) {
	// 这里可以记录到专门的日志表中
	log.Printf("用户 %s (ID: %d) 流量超限: 使用 %d / 限制 %d",
		user.Username, user.ID, user.TrafficUsed, user.TrafficLimit)
}

// generateUserStats 生成用户统计
func (s *TaskScheduler) generateUserStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总用户数
	var totalUsers int64
	if err := s.db.Model(&model.User{}).Count(&totalUsers).Error; err != nil {
		return nil, err
	}
	stats["total_users"] = totalUsers

	// 活跃用户数
	var activeUsers int64
	if err := s.db.Model(&model.User{}).Where("status = ?", "active").Count(&activeUsers).Error; err != nil {
		return nil, err
	}
	stats["active_users"] = activeUsers

	// 过期用户数
	var expiredUsers int64
	if err := s.db.Model(&model.User{}).Where("status = ? OR (expired_at IS NOT NULL AND expired_at < NOW())", "expired").Count(&expiredUsers).Error; err != nil {
		return nil, err
	}
	stats["expired_users"] = expiredUsers

	return stats, nil
}

// generateTrafficStats 生成流量统计
func (s *TaskScheduler) generateTrafficStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 今日流量
	var todayTraffic int64
	if err := s.db.Model(&model.TrafficLog{}).
		Where("DATE(recorded_at) = CURDATE()").
		Select("COALESCE(SUM(upload + download), 0)").
		Scan(&todayTraffic).Error; err != nil {
		return nil, err
	}
	stats["today_traffic"] = todayTraffic

	// 本月流量
	var monthTraffic int64
	if err := s.db.Model(&model.TrafficLog{}).
		Where("MONTH(recorded_at) = MONTH(NOW()) AND YEAR(recorded_at) = YEAR(NOW())").
		Select("COALESCE(SUM(upload + download), 0)").
		Scan(&monthTraffic).Error; err != nil {
		return nil, err
	}
	stats["month_traffic"] = monthTraffic

	// 总流量记录数
	var totalRecords int64
	if err := s.db.Model(&model.TrafficLog{}).Count(&totalRecords).Error; err != nil {
		return nil, err
	}
	stats["total_records"] = totalRecords

	return stats, nil
}

// GetSchedulerStatus 获取调度器状态
func (s *TaskScheduler) GetSchedulerStatus() map[string]interface{} {
	entries := s.cron.Entries()

	taskList := make([]map[string]interface{}, len(entries))
	for i, entry := range entries {
		taskList[i] = map[string]interface{}{
			"schedule": entry.Schedule,
			"next_run": entry.Next,
			"prev_run": entry.Prev,
		}
	}

	return map[string]interface{}{
		"is_running": s.isRunning,
		"tasks":      taskList,
		"task_count": len(entries),
	}
}

// RunTaskNow 立即运行指定任务
func (s *TaskScheduler) RunTaskNow(taskName string) error {
	switch taskName {
	case "cleanup_expired_online_users":
		s.cleanupExpiredOnlineUsers()
	case "check_traffic_limits":
		s.checkTrafficLimits()
	case "aggregate_traffic_data":
		s.aggregateTrafficData()
	case "cleanup_expired_traffic_logs":
		s.cleanupExpiredTrafficLogs()
	case "check_expired_subscriptions":
		s.checkExpiredSubscriptions()
	case "perform_node_health_checks":
		s.performNodeHealthChecks()
	case "generate_daily_reports":
		s.generateDailyReports()
	default:
		return fmt.Errorf("未知的任务名称: %s", taskName)
	}
	return nil
}

// GetSystemCleanupStats 获取系统清理统计信息
func (s *TaskScheduler) GetSystemCleanupStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 获取调度器状态
	stats["scheduler"] = s.GetSchedulerStatus()

	// 获取流量系统统计
	trafficStats, err := s.trafficService.GetTrafficSystemStats()
	if err != nil {
		log.Printf("获取流量系统统计失败: %v", err)
	} else {
		stats["traffic_system"] = trafficStats
	}

	// 获取在线用户清理统计
	onlineUserStats, err := s.onlineUserService.GetOnlineUserCleanupStats()
	if err != nil {
		log.Printf("获取在线用户统计失败: %v", err)
	} else {
		stats["online_users"] = onlineUserStats
	}

	// 获取系统运行时间
	stats["system_uptime"] = time.Now().Format("2006-01-02 15:04:05")

	return stats, nil
}

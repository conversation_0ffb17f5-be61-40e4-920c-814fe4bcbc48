package database

import (
	"anypanel/internal/config"
	"anypanel/internal/model"
	"log"

	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

// InitDatabase 初始化数据库并执行迁移
func InitDatabase(cfg *config.Config) (*gorm.DB, *redis.Client, error) {
	// 初始化MySQL连接
	db, err := Init(cfg)
	if err != nil {
		return nil, nil, err
	}

	// 初始化Redis连接
	rdb, err := InitRedis(cfg)
	if err != nil {
		return nil, nil, err
	}

	// 执行数据库迁移
	if err := RunMigrations(db, cfg); err != nil {
		return nil, nil, err
	}

	log.Println("✅ 数据库初始化完成")
	return db, rdb, nil
}

// RunMigrations 执行数据库迁移
func RunMigrations(db *gorm.DB, cfg *config.Config) error {
	log.Println("🔄 开始执行数据库迁移...")

	// 执行自动迁移
	if err := model.AutoMigrate(db); err != nil {
		return err
	}

	// 创建索引
	if err := model.CreateIndexes(db); err != nil {
		return err
	}

	log.Println("✅ 数据库迁移完成")
	return nil
}

// CloseDatabase 关闭数据库连接
func CloseDatabase(db *gorm.DB, rdb *redis.Client) error {
	var err error
	
	// 关闭MySQL连接
	if db != nil {
		sqlDB, err := db.DB()
		if err != nil {
			return err
		}
		err = sqlDB.Close()
		if err != nil {
			return err
		}
	}
	
	// 关闭Redis连接
	if rdb != nil {
		err = rdb.Close()
		if err != nil {
			return err
		}
	}
	
	return nil
}
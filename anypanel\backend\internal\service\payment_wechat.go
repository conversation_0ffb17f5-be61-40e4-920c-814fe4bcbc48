package service

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/xml"
	"errors"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"
)

// WechatPayNativeGateway 微信支付Native网关
type WechatPayNativeGateway struct {
	config *WechatPayNativeConfig
}

// WechatPayNativeConfig 微信支付Native配置
type WechatPayNativeConfig struct {
	AppID  string `json:"app_id"`
	MchID  string `json:"mch_id"`
	APIKey string `json:"api_key"`
}

// WechatPayRequest 微信支付请求
type WechatPayRequest struct {
	AppID     string `xml:"appid"`
	MchID     string `xml:"mch_id"`
	NonceStr  string `xml:"nonce_str"`
	Sign      string `xml:"sign"`
	Body      string `xml:"body"`
	OutTradeNo string `xml:"out_trade_no"`
	TotalFee  int    `xml:"total_fee"`
	SpbillCreateIP string `xml:"spbill_create_ip"`
	NotifyURL string `xml:"notify_url"`
	TradeType string `xml:"trade_type"`
	ProductID string `xml:"product_id,omitempty"`
}

// WechatPayResponse 微信支付响应
type WechatPayResponse struct {
	ReturnCode string `xml:"return_code"`
	ReturnMsg  string `xml:"return_msg"`
	AppID      string `xml:"appid"`
	MchID      string `xml:"mch_id"`
	NonceStr   string `xml:"nonce_str"`
	Sign       string `xml:"sign"`
	ResultCode string `xml:"result_code"`
	ErrCode    string `xml:"err_code"`
	ErrCodeDes string `xml:"err_code_des"`
	PrepayID   string `xml:"prepay_id"`
	CodeURL    string `xml:"code_url"`
}

// WechatPayNotify 微信支付回调
type WechatPayNotify struct {
	ReturnCode     string `xml:"return_code"`
	ReturnMsg      string `xml:"return_msg"`
	AppID          string `xml:"appid"`
	MchID          string `xml:"mch_id"`
	NonceStr       string `xml:"nonce_str"`
	Sign           string `xml:"sign"`
	ResultCode     string `xml:"result_code"`
	ErrCode        string `xml:"err_code"`
	ErrCodeDes     string `xml:"err_code_des"`
	OpenID         string `xml:"openid"`
	IsSubscribe    string `xml:"is_subscribe"`
	TradeType      string `xml:"trade_type"`
	BankType       string `xml:"bank_type"`
	TotalFee       int    `xml:"total_fee"`
	CashFee        int    `xml:"cash_fee"`
	TransactionID  string `xml:"transaction_id"`
	OutTradeNo     string `xml:"out_trade_no"`
	TimeEnd        string `xml:"time_end"`
	TradeState     string `xml:"trade_state"`
	TradeStateDesc string `xml:"trade_state_desc"`
	FeeType        string `xml:"fee_type"`
	CashFeeType    string `xml:"cash_fee_type"`
}

// NewWechatPayNativeGateway 创建微信支付Native网关
func NewWechatPayNativeGateway(paymentConfig map[string]interface{}) *WechatPayNativeGateway {
	config := &WechatPayNativeConfig{
		AppID:  getString(paymentConfig, "app_id"),
		MchID:  getString(paymentConfig, "mch_id"),
		APIKey: getString(paymentConfig, "api_key"),
	}
	
	return &WechatPayNativeGateway{config: config}
}

// Form 获取支付配置表单
func (g *WechatPayNativeGateway) Form() map[string]FormField {
	return map[string]FormField{
		"app_id": {
			Label:       "APPID",
			Description: "绑定微信支付商户的APPID",
			Type:        "input",
			Required:    true,
		},
		"mch_id": {
			Label:       "商户号",
			Description: "微信支付商户号",
			Type:        "input",
			Required:    true,
		},
		"api_key": {
			Label:       "API密钥",
			Description: "微信支付API密钥(v1)",
			Type:        "input",
			Required:    true,
		},
	}
}

// Pay 发起支付
func (g *WechatPayNativeGateway) Pay(order PaymentOrder) (*PaymentResult, error) {
	if g.config.AppID == "" || g.config.MchID == "" || g.config.APIKey == "" {
		return nil, errors.New("微信支付配置不完整")
	}

	// 构建请求参数
	nonceStr := generateNonceStr()
	request := WechatPayRequest{
		AppID:          g.config.AppID,
		MchID:          g.config.MchID,
		NonceStr:       nonceStr,
		Body:           order.Subject,
		OutTradeNo:     order.OrderID,
		TotalFee:       int(order.TotalAmount * 100), // 转换为分
		SpbillCreateIP: "127.0.0.1", // 可以从请求中获取真实IP
		NotifyURL:      order.NotifyURL,
		TradeType:      "NATIVE",
		ProductID:      order.OrderID,
	}

	// 生成签名
	params := map[string]interface{}{
		"appid":            request.AppID,
		"mch_id":           request.MchID,
		"nonce_str":        request.NonceStr,
		"body":             request.Body,
		"out_trade_no":     request.OutTradeNo,
		"total_fee":        request.TotalFee,
		"spbill_create_ip": request.SpbillCreateIP,
		"notify_url":       request.NotifyURL,
		"trade_type":       request.TradeType,
		"product_id":       request.ProductID,
	}

	sign, err := g.generateSign(params)
	if err != nil {
		return nil, err
	}
	request.Sign = sign

	// 发送请求
	response, err := g.sendRequest(request)
	if err != nil {
		return nil, err
	}

	// 验证响应
	if response.ReturnCode != "SUCCESS" {
		return nil, fmt.Errorf("微信支付失败: %s", response.ReturnMsg)
	}

	if response.ResultCode != "SUCCESS" {
		return nil, fmt.Errorf("微信支付失败: %s - %s", response.ErrCode, response.ErrCodeDes)
	}

	// 验证响应签名
	if err := g.verifyResponse(response); err != nil {
		return nil, err
	}

	return &PaymentResult{
		Type:          0, // 二维码
		Data:          response.CodeURL,
		TransactionID: response.PrepayID,
		CustomResult:  "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>",
	}, nil
}

// Notify 处理支付回调
func (g *WechatPayNativeGateway) Notify(params map[string]interface{}) (*PaymentNotifyResult, error) {
	// 转换为结构体
	notify := &WechatPayNotify{}
	
	// 从params中提取值
	notify.ReturnCode = getString(params, "return_code")
	notify.ReturnMsg = getString(params, "return_msg")
	notify.AppID = getString(params, "appid")
	notify.MchID = getString(params, "mch_id")
	notify.NonceStr = getString(params, "nonce_str")
	notify.Sign = getString(params, "sign")
	notify.ResultCode = getString(params, "result_code")
	notify.ErrCode = getString(params, "err_code")
	notify.ErrCodeDes = getString(params, "err_code_des")
	notify.OpenID = getString(params, "openid")
	notify.IsSubscribe = getString(params, "is_subscribe")
	notify.TradeType = getString(params, "trade_type")
	notify.BankType = getString(params, "bank_type")
	notify.TotalFee = parseInt(getString(params, "total_fee"))
	notify.CashFee = parseInt(getString(params, "cash_fee"))
	notify.TransactionID = getString(params, "transaction_id")
	notify.OutTradeNo = getString(params, "out_trade_no")
	notify.TimeEnd = getString(params, "time_end")
	notify.TradeState = getString(params, "trade_state")
	notify.TradeStateDesc = getString(params, "trade_state_desc")
	notify.FeeType = getString(params, "fee_type")
	notify.CashFeeType = getString(params, "cash_fee_type")

	// 验证签名
	if err := g.verifyNotify(notify); err != nil {
		return nil, err
	}

	// 检查支付状态
	if notify.ReturnCode != "SUCCESS" || notify.ResultCode != "SUCCESS" {
		return nil, fmt.Errorf("支付失败: %s", notify.ReturnMsg)
	}

	return &PaymentNotifyResult{
		TradeNo:    notify.OutTradeNo,
		CallbackNo: notify.TransactionID,
		Amount:     fmt.Sprintf("%.2f", float64(notify.TotalFee)/100),
		Status:     "success",
	}, nil
}

// Refund 处理退款
func (g *WechatPayNativeGateway) Refund(refundOrder PaymentRefundOrder) (*PaymentRefundResult, error) {
	// TODO: 实现退款逻辑
	return &PaymentRefundResult{
		Success: false,
		Message: "退款功能暂未实现",
	}, nil
}

// 内部方法

// generateSign 生成签名
func (g *WechatPayNativeGateway) generateSign(params map[string]interface{}) (string, error) {
	// 构建待签名字符串
	var keys []string
	for k := range params {
		if params[k] != nil && params[k] != "" {
			keys = append(keys, k)
		}
	}

	// 排序
	sort.Strings(keys)

	// 构建查询字符串
	var queryBuilder strings.Builder
	for _, k := range keys {
		if queryBuilder.Len() > 0 {
			queryBuilder.WriteString("&")
		}
		queryBuilder.WriteString(k)
		queryBuilder.WriteString("=")
		queryBuilder.WriteString(fmt.Sprintf("%v", params[k]))
	}

	queryString := queryBuilder.String()
	queryString += "&key=" + g.config.APIKey

	// MD5签名
	hash := md5.Sum([]byte(queryString))
	sign := strings.ToUpper(hex.EncodeToString(hash[:]))

	return sign, nil
}

// verifySign 验证签名
func (g *WechatPayNativeGateway) verifySign(params map[string]interface{}, sign string) error {
	// 构建待签名字符串
	var keys []string
	for k := range params {
		if k != "sign" && params[k] != nil && params[k] != "" {
			keys = append(keys, k)
		}
	}

	// 排序
	sort.Strings(keys)

	// 构建查询字符串
	var queryBuilder strings.Builder
	for _, k := range keys {
		if queryBuilder.Len() > 0 {
			queryBuilder.WriteString("&")
		}
		queryBuilder.WriteString(k)
		queryBuilder.WriteString("=")
		queryBuilder.WriteString(fmt.Sprintf("%v", params[k]))
	}

	queryString := queryBuilder.String()
	queryString += "&key=" + g.config.APIKey

	// MD5签名
	hash := md5.Sum([]byte(queryString))
	calculatedSign := strings.ToUpper(hex.EncodeToString(hash[:]))

	if calculatedSign != sign {
		return errors.New("签名验证失败")
	}

	return nil
}

// verifyResponse 验证响应签名
func (g *WechatPayNativeGateway) verifyResponse(response *WechatPayResponse) error {
	// 构建验证参数
	params := map[string]interface{}{
		"return_code": response.ReturnCode,
		"return_msg":  response.ReturnMsg,
		"appid":       response.AppID,
		"mch_id":      response.MchID,
		"nonce_str":   response.NonceStr,
		"result_code": response.ResultCode,
	}

	if response.ErrCode != "" {
		params["err_code"] = response.ErrCode
	}
	if response.ErrCodeDes != "" {
		params["err_code_des"] = response.ErrCodeDes
	}
	if response.PrepayID != "" {
		params["prepay_id"] = response.PrepayID
	}
	if response.CodeURL != "" {
		params["code_url"] = response.CodeURL
	}

	return g.verifySign(params, response.Sign)
}

// verifyNotify 验证回调签名
func (g *WechatPayNativeGateway) verifyNotify(notify *WechatPayNotify) error {
	// 构建验证参数
	params := map[string]interface{}{
		"return_code":     notify.ReturnCode,
		"return_msg":      notify.ReturnMsg,
		"appid":           notify.AppID,
		"mch_id":          notify.MchID,
		"nonce_str":       notify.NonceStr,
		"result_code":     notify.ResultCode,
		"err_code":        notify.ErrCode,
		"err_code_des":    notify.ErrCodeDes,
		"openid":          notify.OpenID,
		"is_subscribe":    notify.IsSubscribe,
		"trade_type":      notify.TradeType,
		"bank_type":       notify.BankType,
		"total_fee":       notify.TotalFee,
		"cash_fee":        notify.CashFee,
		"transaction_id":  notify.TransactionID,
		"out_trade_no":    notify.OutTradeNo,
		"time_end":        notify.TimeEnd,
		"trade_state":     notify.TradeState,
		"trade_state_desc": notify.TradeStateDesc,
		"fee_type":        notify.FeeType,
		"cash_fee_type":    notify.CashFeeType,
	}

	return g.verifySign(params, notify.Sign)
}

// sendRequest 发送请求
func (g *WechatPayNativeGateway) sendRequest(request WechatPayRequest) (*WechatPayResponse, error) {
	// 序列化请求
	xmlData, err := xml.Marshal(request)
	if err != nil {
		return nil, err
	}

	// 发送请求
	resp, err := http.Post("https://api.mch.weixin.qq.com/pay/unifiedorder", "application/xml", strings.NewReader(string(xmlData)))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var response WechatPayResponse
	if err := xml.Unmarshal(body, &response); err != nil {
		return nil, err
	}

	return &response, nil
}

// generateNonceStr 生成随机字符串
func generateNonceStr() string {
	return fmt.Sprintf("%d", time.Now().UnixNano())
}

// parseInt 字符串转整数
func parseInt(s string) int {
	if s == "" {
		return 0
	}
	i, _ := strconv.Atoi(s)
	return i
}
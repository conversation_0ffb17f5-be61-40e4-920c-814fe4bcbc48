package model

import (
	"encoding/json"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// SystemConfig 系统配置模型
type SystemConfig struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	ConfigKey   string         `json:"config_key" gorm:"uniqueIndex;size:100"`
	ConfigName  string         `json:"config_name" gorm:"size:200"`
	ConfigType  string         `json:"config_type" gorm:"size:50;default:'object'"` // object, string, number, boolean
	ConfigValue datatypes.JSON `json:"config_value"`
	Description string         `json:"description" gorm:"type:text"`
	Category    string         `json:"category" gorm:"size:50"`
	IsEncrypted bool           `json:"is_encrypted" gorm:"default:false"`
	IsPublic    bool           `json:"is_public" gorm:"default:false"` // 是否允许前端读取
	SortOrder   int            `json:"sort_order" gorm:"default:0"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (SystemConfig) TableName() string {
	return "system_configs"
}

// GetStringValue 获取字符串值
func (sc *SystemConfig) GetStringValue() string {
	var value string
	if err := json.Unmarshal(sc.ConfigValue, &value); err != nil {
		return ""
	}
	return value
}

// GetBoolValue 获取布尔值
func (sc *SystemConfig) GetBoolValue() bool {
	var value bool
	if err := json.Unmarshal(sc.ConfigValue, &value); err != nil {
		return false
	}
	return value
}

// GetIntValue 获取整数值
func (sc *SystemConfig) GetIntValue() int {
	var value int
	if err := json.Unmarshal(sc.ConfigValue, &value); err != nil {
		return 0
	}
	return value
}

// GetFloat64Value 获取浮点数值
func (sc *SystemConfig) GetFloat64Value() float64 {
	var value float64
	if err := json.Unmarshal(sc.ConfigValue, &value); err != nil {
		return 0
	}
	return value
}

// GetObjectValue 获取对象值
func (sc *SystemConfig) GetObjectValue(target interface{}) error {
	return json.Unmarshal(sc.ConfigValue, target)
}

// SetValue 设置配置值
func (sc *SystemConfig) SetValue(value interface{}) error {
	data, err := json.Marshal(value)
	if err != nil {
		return err
	}
	sc.ConfigValue = data
	return nil
}
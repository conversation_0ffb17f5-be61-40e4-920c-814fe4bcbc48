package utils

import (
	"anypanel/internal/model"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"runtime"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// LogLevel 日志级别
type LogLevel string

const (
	DEBUG LogLevel = "debug"
	INFO  LogLevel = "info"
	WARN  LogLevel = "warn"
	ERROR LogLevel = "error"
	FATAL LogLevel = "fatal"
)

// Logger 结构化日志记录器
type Logger struct {
	db        *gorm.DB
	component string
	output    io.Writer
	level     LogLevel
	context   map[string]interface{}
}

// LogEntry 日志条目
type LogEntry struct {
	Level     LogLevel               `json:"level"`
	Timestamp time.Time              `json:"timestamp"`
	Component string                 `json:"component"`
	Message   string                 `json:"message"`
	Context   map[string]interface{} `json:"context,omitempty"`
	TraceID   string                 `json:"trace_id,omitempty"`
	UserID    uint                   `json:"user_id,omitempty"`
	IPAddress string                 `json:"ip_address,omitempty"`
	UserAgent string                 `json:"user_agent,omitempty"`
	File      string                 `json:"file,omitempty"`
	Line      int                    `json:"line,omitempty"`
	Function  string                 `json:"function,omitempty"`
}

// NewLogger 创建新的日志记录器
func NewLogger(db *gorm.DB, component string) *Logger {
	return &Logger{
		db:        db,
		component: component,
		output:    os.Stdout,
		level:     INFO,
		context:   make(map[string]interface{}),
	}
}

// SetLevel 设置日志级别
func (l *Logger) SetLevel(level LogLevel) *Logger {
	l.level = level
	return l
}

// SetOutput 设置输出
func (l *Logger) SetOutput(output io.Writer) *Logger {
	l.output = output
	return l
}

// WithContext 添加上下文信息
func (l *Logger) WithContext(key string, value interface{}) *Logger {
	newLogger := &Logger{
		db:        l.db,
		component: l.component,
		output:    l.output,
		level:     l.level,
		context:   make(map[string]interface{}),
	}
	
	// 复制现有上下文
	for k, v := range l.context {
		newLogger.context[k] = v
	}
	
	// 添加新的上下文
	newLogger.context[key] = value
	
	return newLogger
}

// WithUser 添加用户信息
func (l *Logger) WithUser(userID uint) *Logger {
	return l.WithContext("user_id", userID)
}

// WithRequest 添加请求信息
func (l *Logger) WithRequest(traceID, ipAddress, userAgent string) *Logger {
	return l.WithContext("trace_id", traceID).
		WithContext("ip_address", ipAddress).
		WithContext("user_agent", userAgent)
}

// Debug 记录调试日志
func (l *Logger) Debug(message string, args ...interface{}) {
	if l.shouldLog(DEBUG) {
		l.log(DEBUG, fmt.Sprintf(message, args...))
	}
}

// Info 记录信息日志
func (l *Logger) Info(message string, args ...interface{}) {
	if l.shouldLog(INFO) {
		l.log(INFO, fmt.Sprintf(message, args...))
	}
}

// Warn 记录警告日志
func (l *Logger) Warn(message string, args ...interface{}) {
	if l.shouldLog(WARN) {
		l.log(WARN, fmt.Sprintf(message, args...))
	}
}

// Error 记录错误日志
func (l *Logger) Error(message string, args ...interface{}) {
	if l.shouldLog(ERROR) {
		l.log(ERROR, fmt.Sprintf(message, args...))
	}
}

// Fatal 记录致命错误日志
func (l *Logger) Fatal(message string, args ...interface{}) {
	if l.shouldLog(FATAL) {
		l.log(FATAL, fmt.Sprintf(message, args...))
		os.Exit(1)
	}
}

// shouldLog 检查是否应该记录日志
func (l *Logger) shouldLog(level LogLevel) bool {
	levels := map[LogLevel]int{
		DEBUG: 0,
		INFO:  1,
		WARN:  2,
		ERROR: 3,
		FATAL: 4,
	}
	
	return levels[level] >= levels[l.level]
}

// log 记录日志
func (l *Logger) log(level LogLevel, message string) {
	entry := l.createLogEntry(level, message)
	
	// 输出到控制台
	l.outputToConsole(entry)
	
	// 异步保存到数据库
	go l.saveToDatabase(entry)
}

// createLogEntry 创建日志条目
func (l *Logger) createLogEntry(level LogLevel, message string) *LogEntry {
	entry := &LogEntry{
		Level:     level,
		Timestamp: time.Now(),
		Component: l.component,
		Message:   message,
		Context:   make(map[string]interface{}),
	}
	
	// 复制上下文
	for k, v := range l.context {
		entry.Context[k] = v
	}
	
	// 提取特殊字段
	if traceID, ok := entry.Context["trace_id"].(string); ok {
		entry.TraceID = traceID
		delete(entry.Context, "trace_id")
	}
	
	if userID, ok := entry.Context["user_id"].(uint); ok {
		entry.UserID = userID
		delete(entry.Context, "user_id")
	}
	
	if ipAddress, ok := entry.Context["ip_address"].(string); ok {
		entry.IPAddress = ipAddress
		delete(entry.Context, "ip_address")
	}
	
	if userAgent, ok := entry.Context["user_agent"].(string); ok {
		entry.UserAgent = userAgent
		delete(entry.Context, "user_agent")
	}
	
	// 获取调用者信息
	if pc, file, line, ok := runtime.Caller(3); ok {
		entry.File = file
		entry.Line = line
		if fn := runtime.FuncForPC(pc); fn != nil {
			entry.Function = fn.Name()
		}
	}
	
	return entry
}

// outputToConsole 输出到控制台
func (l *Logger) outputToConsole(entry *LogEntry) {
	jsonData, _ := json.Marshal(entry)
	fmt.Fprintln(l.output, string(jsonData))
}

// saveToDatabase 保存到数据库
func (l *Logger) saveToDatabase(entry *LogEntry) {
	if l.db == nil {
		return
	}
	
	contextJSON, _ := json.Marshal(entry.Context)
	
	systemLog := &model.SystemLog{
		Level:     string(entry.Level),
		Component: entry.Component,
		Message:   entry.Message,
		Context:   contextJSON,
		TraceID:   entry.TraceID,
		UserID:    entry.UserID,
		IPAddress: entry.IPAddress,
		UserAgent: entry.UserAgent,
		CreatedAt: entry.Timestamp,
	}
	
	// 使用独立的数据库连接，避免影响主业务
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	l.db.WithContext(ctx).Create(systemLog)
}

// LogRotator 日志轮转器
type LogRotator struct {
	db           *gorm.DB
	retentionDays int
	ticker       *time.Ticker
	done         chan bool
}

// NewLogRotator 创建日志轮转器
func NewLogRotator(db *gorm.DB, retentionDays int) *LogRotator {
	return &LogRotator{
		db:           db,
		retentionDays: retentionDays,
		done:         make(chan bool),
	}
}

// Start 启动日志轮转
func (r *LogRotator) Start() {
	r.ticker = time.NewTicker(24 * time.Hour) // 每天执行一次
	
	go func() {
		for {
			select {
			case <-r.ticker.C:
				r.cleanup()
			case <-r.done:
				return
			}
		}
	}()
}

// Stop 停止日志轮转
func (r *LogRotator) Stop() {
	if r.ticker != nil {
		r.ticker.Stop()
	}
	r.done <- true
}

// cleanup 清理旧日志
func (r *LogRotator) cleanup() {
	cutoff := time.Now().AddDate(0, 0, -r.retentionDays)
	
	result := r.db.Where("created_at < ?", cutoff).Delete(&model.SystemLog{})
	if result.Error != nil {
		fmt.Printf("日志清理失败: %v\n", result.Error)
	} else {
		fmt.Printf("清理了 %d 条日志记录\n", result.RowsAffected)
	}
}

// 全局日志记录器实例
var (
	defaultLogger *Logger
	systemLogger  *Logger
	apiLogger     *Logger
	securityLogger *Logger
)

// InitLoggers 初始化日志记录器
func InitLoggers(db *gorm.DB) {
	defaultLogger = NewLogger(db, "system").SetLevel(INFO)
	systemLogger = NewLogger(db, "system").SetLevel(INFO)
	apiLogger = NewLogger(db, "api").SetLevel(INFO)
	securityLogger = NewLogger(db, "security").SetLevel(WARN)
}

// GetLogger 获取指定组件的日志记录器
func GetLogger(component string) *Logger {
	switch component {
	case "api":
		return apiLogger
	case "security":
		return securityLogger
	case "system":
		return systemLogger
	default:
		return defaultLogger
	}
}

// 便捷方法
func LogInfo(component, message string, args ...interface{}) {
	GetLogger(component).Info(message, args...)
}

func LogWarn(component, message string, args ...interface{}) {
	GetLogger(component).Warn(message, args...)
}

func LogError(component, message string, args ...interface{}) {
	GetLogger(component).Error(message, args...)
}

func LogDebug(component, message string, args ...interface{}) {
	GetLogger(component).Debug(message, args...)
}

// RequestLogger 请求日志中间件
func RequestLoggerMiddleware(db *gorm.DB) func(c *gin.Context) {
	logger := NewLogger(db, "api")
	
	return func(c *gin.Context) {
		start := time.Now()
		
		// 生成请求ID
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = fmt.Sprintf("req_%d_%s", time.Now().UnixNano(), generateRandomString(8))
		}
		
		// 设置请求上下文
		c.Set("request_id", requestID)
		c.Set("start_time", start)
		
		// 创建请求日志记录器
		reqLogger := logger.WithRequest(requestID, c.ClientIP(), c.GetHeader("User-Agent"))
		
		// 记录请求开始
		reqLogger.Info("请求开始",
			"method", c.Request.Method,
			"path", c.Request.URL.Path,
			"query", c.Request.URL.RawQuery,
		)
		
		// 执行请求
		c.Next()
		
		// 记录请求结束
		duration := time.Since(start)
		status := c.Writer.Status()
		
		logLevel := INFO
		if status >= 400 {
			logLevel = WARN
		}
		if status >= 500 {
			logLevel = ERROR
		}
		
		reqLogger.WithContext("duration", duration).
			WithContext("status", status).
			WithContext("response_size", c.Writer.Size()).
			log(logLevel, "请求完成")
	}
}

// generateRandomString 生成随机字符串
func generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}